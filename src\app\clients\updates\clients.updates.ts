import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ClientEntity } from '../entities/client.entity';
import { tokenAssignGigstack } from '@/lib/getTokenByRegion';
import { TEST_TOKEN } from '@/constants';
import { ClientAPI } from '../client-types';
import { RegionsType } from '@/types/entities.types';
import { CreateClientDto } from '../dto/create-client.dto';

@Injectable()
export class ClientsUpdater {

  constructor(
    @InjectRepository(ClientEntity)
    private readonly clientRepository: Repository<ClientEntity>,
  ) { }

  private readonly baseurl = "https://gigstack-cfdi-bjekv7t4.uc.gateway.dev/v1";

  async createClient(data: CreateClientDto) {
    const client = new ClientEntity();
    const id = client.id;
    client.name = '<PERSON>';
    client.email = '<EMAIL>';
    console.log(client);
    await this.clientRepository.save(data);

  }

  async getGigstackUsers(region: RegionsType) {

    const token = tokenAssignGigstack(region);

    console.log('INICIANDO REQUESTS A GIGSTACK PARA')

    try {

      const res = await fetch(`${this.baseurl}/clients/list`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token || TEST_TOKEN}`,
        },
      });

      const data = await res.json();
      const clients: ClientAPI[] = data.clients;
      console.log('clients', clients.length);
      // console.log('CLIENTS DATA', clients)

      let totalCreated = 0;
      let errorCreated = 0;
      let totalUpdated = 0;

      const errorClients = [];

      for (const client of clients) {
        // console.log('client name', client.name);
        // THIS LOGS DOESN'T AFFECT THE CODE, THIS IS JUST FOR LOCAL CHANGES 
        console.log('legal name', client.legal_name);
        console.log('name', client.name, client.company, client.id);
        console.log('----------------')

        // continue
        // break;
        if (!client.metadata) continue;

        const cliendDb = await this.clientRepository.findOne({
          where: {
            gigId: client.id
          }
        });

        const metadataParsed = JSON.parse(client.metadata);

        const monexClabe = metadataParsed.clabe;

        const completeName = client.name || client.legal_name;
        if (!completeName) {
          continue;
        }
        const names = completeName?.split(' ');
        console.log('names of client', names);
        let name = '';
        let lastName = '';
        if (names.length === 1) {
          name = names[0]
        }
        if (names.length % 2 === 0) {
          name = names.slice(0, names.length / 2).join(' ');
          lastName = names.slice(names.length / 2).join(' ');
        }
        if (names.length % 3 === 0) {
          name = names.slice(0, 1).join(' ');
          lastName = names.slice(1).join(' ');
        }
        if (names.length % 5 === 0) {
          name = names.slice(0, 1).join(' ');
          lastName = names.slice(1).join(' ');
          if (names.includes('JOSE')) {
            name = names.slice(0, 3).join(' ');
            lastName = names.slice(3).join(' ');
          }
        }
        if (names.length === 6) {
          name = names.slice(0, 2).join(' ');
          lastName = names.slice(2).join(' ');
        }

        const legal_name = client.legal_name || (name + ' ' + lastName).toUpperCase();
        const tax_system = typeof client.tax_system === 'string' ? client.tax_system : client.tax_system?.value;

        const data = {
          rfc: client.rfc,
          name,
          lastName,
          email: client.email,
          phone: client.phone,
          street: client.address.address || '',
          legal_name: legal_name,
          // fiscalRegime: client.tax_system || '',
          monexClabe,
          zip: client.address.zip?.toString(),
          contractNumber: client.company,
          tax_system,
          // cfdiUsage: 
          gigId: client.id,
          country: 'MX',
          isActive: monexClabe ? true : false,
          region,
          use_cfdi: client.use || 'G03',
        }



        try {
          console.log('CLIENT INFO: ', client.name, client.email, client.company)
          if (cliendDb) {
            console.log(`Client: ${client.name.toLowerCase()} ALREADY EXISTS ${client.company} ${client.email}`, /* client.metadata */);

            if (!cliendDb.contractNumber) {
              await this.clientRepository.update(cliendDb.id, { contractNumber: client.company });
            }
            if (!cliendDb.monexClabe && monexClabe) {
              await this.clientRepository.update(cliendDb.id, { monexClabe });

            } 

            totalUpdated++;
            await this.clientRepository.update(cliendDb.id, data);

            // continue;
          } else {

            if (monexClabe && client.company) {

              // await this.clientRepository.save(data);
              await this.createClient(data);
              console.log(`Client ${client.name.toLowerCase()} created, ${client.company} ${client.email}`);
              totalCreated++;
            }
            else console.log(`This gigstack client ${client.name} ${client.company} ${client.email} doesn't have a monex clabe`);
          }
        } catch (error) {
          console.log(`Error creating client ${client.name}`, error);
          errorClients.push(client);
          errorCreated++;
        }

      }

      console.log('Total created', totalCreated);
      console.log('Total errors', errorCreated);
      console.log('Total updated', totalUpdated);
      for (const client of errorClients) {
        console.log('Error client', client);
      }
      return data;
    } catch (error) {
      console.log('Hubo un error', error);
    }
  }

  async cleanClients() {

    const clients = await this.clientRepository.find();

    for (const client of clients) {
      if (!client.monexClabe || !client.contractNumber) {
        await this.clientRepository.delete(client.id);
      }
    }


  }

}
