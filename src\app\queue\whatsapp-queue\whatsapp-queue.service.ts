import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { JOB_SEND_WHATSAPP, WHATSAPP_QUEUE } from '@/constants';

@Injectable()
export class WhatsappQueueService {
  private readonly logger = new Logger(WhatsappQueueService.name);
  constructor(@InjectQueue(WHATSAPP_QUEUE) private whatsappQueue: Queue) {}

  async sendWhatsapp(data: any) {
    this.logger.log({
      message: `[WhatsappQueueService] - Whatsapp added to queue for sending: ${data}`
    });
    const job = await this.whatsappQueue.add(JOB_SEND_WHATSAPP, data);
    return job.id; // Return the job ID for tracking
  }
}
