import { Body, Controller, Injectable, Logger, Post, Res } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { SchedulesService } from '../services/schedules.service';
import { tryCatchResponse } from '@/lib/defaultResponse';
import { Response } from 'express';

@ApiTags('schedules')
@Controller('schedules')
export class SchedulesController {

  readonly logger = new Logger(SchedulesController.name);
  constructor(
    private readonly schedulesService: SchedulesService,
  ) { }

  @Post('invoice-receipts')
  async invoiceReceipts(@Res() res: Response, @Body() body: { isTest?: boolean }) {

    this.logger.log('CREATING INVOICE RECEIPTS');
    return await tryCatchResponse(res, () => this.schedulesService.invoiceReceipts(body));
  }

}