import { Module } from '@nestjs/common';
import { SubscriptionsController } from './controllers/subscriptions.controller';
import { SubscriptionsService } from './services/susbscriptions.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SubscriptionEntity } from './entities/subscription.entity';
import { ProductsModule } from '../products/products.module';
import { ClientsModule } from '../clients/clients.module';
import { SubscriptionProductsModule } from '../subscription-products/subscription-products.module';
import { PaymentsModule } from '../payments/payments.module';
import { CreateSubscriptionPayments } from './services/create-payments.service';
import { PaymentsEntity } from '../payments/entities/payment.entity';
import { ClientEntity } from '../clients/entities/client.entity';
import { SubscriptionsUpdatesController } from './controllers/subscriptions-update.controller';
import { SubscriptionsUpdateService } from './services/subscriptions-update.service';
import { SubscriptionProductEntity } from '../subscription-products/entities/subscription-product.entity';
import { SubscriptionSubscriber } from './events/subscription.subscriber';
import { NotificationModule } from '../notification/notification.module';
import { StripePaymentModule } from '../stripe-payment/stripe-payment.module';
import { WeeklyTracking } from './entities/weekly-tracking.entity';
import { WeeklyRecord } from './entities/weekly-record.entity';
import { PaymentQueueService } from '../queue/payment-queue/payment-queue.service';
import { PaymentQueueModule } from '../queue/payment-queue/payment-queue.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([SubscriptionEntity, PaymentsEntity, ClientEntity, SubscriptionProductEntity, WeeklyTracking, WeeklyRecord]),
    ProductsModule,
    ClientsModule,
    SubscriptionProductsModule,
    PaymentsModule,
    NotificationModule,
    StripePaymentModule,
    PaymentQueueModule,
  ],
  controllers: [SubscriptionsController, SubscriptionsUpdatesController],
  providers: [SubscriptionsService, CreateSubscriptionPayments, SubscriptionsUpdateService, SubscriptionSubscriber],
  exports: [SubscriptionsService, SubscriptionsUpdateService],
})
export class SubscriptionsModule { }
