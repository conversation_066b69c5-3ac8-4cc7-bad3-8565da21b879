import { TaxSystem } from './tax_system.types';

// Define el enum para las claves
export enum CFDI_KEYS_ENUM {
  G01 = "G01",
  G02 = "G02",
  G03 = "G03",
  I01 = "I01",
  I02 = "I02",
  I03 = "I03",
  I04 = "I04",
  I05 = "I05",
  I06 = "I06",
  I07 = "I07",
  I08 = "I08",
  D01 = "D01",
  D02 = "D02",
  D03 = "D03",
  D04 = "D04",
  D05 = "D05",
  D06 = "D06",
  D07 = "D07",
  D08 = "D08",
  D09 = "D09",
  D10 = "D10",
  S01 = "S01",
  CP01 = "CP01",
  CN01 = "CN01"
}

export type CFDI_KEYS_TYPE = keyof typeof CFDI_KEYS_ENUM;


// Define el tipo para la información de cada fila
type CFDI_DATA_TYPE = {
  description: string;
  taxSystem: TaxSystem[];
};

// Define el tipo para el objeto que agrupa la información por clave
type InformacionAgrupada = {
  [key in CFDI_KEYS_ENUM]: CFDI_DATA_TYPE;
};

// Objeto que contiene la información agrupada
export const CFDI_DATA: InformacionAgrupada = {
  G01: {
    description: "Adquisición de mercancías",
    taxSystem: ["601", "603", "606", "612", "620", "621", "622", "623", "624", "625", "626"]
  },
  G02: {
    description: "Devoluciones, descuentos o bonificaciones",
    taxSystem: ["601", "603", "606", "612", "620", "621", "622", "623", "624", "625", "626"]
  },
  G03: {
    description: "Gastos en general",
    taxSystem: ["601", "603", "606", "612", "620", "621", "622", "623", "624", "625", "626"]
  },
  I01: {
    description: "Construcciones",
    taxSystem: ["601", "603", "606", "612", "620", "621", "622", "623", "624", "625", "626"]
  },
  I02: {
    description: "Mobiliario y equipo de oficina por inversiones",
    taxSystem: ["601", "603", "606", "612", "620", "621", "622", "623", "624", "625", "626"]
  },
  I03: {
    description: "Equipo de transporte",
    taxSystem: ["601", "603", "606", "612", "620", "621", "622", "623", "624", "625", "626"]
  },
  I04: {
    description: "Equipo de computo y accesorios",
    taxSystem: ["601", "603", "606", "612", "620", "621", "622", "623", "624", "625", "626"]
  },
  I05: {
    description: "Dados, troqueles, moldes, matrices y herramental",
    taxSystem: ["601", "603", "606", "612", "620", "621", "622", "623", "624", "625", "626"]
  },
  I06: {
    description: "Comunicaciones telefónicas",
    taxSystem: ["601", "603", "606", "612", "620", "621", "622", "623", "624", "625", "626"]
  },
  I07: {
    description: "Comunicaciones satelitales",
    taxSystem: ["601", "603", "606", "612", "620", "621", "622", "623", "624", "625", "626"]
  },
  I08: {
    description: "Otra maquinaria y equipo",
    taxSystem: ["601", "603", "606", "612", "620", "621", "622", "623", "624", "625", "626"]
  },
  D01: {
    description: "Honorarios médicos, dentales y gastos hospitalarios",
    taxSystem: ["605", "606", "608", "611", "612", "614", "607", "615", "625"]
  },
  D02: {
    description: "Gastos médicos por incapacidad o discapacidad",
    taxSystem: ["605", "606", "608", "611", "612", "614", "607", "615", "625"]
  },
  D03: {
    description: "Gastos funerales",
    taxSystem: ["605", "606", "608", "611", "612", "614", "607", "615", "625"]
  },
  D04: {
    description: "Donativos",
    taxSystem: ["605", "606", "608", "611", "612", "614", "607", "615", "625"]
  },
  D05: {
    description: "Intereses reales efectivamente pagados por créditos hipotecarios (casa habitación)",
    taxSystem: ["605", "606", "608", "611", "612", "614", "607", "615", "625"]
  },
  D06: {
    description: "Aportaciones voluntarias al SAR",
    taxSystem: ["605", "606", "608", "611", "612", "614", "607", "615", "625"]
  },
  D07: {
    description: "Primas por seguros de gastos médicos",
    taxSystem: ["605", "606", "608", "611", "612", "614", "607", "615", "625"]
  },
  D08: {
    description: "Gastos de transportación escolar obligatoria",
    taxSystem: ["605", "606", "608", "611", "612", "614", "607", "615", "625"]
  },
  D09: {
    description: "Depósitos en cuentas para el ahorro, primas que tengan como base planes de pensiones",
    taxSystem: ["605", "606", "608", "611", "612", "614", "607", "615", "625"]
  },
  D10: {
    description: "Pagos por servicios educativos (colegiaturas)",
    taxSystem: ["605", "606", "608", "611", "612", "614", "607", "615", "625"]
  },
  S01: {
    description: "Sin efectos fiscales",
    taxSystem: ["601", "603", "605", "606", "608", "610", "611", "612", "614", "616", "620", "621", "622", "623", "624", "607", "615", "625", "626"]
  },
  CP01: {
    description: "Pagos",
    taxSystem: ["601", "603", "605", "606", "608", "610", "611", "612", "614", "616", "620", "621", "622", "623", "624", "607", "615", "625", "626"]
  },
  CN01: {
    description: "Nómina",
    taxSystem: ["605"]
  }
};



export const G03_LIST = ["601", "603", "606", "612", "620", "621", "622", "623", "624", "625", "626"];
