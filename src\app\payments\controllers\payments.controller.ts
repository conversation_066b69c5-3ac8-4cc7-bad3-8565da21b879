import { Controller, Get, Post, Body, Patch, Param, Delete, Res, Query, Headers } from '@nestjs/common';
import { PaymentsService } from '../services/payments.service';
import { CreateIsolatedPayment, CreatePaymentDto, UnlockPaymentPlateDto, UnlockPaymentDto, Validate2LastPaymentsDto, SoldPaymentDto } from '../dto/create-payment.dto';
import { UpdatePaymentDto } from '../dto/update-payment.dto';
import { tryCatchResponse } from '@/lib/defaultResponse';
import { Response } from 'express';
import { Wire4WebHookService } from '../services/wire4Webhook.service';
import { FindQueryPaymentDto } from '../dto/find-query-payment.dto';
import {UnlockPaymentsService} from '../services/unlock-payments.service';
import {BlockPaymentsService} from '../services/block-payments.service';

@Controller('payments')
export class PaymentsController {
  constructor(private readonly paymentsService: PaymentsService,
    private readonly wire4Webhook: Wire4WebHookService,
    private readonly unlockPaymentsService: UnlockPaymentsService,
    private readonly blockPaymentsService: BlockPaymentsService
  ) { }

  @Post()
  async create(@Body() createPaymentDto: CreatePaymentDto, @Res() res: Response) {

    return await tryCatchResponse(res, async () => this.paymentsService.create(createPaymentDto));

  }

  /**
   * Create a payment without a subscription
   */
  @Post('isolated')
  async createIsolatedPayment(@Body() createPaymentDto: CreateIsolatedPayment, @Res() res: Response) {

    return await tryCatchResponse(res, async () => this.paymentsService.createIsolatedPayment(createPaymentDto));
  }

  @Post('unlock-payment')
  async unlockPayment(@Body() createPaymentDto: UnlockPaymentDto, @Res() res: Response) {

    return await tryCatchResponse(res, async () => this.paymentsService.unlockPayment(createPaymentDto));

  }

  @Post('sold-payment')
  async soldPayment(@Body() createPaymentDto: SoldPaymentDto, @Res() res: Response) {

    return await tryCatchResponse(res, async () => this.paymentsService.soldPayment(createPaymentDto));

  }

  @Post('unlock-payment-by-plates')
  async UnlockPaymentPlate(@Body() createPaymentDto: UnlockPaymentPlateDto, @Res() res: Response) {

    return await tryCatchResponse(res, async () => this.paymentsService.unlockPaymentByPlates(createPaymentDto));

  }

  @Post('validate-two-last-payments')
  async validateTwoLastPayments(@Body() createPaymentDto: Validate2LastPaymentsDto, @Res() res: Response) {
    return await tryCatchResponse(res, async () => this.paymentsService.validate2LastPayments(createPaymentDto));
  }

  @Get()
  async findAll(@Res() res: Response, @Query() query?: FindQueryPaymentDto) {
    return await tryCatchResponse(res, async () => this.paymentsService.findAll(query));
  }

  @Get(':id')
  async findOne(@Param('id') id: string, @Res() res: Response, @Headers('x-origin-url') origin: string) {

    return await tryCatchResponse(res, async () => this.paymentsService.findOne(id));
  }

  @Patch(':id')
  async update(@Param('id') id: string, @Body() updatePaymentDto: UpdatePaymentDto, @Res() res: Response) {
    return await tryCatchResponse(res, async () => this.paymentsService.update(id, updatePaymentDto));
  }

  @Patch(':id/cancel')
  async cancel(@Param('id') id: string, @Res() res: Response, @Body() body: any) {
    return await tryCatchResponse(res, async () => this.paymentsService.cancel(id, body));
  }

  @Delete(':id')
  async remove(@Param('id') id: string, @Res() res: Response, @Query('completeDelete') completeDelete: string) {
    return await tryCatchResponse(res, async () => this.paymentsService.remove(id, completeDelete === 'true'));

  }

  @Delete(':id/remove-temporal-products')
  async removeTemporalProducts(@Param('id') id: string, @Res() res: Response) {
    return await tryCatchResponse(res, async () => this.paymentsService.removeTemporalProducts(id));
  }

  @Patch(':id/retry-invoice')
  async retryInvoice(@Param('id') id: string, @Res() res: Response) {
    return await tryCatchResponse(res, async () => this.paymentsService.retryInvoice(id));
  }

  @Get('/associateId/:associateId')
  async findByAssociateId(@Param('associateId') associateId: string, @Res() res: Response, @Headers('x-origin-url') origin: string) {
    return await tryCatchResponse(res, async () => this.paymentsService.findByAssociateId(associateId));
  }

  //this is an internal function only for testing
  @Get('create-unlock-payment/:type')
  async CreateUnlockPayment(@Param('type') type: string, @Res() res: Response) {
    return await tryCatchResponse(res, async () => this.unlockPaymentsService.createUnlockPaymentTest(type));
  }

  //this is an internal function only for testing block payments
  @Get('test-block-payments')
  async testBlockPayments(@Res() res: Response) {
    return await tryCatchResponse(res, async () => this.blockPaymentsService.processBlockPayments());
  }
}
