import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';

const INVOICE_EMAIL_QUEUE = 'invoice-email-queue';
const JOB_SEND_INVOICE_EMAIL = 'send-invoice-email';

interface InvoiceEmailData {
  invoiceId: string;
  paymentId: string;
  clientEmail: string;
  clientName: string;
}

@Injectable()
export class InvoiceEmailQueueService {
  private readonly logger = new Logger(InvoiceEmailQueueService.name);
  
  constructor(@InjectQueue(INVOICE_EMAIL_QUEUE) private invoiceEmailQueue: Queue) {}

  async sendInvoiceEmail(data: InvoiceEmailData) {
    this.logger.log({
      message: `[InvoiceEmailQueueService] - Añadiendo factura a la cola para envío de correo: ${data.invoiceId}`,
    });
    const job = await this.invoiceEmailQueue.add(JO<PERSON>_SEND_INVOICE_EMAIL, data);
    return job.id;
  }
}