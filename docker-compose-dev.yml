version: '3.8'

name: payments

services:
  db:
    image: supabase/postgres:15.6.1.106
    ports:
      - "5432:5432"
      - "5433:5433"
    command: postgres -c config_file=/etc/postgresql/postgresql.conf 
    environment:
      POSTGRES_PASSWORD: postgres
    volumes:
      - postgres_data:/var/lib/postgresql/data


  redis:
    image: redis:latest
    container_name: redis
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
    restart: unless-stopped
    volumes:
      - redis-data:/data
    networks:
      - app-network

  app:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: payments
    env_file:
      - .env
    ports:
      - "${PORT}:${PORT}"
    depends_on:
      redis:
        condition: service_healthy

    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:${PORT}/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    volumes:
      - ./logs:/var/log/app
      - ./:/myapp
    networks:
      - app-network

volumes:
  redis-data:
  postgres_data:

networks:
  app-network:
    driver: bridge