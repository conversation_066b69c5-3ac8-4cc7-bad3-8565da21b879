{
  "compilerOptions": {
    "module": "commonjs",
    "declaration": true,
    "removeComments": true,
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "allowSyntheticDefaultImports": true,
    "target": "ES2021",
    "jsx": "react",
    "sourceMap": true,
    "resolveJsonModule": true,
    "esModuleInterop": true,
    "outDir": "./build",
    "baseUrl": "./",
    "incremental": true,
    "skipLibCheck": true,
    "strict": true,
    "strictNullChecks": false,
    "noImplicitAny": false,
    "strictBindCallApply": false,
    "forceConsistentCasingInFileNames": false,
    "noFallthroughCasesInSwitch": false,
    "paths": {
      "@/*": ["src/*"],
      "@facturapi": ["src/facturapi/index"],
      "@constants/*": ["src/constants/*"],
      "@lib/*": ["src/lib/*"],
      "@app/*": ["src/app/*"],
      "@postgres/*": ["src/db/postgres/*"],
      "@mongo/*": ["src/db/mongo/*"],
    },
  },
  "exclude": ["node_modules"],
  "include": [
    "src/**/*.ts",
    "src/**/*.tsx",
    "uploads/**/*",
  ]
}
