import {
  NODE_ENV,
} from '@/constants';
import { Injectable, Logger } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, LessThanOrEqual } from 'typeorm';
import {
  PaymentsEntity,
  PaymentStatusEnum,
} from '../entities/payment.entity';
import { StockVehicleService } from './stock.service';
import { GpsService } from './gps.service';
import { slackApi } from '@/logger.config';

const MX_CLIENTS_BLOCK_PAYMENTS_CRON_EVERY_TUESDAY_NINE_AM =
  NODE_ENV !== 'production' ? '0 9 * * 2' : '0 9 * * 2';
const timeZone =
  NODE_ENV !== 'production' ? 'Asia/Karachi' : 'America/Mexico_City';

@Injectable()
export class BlockPaymentsService {
  private readonly logger = new Logger(BlockPaymentsService.name);
  
  constructor(
    @InjectRepository(PaymentsEntity)
    private readonly paymentsRepository: Repository<PaymentsEntity>,
    private readonly stockVehicleService: StockVehicleService,
    private readonly gpsService: GpsService,
  ) {}

  @Cron(MX_CLIENTS_BLOCK_PAYMENTS_CRON_EVERY_TUESDAY_NINE_AM, {
    timeZone,
  })
  async handleBlockPaymentsTuesdayNineAM() {
    if (process.env.RUN_MX_CRON_JOB === 'true') {
      this.logger.log(
        '[BlockPaymentsService] handleBlockPaymentsTuesdayNineAM - Running MX Clients Block Payments Cron Job at 9:00am',
      );
      await this.processBlockPayments();
    }
  }

  async processBlockPayments() {
    try {
      this.logger.log(
        `[BlockPaymentsService] processBlockPayments - cron job started at ${new Date()}`,
      );

      const today = new Date();
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);

      this.logger.log(
        `[BlockPaymentsService] processBlockPayments - searching for payments with dateLimit before ${yesterday.toISOString()}`,
      );

      this.logger.log(
        `[BlockPaymentsService] processBlockPayments - executing database query for unpaid MX payments`,
      );

      const unpaidPayments = await this.paymentsRepository.find({
        where: {
          isPaid: false,
          status: PaymentStatusEnum.PENDING,
          dateLimit: LessThanOrEqual(yesterday),
          client: {
            country: 'MX',
          },
        },
        relations: ['client'],
        take: 10,
      });

      this.logger.log(
        `[BlockPaymentsService] processBlockPayments - database query completed, found ${unpaidPayments?.length || 0} records`,
      );

      if (!unpaidPayments || unpaidPayments.length === 0) {
        this.logger.log(
          `[BlockPaymentsService] processBlockPayments - no unpaid payments found`,
        );
        return;
      }

      this.logger.log(
        `[BlockPaymentsService] processBlockPayments - found ${unpaidPayments.length} unpaid payments for blocking (pilot test)`,
      );

      const blockResults = {
        success: [],
        failed: [],
      };

      for (const payment of unpaidPayments) {
        try {
          this.logger.log(
            `[BlockPaymentsService] processBlockPayments - processing payment ${payment.id} for associate ${payment.client.associateId}`,
          );

          const stockVehicle = await this.stockVehicleService.getStockVehicleByAssociateId(
            payment.client.associateId,
          );

          this.logger.log(
            `[BlockPaymentsService] processBlockPayments - stockVehicle response: ${JSON.stringify(stockVehicle)}`,
          );

          if (!stockVehicle?.data?.gpsNumber) {
            this.logger.warn(
              `[BlockPaymentsService] processBlockPayments - GPS number not found for associate ${payment.client.associateId}. StockVehicle data: ${JSON.stringify(stockVehicle)}`,
            );
            blockResults.failed.push({
              paymentId: payment.id,
              associateId: payment.client.associateId,
              reason: 'GPS number not found',
            });
            continue;
          }

          const gpsNumber = stockVehicle.data.gpsNumber;
          const contractNumber = stockVehicle.data.carNumber || 'N/A';
          const plates = stockVehicle.data.carPlates?.plates || 'N/A';

          this.logger.log(
            `[BlockPaymentsService] processBlockPayments - extracted vehicle data - GPS: ${gpsNumber}, Contract: ${contractNumber}, Plates: ${plates}`,
          );

          // Ejecutar bloqueo real del vehículo
          this.logger.log(
            `[BlockPaymentsService] processBlockPayments - attempting to block vehicle - GPS: ${gpsNumber}, Contract: ${contractNumber}, Plates: ${plates}`,
          );
          
          const blockResult = await this.blockVehicle(gpsNumber);
          
          if (blockResult.success) {
            this.logger.log(
              `[BlockPaymentsService] processBlockPayments - vehicle blocked successfully - GPS: ${gpsNumber}`,
            );
            
            blockResults.success.push({
              paymentId: payment.id,
              associateId: payment.client.associateId,
              gpsNumber: gpsNumber,
              contractNumber: contractNumber,
              plates: plates,
              status: 'blocked_successfully',
            });
          } else {
            this.logger.warn(
              `[BlockPaymentsService] processBlockPayments - failed to block vehicle - GPS: ${gpsNumber}, Reason: ${blockResult.reason}`,
            );
            
            blockResults.failed.push({
              paymentId: payment.id,
              associateId: payment.client.associateId,
              gpsNumber: gpsNumber,
              contractNumber: contractNumber,
              plates: plates,
              reason: blockResult.reason,
            });
          }

        } catch (error: any) {
          this.logger.error(
            `[BlockPaymentsService] processBlockPayments - error processing payment ${payment.id}`,
            {
              error: error?.message || error,
              stack: error?.stack,
            },
          );
          blockResults.failed.push({
            paymentId: payment.id,
            associateId: payment.client.associateId,
            reason: error?.message || 'Unknown error',
          });
        }
      }

      this.logger.log(
        `[BlockPaymentsService] processBlockPayments - sending Slack report with ${blockResults.success.length} successful and ${blockResults.failed.length} failed results`,
      );

      await this.sendBlockingReport(blockResults);

      this.logger.log(
        `[BlockPaymentsService] processBlockPayments - completed. ${blockResults.success.length} successful, ${blockResults.failed.length} failed`,
      );

      return {
        data: blockResults,
        status: true,
      };

    } catch (error: any) {
      this.logger.error(
        `[BlockPaymentsService] processBlockPayments - internal server error`,
        {
          error: error?.message || error,
          stack: error?.stack,
        },
      );
      return {
        message: 'Internal server error',
        status: false,
      };
    }
  }

  async blockVehicle(gpsNumber: string) {
    try {
      this.logger.log(
        `[BlockPaymentsService] blockVehicle - Sending block command for GPS ${gpsNumber}`,
      );

      const blockResponse = await this.gpsService.setBlockEvent(gpsNumber);
      
      if (blockResponse) {
        this.logger.log(
          `[BlockPaymentsService] blockVehicle - GPS ${gpsNumber} block command response: ${JSON.stringify(blockResponse)}`,
        );
      }

      await new Promise(resolve => setTimeout(resolve, 5000));

      const finalStatus = await this.gpsService.gpsStatus(gpsNumber);
      
      if (finalStatus) {
        this.logger.log(
          `[BlockPaymentsService] blockVehicle - GPS ${gpsNumber} final status: ${JSON.stringify(finalStatus)}`,
        );

        if (finalStatus.engineBlock === '1' || finalStatus.engineBlock === 1) {
          return {
            success: true,
            message: 'Vehicle blocked successfully',
          };
        }
      }

      return {
        success: false,
        reason: 'Block command failed - vehicle not blocked',
      };

    } catch (error: any) {
      this.logger.error(
        `[BlockPaymentsService] blockVehicle - error blocking GPS ${gpsNumber}`,
        {
          error: error?.message || error,
          stack: error?.stack,
        },
      );
      return {
        success: false,
        reason: error?.message || 'Unknown error during blocking',
      };
    }
  }

  async sendBlockingReport(blockResults: any) {
    try {
      this.logger.log(
        `[BlockPaymentsService] sendBlockingReport - starting to send Slack report`,
      );

      const { success, failed } = blockResults;
      const totalProcessed = success.length + failed.length;

      this.logger.log(
        `[BlockPaymentsService] sendBlockingReport - preparing message for ${totalProcessed} total processed (${success.length} success, ${failed.length} failed)`,
      );

      let message = `*🚗 Block Payments Report - ${new Date().toLocaleDateString()}*\n\n`;
      message += `*Total Processed:* ${totalProcessed}\n`;
      message += `*Successfully Blocked:* ${success.length}\n`;
      message += `*Failed to Block:* ${failed.length}\n\n`;

      if (success.length > 0) {
        message += `*🚗 Vehicles Successfully Blocked:*\n`;
        success.forEach((item, index) => {
          message += `${index + 1}. Contract: ${item.contractNumber}, Plates: ${item.plates}, GPS: ${item.gpsNumber}\n`;
        });
        message += `\n`;
      }

      if (failed.length > 0) {
        message += `*❌ Failed to Block Vehicles:*\n`;
        failed.forEach((item, index) => {
          message += `${index + 1}. Contract: ${item.contractNumber || 'N/A'}, Reason: ${item.reason}\n`;
        });
      }

      this.logger.log(
        `[BlockPaymentsService] sendBlockingReport - sending message to Slack: ${message}`,
      );

      await this.sendMessageOnSlack(message);
      
      this.logger.log(
        `[BlockPaymentsService] sendBlockingReport - report sent to Slack successfully`,
      );

    } catch (error: any) {
      this.logger.error(
        `[BlockPaymentsService] sendBlockingReport - error sending report`,
        {
          error: error?.message || error,
          stack: error?.stack,
        },
      );
    }
  }

  async sendMessageOnSlack(message: string) {
    this.logger.log(
      `[BlockPaymentsService] sendMessageOnSlack - preparing to send message to channel: ${process.env.ACTIVATION_PAYMENT_CHANNEL}`,
    );

    try {
      const response = await slackApi.chat.postMessage({
        text: message,
        channel: process.env.ACTIVATION_PAYMENT_CHANNEL!,
      });

      this.logger.log(
        `[BlockPaymentsService] sendMessageOnSlack - message sent successfully. Response: ${JSON.stringify(response)}`,
      );
    } catch (error: any) {
      this.logger.error(
        `[BlockPaymentsService] sendMessageOnSlack - error sending message to Slack`,
        {
          error: error?.message || error,
          stack: error?.stack,
        },
      );
      throw error;
    }
  }
}