import { FACTURAPI_SECRET_KEY } from '@/constants';
import { Customers } from './Customers';
import { Wrapper } from './Wrapper';
import { Products } from './Products';
import { Invoices } from './Invoices';
import { Receipts } from './Receipt';

const VALID_API_VERSIONS = ['v1', 'v2'];

const DEFAULT_API_VERSION = VALID_API_VERSIONS[1];

class Facturapi {

  private apikey: string;

  readonly customers: Customers;
  readonly products: Products;
  readonly invoices: Invoices;
  readonly receipts: Receipts;

  private apiVersion: string;

  constructor(apikey: string, options?: { apiVersion?: string }) {
    // console.log('Facturapi constructor');

    if (options && options.apiVersion) {
      if (!VALID_API_VERSIONS.includes(options.apiVersion)) {
        throw new Error(
          'Invalid API version. Valid values are: ' +
          VALID_API_VERSIONS.join(', ')
        );
      }
      this.apiVersion = options.apiVersion;
    } else {
      this.apiVersion = DEFAULT_API_VERSION;
    }

    this.apikey = apikey;

    const wrapper = new Wrapper(this.apikey, /* 'v2' */);

    this.customers = new Customers(wrapper.client);
    this.products = new Products(wrapper.client);
    this.invoices = new Invoices(wrapper.client);
    this.receipts = new Receipts(wrapper.client);

  }


}

const facturapi = new Facturapi(FACTURAPI_SECRET_KEY);


export default facturapi;