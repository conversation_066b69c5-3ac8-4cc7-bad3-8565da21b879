import nodemailer from 'nodemailer';

export const transporter = nodemailer.createTransport({
  // host: 'smtp.gmail.com',
  // port: 465,
  host: process.env.SMTP_HOST,
  pool: true,
  secure: true,
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASS,
  },
});

export interface EmailAttachment {
  filename: string;
  path?: string;
  content?: Buffer | string;
  contentType?: string;
}

export const sendEmail = async (to: string, subject: string, html: string, attachments?: EmailAttachment[] ) => {
  try {
    const res = await transporter.sendMail({
      from: process.env.SMTP_FROM,
      to,
      subject,
      html,
      attachments
    });
    console.log('Email sent to: ', res.accepted, res.response);
    return true;
  } catch (error: any) {
    console.log('Error sending email: ', error);
    // console.log('To: ', to, 'Subject: ', subject);
  }
};

export const sendEmailCronMX = async (to: string, subject: string, html: string) => {
  try {
    await transporter.sendMail({
      from: process.env.SMTP_FROM,
      to,
      subject,
      html,
    });
    return true;
  } catch (error) {
    throw error;
  }
};


// async function paymentLinkEmail(object: any) {
//   console.log('sending email')
//   const html = render(KoalaWelcomeEmail({ name: 'PEDRO MARTINEZ, TEST', url: 'https://demo.onecarnow.com/payment/payment-f6J-CsaGxafncIKj' }))
//   // console.log('html', html)

//   await sendEmail(object.email, 'Enlace de pago OneCarNow!', html);
// }

// paymentLinkEmail({ email: '<EMAIL>' })