import { Controller, Get, Post, Body, Patch, Param, Delete, Res, Query, Logger } from '@nestjs/common';
import { SubscriptionsService } from '../services/susbscriptions.service';
import { CreateSubscriptionDto } from '../dto/create-subscription.dto';
import { AddTemporalSubscriptionProducts, UpdateSubscriptionDto, UpdateSubscriptionStatusDto } from '../dto/update-subscription.dto';
import { tryCatchResponse } from '@/lib/defaultResponse';
import { Response } from 'express';
import { ApiTags } from '@nestjs/swagger';
import { FindOneSubscriptionQueryDto } from '../dto/findOne-query.dto';
import { CreateSubscriptionPayments } from '../services/create-payments.service';
import { FindOperator, ILike, Like } from 'typeorm';
import { FindAllSubscriptionQueryDto } from '../dto/findAll.dto';
import { isUUID } from 'class-validator';
import { CreateSubscriptionPaymentDto, InitiatePayFlowDto } from '../dto/create-payment.dto';
import { SubscriptionsUpdateService } from '../services/subscriptions-update.service';

@ApiTags('subscriptions')
@Controller('subscriptions')
export class SubscriptionsUpdatesController {
  private readonly logger = new Logger(SubscriptionsUpdatesController.name);
  constructor(
    private readonly subscriptionsUpdate: SubscriptionsUpdateService,
  ) { }



  @Patch(':id/add-temporal-products')
  async update(@Param('id') id: string, @Body() body: AddTemporalSubscriptionProducts, @Res() res: Response) {
    return await tryCatchResponse(res, async () => this.subscriptionsUpdate.addTemporalSubscriptionProducts(id, body));
  }


  @Patch(':id/remove-temporal-products')
  async removeTemporalProducts(@Param('id') id: string, @Res() res: Response) {
    return await tryCatchResponse(res, async () => this.subscriptionsUpdate.removeTemporalSubscriptionProducts(id));
  }


}
