import { <PERSON>, Post, Body, Res, Query, Get, Param, Logger } from '@nestjs/common';
import { tryCatchResponse } from '@/lib/defaultResponse';
import { Response } from 'express';
import { UpdatePaymentService } from '../services/update-payments.service';
import { MarkAsPaidDTO } from '../dto/update-payment.dto';
import { FacturapiPaymentsService } from '../services/facturapi-payments.service';
import { FindPaymentInvoicesDto, TypeOfFileDto } from '../dto/facturapi-payments.dto';
import * as fs from 'fs';
import * as path from 'path';
import { PublicRoute } from '@/app/keys/guards/public.decorator';
import { FastifyReply } from 'fastify';
import { MIME_TYPES } from '@/app/aws';

@Controller('payments')
export class PaymentsControllerSecond {

  private readonly logger = new Logger(PaymentsControllerSecond.name);

  constructor(
    private readonly updatePaymentService: UpdatePaymentService,
    private readonly facturapiPaymentsService: FacturapiPaymentsService,
  ) { }

  @Post('mark-as-paid')
  async create(@Body() body: MarkAsPaidDTO, @Res() res: Response) {
    return await tryCatchResponse(res, async () => this.updatePaymentService.markAsPaid(body));
  }

  @Get('invoices')
  async getPaymentInvoices(@Res() res: Response, @Query() query: FindPaymentInvoicesDto) {
    return await tryCatchResponse(res, async () => this.facturapiPaymentsService.getPaymentInvoices(query));
  }

  @Get('receipts')
  async getPaymentReceipts(@Res() res: Response, @Query() query: FindPaymentInvoicesDto) {
    return await tryCatchResponse(res, async () => this.facturapiPaymentsService.getPaymentReceipts(query));
  }

  @Get('invoice/download/:invoiceId')
  async downloadInvoice(@Res() res: FastifyReply, @Param('invoiceId') invoiceId: string, @Query() query: TypeOfFileDto) {

    try {
      const { fileName, stream } = await this.facturapiPaymentsService.downloadInvoice(invoiceId, query.type);


      res.header('Content-Disposition', `attachment; filename=${fileName}`);

      const type = MIME_TYPES[path.extname(fileName).toLowerCase()] || MIME_TYPES.default;

      return res.type(type).code(200).send(stream);

    } catch (error: any) {

      this.logger.error('[SERVER ERROR]', error.message);
      // console.log('[]', error);
      return res.status(500).send({
        message: 'Error downloading file',
        error: error.message,
      });
    }

  }

  @Get('receipt/download/:receiptId')
  async downloadReceipt(@Res() res: Response, @Param('receiptId') receiptId: string, @Query() query: TypeOfFileDto) {

    try {
      const { fileName, stream } = await this.facturapiPaymentsService.downloadReceipt(receiptId, query.type);

      res.header('Content-Disposition', `attachment; filename=${fileName}`);

      const type = MIME_TYPES[path.extname(fileName).toLowerCase()] || MIME_TYPES.default;

      return res.type(type).send(stream);

    } catch (error: any) {

      this.logger.error('[SERVER ERROR]', error.message);
      return res.status(500).send({
        message: 'Error downloading file',
        error: error.message,
      });
    }

  }

  @Get('receipts/:receiptId')
  async getReceiptById(@Res() res: Response, @Param('receiptId') receiptId: string) {
    return await tryCatchResponse(res, async () => this.facturapiPaymentsService.getReceiptById(receiptId));
  }

  @Get('invoices/:invoiceId')
  async getInvoiceById(@Res() res: Response, @Param('invoiceId') invoiceId: string) {
    return await tryCatchResponse(res, async () => this.facturapiPaymentsService.getInvoiceById(invoiceId));
  }

}


/* 

  @PublicRoute()
  @Get('invoice/download/:invoiceId')
  async downloadInvoice(
    @Res() res: any,
    @Param('invoiceId') invoiceId: string,
    @Query() query: TypeOfFileDto,
  ) {
    try {
      const filePath = await this.facturapiPaymentsService.downloadInvoice(invoiceId, query.type);
      const fileName = path.basename(filePath);

      console.log('filePath', filePath);
      console.log('fileName', fileName);

      res.header('Content-Disposition', `attachment; filename=${fileName}`);
      res.sendFile(filePath, (err) => {
        if (err) {
          console.log('Error downloading file', err);
          return res.status(500).send('Error downloading file');
        }

        fs.unlink(filePath, (err) => {
          if (err) {
            console.log('Error deleting file', err);
          }
        });
      });
    } catch (error: any) {
      console.error('[SERVER ERROR]', error.message);
      return res.status(500).send({
        message: 'Error downloading file',
        error: error.message,
      });
    }
  }
*/