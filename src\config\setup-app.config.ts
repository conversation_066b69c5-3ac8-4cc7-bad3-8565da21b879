import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { INestApplication, UnprocessableEntityException, ValidationPipe } from '@nestjs/common';
import { ValidationError } from 'class-validator';

export function setupSwagger(app: INestApplication): void {
  const config = new DocumentBuilder()
    .setTitle('Payments')
    .addBearerAuth()
    .setDescription('The payments API description')
    .setVersion('1.0')
    .addTag('payments')
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('docs/payments', app, document);
}


export function setupGlobalPipes(app: INestApplication): void {
  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      exceptionFactory: (errors) => {
      // const result = errors.map((error) => {

      //   const childrenExist = error.children && error.children.length > 0;

      //   if (childrenExist) {
      //     // Si el error es un objeto anidado (como un array de objetos), procesa sus hijos
      //     console.log('CHILDREN', error.children[0].children[0].constraints[Object.keys(error.children[0].children[0].constraints)[0]])
      //     console.log('PROPERTY', error.children[0].children[0].property)
      //     const childErrors = error.children.map((childError) => ({
      //       property: `${error.property}.${childError.property}`,
      //       // message: childError.constraints[Object.keys(childError.constraints)[0]],
      //     }));
      //     return childErrors;
      //   }

        //   const me = error.constraints[Object.keys(error.constraints)[0]];
        //   return {
        //     property: error.property,
        //     // message: error.constraints[Object.keys(error.constraints)[0]],
        //   }
        // });

        // const result = errors.flatMap((error) => {
        //   const childrenExist = error.children && error.children.length > 0;
        //   if (childrenExist) {
        //     // Si el error tiene hijos, procesa cada uno de ellos recursivamente
        //     return error.children.flatMap((childError) => {
        //       console.log('CHILDREN', childError)
        //       return {
        //         property: childError.property,
        //         message: childError.constraints ? Object.values(childError.constraints)[0] : undefined,
        //       }
        //     });
        //   } else {
        //     // Si no hay hijos, simplemente devuelve el error actual
        //     return [{
        //       property: error.property,
        //       message: error.constraints ? Object.values(error.constraints)[0] : undefined,
        //     }];
        //   }
        // });
        const result = extractErrors(errors);

        return new UnprocessableEntityException(result);
      },
      stopAtFirstError: true,
    }),
  );
}

function extractErrors(errors: ValidationError[], propertyPrefix = '') {
  const result: { message: string; property: string; }[] = [];
  for (const error of errors) {
    if (error.children && error.children.length > 0) {
      // Si hay hijos, procesar recursivamente los errores hijos
      result.push(...extractErrors(error.children, `${propertyPrefix}${error.property}.`));
    } else {
      // Si no hay hijos, agregar el error actual al resultado final
      const prop = error.property.split('.') // Para los casos en los que la propiedad es un array de objetos anidado
      const property = prop[prop.length - 1] || `${propertyPrefix}${error.property}`
      result.push({
        property,
        message: error.constraints ? Object.values(error.constraints)[0] : undefined,
      });
    }
  }
  return result;
}

// const result = errors.map((error) => {
//   if (error.children && error.children.length > 0) {
//     // Si el error es un objeto anidado (como un array de objetos), procesa sus hijos
//     const childErrors = error.children.map((childError) => ({
//       property: `${error.property}.${childError.property}`,
//       message: childError.constraints[Object.keys(childError.constraints)[0]],
//     }));
//     return childErrors;
//   } else {
//     // Si el error es de nivel superior, simplemente procesa el error
//     return {
//       property: error.property,
//       message: error.constraints[Object.keys(error.constraints)[0]],
//     };
//   }
// });