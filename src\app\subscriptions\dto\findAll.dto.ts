import { IsOptional, IsString } from "class-validator";


export class FindAllSubscriptionQueryDto {
  @IsOptional()
  @IsString()
  // @ValidateIf(o => o.clientId !== null)
  readonly email?: string | null;

  @IsOptional()
  @IsString()
  // @ValidateIf(o => o.region !== null)
  readonly contractNumber?: string | null | undefined;

  @IsOptional()
  @IsString()
  // @ValidateIf(o => o.subProductId !== null)
  clientId?: string | null;

  @IsOptional()
  @IsString()
  readonly search?: string | null;

  @IsOptional()
  @IsString()
  readonly id?: string | null;

}