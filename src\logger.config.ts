import { WinstonModule } from 'nest-winston';
import { format, transports } from 'winston';
import SlackHook = require('winston-slack-webhook-transport');
import 'dotenv/config';
import { WebClient } from '@slack/web-api';

const logger = WinstonModule.createLogger({
  transports: [
    new transports.File({
      filename: `logs/error.log`,
      level: 'error',
      format: format.combine(format.timestamp(), format.json()),
    }),
    // logging all level
    new transports.File({
      filename: `logs/info.log`,
      level: 'info',
      format: format.combine(format.timestamp(), format.json()),
    }),
    new transports.Console({
      format: format.combine(
        format.timestamp(),
        format.printf((info) => {
          const orderedLog = {
            timestamp: info.timestamp,
            level: info.level,
            message: info.message,
            ...info, // Spread other fields to ensure they are also included
          };
          return JSON.stringify(orderedLog);
        })

      ),
    }),
    new SlackHook({
      webhookUrl: process.env.SLACK_WEBHOOK_URL,
      channel: '#error-payment-logs',
      username: '<PERSON>ggerBot',
      level: 'error',
      format: format.combine(
        format.timestamp(), // Add a timestamp to Slack logs
        format.printf(({ timestamp, level, message, context, trace }) => {
          return `${timestamp} [${context}] ${level}: ${message}${trace ? `\n${trace}` : ''}`;
        }),
      ),
    }),
  ],
});


export default logger;

export const slackApi = new WebClient(process.env.SLACK_BOT_TOKEN);
