import { Module, forwardRef } from '@nestjs/common';
import { SubscriptionProductsController } from './controllers/subscription-products.controller';
import { SubscriptionProductsService } from './services/subscription-products.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SubscriptionProductEntity } from './entities/subscription-product.entity';
import { SubscriptionsModule } from '../subscriptions/subscriptions.module';
import { SubscriptionEntity } from '../subscriptions/entities/subscription.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([SubscriptionProductEntity, SubscriptionEntity]),
    forwardRef(() => SubscriptionsModule),
  ],
  controllers: [SubscriptionProductsController],
  providers: [SubscriptionProductsService],
  exports: [SubscriptionProductsService],
})
export class SubscriptionProductsModule { }
