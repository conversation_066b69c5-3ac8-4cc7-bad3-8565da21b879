import { CFDI_KEYS_TYPE } from '@/sat/cfdi.types';
import { TaxSystem } from '@/sat/tax_system.types';

export interface ClientAPI {
  address: Address;
  company?: null | string;
  country?: string;
  // countryM?:              CountryClass | CountryEnum | null;
  day: number;
  email: null | string;
  id: string;
  internalId?: string;
  legal_name?: null | string;
  metadata: string | null;
  month: number;
  name: string;
  // owner?:                 Owner;
  phone?: string;
  rfc?: null | string;
  // savedFrom:              SavedFrom;
  stripeCustomer: null;
  stripeId: null;
  tax_system: TaxSystem | {
    value: TaxSystem;
  };
  tax_system_description: null;
  // teamId:                 TeamID;
  timestamp: number;
  week: number;
  year: number;
  yearmonth: string;
  use: CFDI_KEYS_TYPE;
}

export interface Address {
  address?: null | string;
  city?: string;
  country?: string;
  countryName?: string;
  exterior?: string;
  line1?: null;
  line2?: null;
  neighborhood?: string;
  state?: State | null;
  street?: null | string;
  zip?: number | null | string;
}

export enum State {
  CiudadDeMéxico = "Ciudad de México",
  EstadoDeMéxico = "Estado de México",
  Jalisco = "Jalisco",
}