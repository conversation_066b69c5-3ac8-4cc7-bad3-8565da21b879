import { RegionsType } from '@/types/entities.types';
import { CDMX_TOKEN, GDL_TOKEN, MTY_TOKEN, PBC_TOKEN, QRO_TOKEN, TEST_TOKEN, TIJ_TOKEN } from '@constants/index';


export const tokenAssignGigstack = (region: RegionsType): string | null => {
  const REGION_TOKENS = {
    CDMX: CDMX_TOKEN,
    QRO: QRO_TOKEN,
    GDL: GDL_TOKEN,
    TIJ: TIJ_TOKEN,
    MTY: MTY_TOKEN,
    TEST: TEST_TOKEN,
    PBC: PBC_TOKEN,
  };
  return REGION_TOKENS[region] || null;
}