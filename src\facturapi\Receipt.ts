import { CFDI_KEYS_TYPE } from '@/sat/cfdi.types';
import { Customer } from './Customers';
import { CustomAxiosInstance } from './Wrapper';

export interface InvoiceReceiptData {
  customer: Customer | string;
  use?: CFDI_KEYS_TYPE;
  folio_number?: number;
  series?: string;
  global?: any
}

export interface InvoiceReceiptResponse {
  id: string;
  customer: Customer;
  status: 'pending' | 'draft' | 'valid' | 'canceled',
  verification_url: string;
  total: number;
  uuid: string;
  folio_number: number;
  series: string;
  external_id: string;
  items: any[];
}
export class Receipts {

  private readonly client: CustomAxiosInstance;

  constructor(client: CustomAxiosInstance) {
    this.client = client;
  }

  /**
   * Creates a new receipt
   * @param {Object} data
  */
  async create(data: any) {
    return await this.client.post('/receipts', data);
  }

  /**
   * Retrieves a receipt by its ID
   * @param {String} id
  */

  async retrieve(id: string) {
    return await this.client.get('/receipts/' + id);
  }

  async list(params?: any) {
    return await this.client.get('/receipts', { params });
  }

  /**
   * Sends the receipt by email
   * @param {String} id
   * @param {Object} data
  */
  async sendReceiptByEmail(id: string, data?: { email?: string }) {
    return await this.client.post('/receipts/' + id + '/email', data);
  }

  /**
   * Cancels a receipt
   * @param {String} id
  */
  async cancel(id: string) {
    return await this.client.delete('/receipts/' + id);
  }

  /** 
   * Invoice a receipt
   * @param {String} id - receipt id
   * @param {Object} data - invoice data
  */
  async invoice(id: string, data: InvoiceReceiptData) {
    const res = await this.client.post(`/receipts/${id}/invoice`, data);
    return res as InvoiceReceiptResponse;
  }


  /** 
 * Downloads the receipt on PDF, XML or both formats in a ZIP file 
 * @param {String} id
 * @param {String} format
*/
  async download(id: string, format: 'pdf' | 'xml' | 'zip') {
    return await this.client.get(`/receipts/${id}/${format}`, {
      responseType: 'arraybuffer',
      responseEncoding: 'binary',
    });
  }

}