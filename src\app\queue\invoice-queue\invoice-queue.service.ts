import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { PaymentsEntity } from '@/app/payments/entities/payment.entity';
import { INVOICE_QUEUE, JOB_PROCESS_INVOICE } from '@/constants/queues';

@Injectable()
export class InvoiceQueueService {
  private readonly logger = new Logger(InvoiceQueueService.name);

  constructor(@InjectQueue(INVOICE_QUEUE) private invoiceQueue: Queue) { }

  async processInvoice(payment: PaymentsEntity) {
    this.logger.log({
      message: `[InvoiceQueueService] - Añadiendo pago a la cola para facturación: ${payment.id}`,
    });
    const job = await this.invoiceQueue.add(JOB_PROCESS_INVOICE, payment);
    return job.id;
  }
}
