
export const configLoader = () => {
  return {
    port: parseInt(process.env.PORT, 10) || 4000,
    database: {
      host: process.env.DB_HOST,
      port: parseInt(process.env.DB_PORT, 10) || 5432,
      username: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      name: process.env.DB_NAME,
    },
    STRIPE_SECRET_KEY: process.env.STRIPE_SECRET_KEY,
    STRIPE_PAYMENT_WEBHOOK_SECRET: process.env.STRIPE_PAYMENT_WEBHOOK_SECRET,
    REDIS_URL:process.env.REDIS_URL,
    REDIS_HOST:process.env.REDIS_HOST,
    REDIS_PORT:process.env.REDIS_PORT,
  };
}