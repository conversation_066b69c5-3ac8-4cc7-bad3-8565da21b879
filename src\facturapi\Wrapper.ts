import { FACTURAPI_URL } from '@/constants';
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';

export interface CustomAxiosInstance extends AxiosInstance {
  request<T = any>(config: AxiosRequestConfig): Promise<T>;
  get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T>;
  delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T>;
  head<T = any>(url: string, config?: AxiosRequestConfig): Promise<T>;
  post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T>;
  put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T>;
  patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T>;
}

const responseInterceptor = function <T>(response: AxiosResponse<T>): T {
  return response.data;
};

const errorInterceptor = function (error: any) {
  console.log('errorInterceptor FACTURAPI', error.response.data);
  if (error.isAxiosError) {
    return Promise.reject(new Error(error.response.data.message));
  } else {
    return Promise.reject(new Error(error.message));
  }
}

function createAxiosInstance(apiKey: string, /* apiVersion: string */) {
  return axios.create({
    baseURL: /* apiVersion === 'v1' ? '' :  */ FACTURAPI_URL,
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${apiKey}`
    }
  });
}

export class Wrapper {

  readonly client: CustomAxiosInstance;

  constructor(apiKey: string, /* apiVersion: string */) {
    this.client = createAxiosInstance(apiKey, /* apiVersion */);
    this.client.interceptors.response.use(
      responseInterceptor,
      errorInterceptor
    );
  }

  method = async () => {
    const res = await this.client.get('/path');

  }

}