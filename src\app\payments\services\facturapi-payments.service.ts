import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { IsNull, Not, Repository } from 'typeorm';
import { PaymentsEntity } from '../entities/payment.entity';
import { FindPaymentInvoicesDto } from '../dto/facturapi-payments.dto';
import facturapi from '@/facturapi';
import { DownloadInvoice, DownloadReceipt } from '@/changes';
import * as fs from 'fs';

@Injectable()
export class FacturapiPaymentsService {

  constructor(
    @InjectRepository(PaymentsEntity) private readonly paymentRepository: Repository<PaymentsEntity>,
  ) { }


  async getReceiptById(id: string) {

    const receipt = await facturapi.receipts.retrieve(id);

    return {
      message: 'Receipt found',
      data: receipt,
    };

  }

  async getInvoiceById(id: string) {

    const invoice = await facturapi.invoices.retrieve(id);

    return {
      message: 'Invoice found',
      data: invoice,
    };

  }


  async getPaymentInvoices(query: FindPaymentInvoicesDto) {

    const where = { invoiceId: Not(IsNull()) }

    if (query.clientId) where['client'] = { id: query.clientId }
    if (query.facturapiId) where['client'] = { facturapiId: query.facturapiId }

    const invoicesFound = await this.paymentRepository.find({
      where,
      // relations: ['client']
    });

    const invoices = [];

    for (const invoice of invoicesFound) {

      const invoiceData = await facturapi.invoices.retrieve(invoice.invoiceId);

      invoices.push(invoiceData);

    }

    return {
      message: 'Invoices found',
      total: invoices.length,
      data: invoices,
    };

  }

  async getPaymentReceipts(query: FindPaymentInvoicesDto) {

    const where = { receiptId: Not(IsNull()) }

    if (query.clientId) where['client'] = { id: query.clientId }
    if (query.facturapiId) where['client'] = { facturapiId: query.facturapiId }

    const receiptsFound = await this.paymentRepository.find({
      where,
      select: {
        receiptId: true,
      }
    });

    const receipts = [];

    for (const receipt of receiptsFound) {

      const receiptData = await facturapi.receipts.retrieve(receipt.receiptId);

      receipts.push(receiptData);

    }


    return {
      message: 'Receipts found',
      total: receipts.length,
      data: receipts,
    };
  }

  /** 
   * Downloads an invoice from facturapi
   * @param id - The payment id
   * @returns The download URL
   */
  async downloadInvoice(id: string, type: 'pdf' | 'xml' = 'pdf') {

    const { path: filePath, fileName } = await DownloadInvoice(id, type);

    const stream = fs.createReadStream(filePath);

    stream.on('close', () => {
      fs.unlink(filePath, (err) => {
        if (err) {
          console.error('Error deleting file', err);
        }
      });
    });

    return {
      message: 'Invoice downloaded',
      stream,
      fileName,
      filePath,
    };

  }

  async downloadReceipt(id: string, type: 'pdf' | 'xml' = 'pdf') {

    const { path, fileName } = await DownloadReceipt(id, type);

    const stream = fs.createReadStream(path);

    stream.on('close', () => {
      fs.unlink(path, (error) => {
        if (error) {
          console.error('Error deleting file', error);
        }
      })
    });

    return {
      message: 'Receipt downloaded',
      stream,
      fileName,
      path,
    };
  }

}
