import { NotificationToken } from '@/app/notification/entities/notification-token.entity';
import { Notifications } from '@/app/notification/entities/notification.entity';
import { StripeCustomerInfoEntity } from '@/app/stripe-payment/entities/stripe-customer.entity';
import { SubscriptionEntity } from '@/app/subscriptions/entities/subscription.entity';
import { CFDI_KEYS_ENUM, CFDI_KEYS_TYPE } from '@/sat/cfdi.types';
import { TaxSystem, TaxSystemValues } from '@/sat/tax_system.types';
import { RegionsEnum, RegionsType } from '@/types/entities.types';
import { Column, Entity, OneToOne, PrimaryGeneratedColumn, DeleteDateColumn, OneToMany } from "typeorm";

@Entity({ name: 'clients' })
export class ClientEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({
    type: 'text',
    nullable: true
  })
  contractNumber: string; // contractNumber

  @Column({
    type: 'text'
  })
  name: string; // name

  @Column({
    type: 'varchar',
    nullable: true
  })
  lastName: string; // lastName

  @Column({
    type: 'text',
    nullable: true
  })
  email: string; // email

  @Column({
    type: 'text',
    nullable: true
  })
  phone: string; // phone

  @Column({
    type: 'text',
    nullable: true
  })
  rfc: string; // rfc

  @Column({
    type: 'text',
    nullable: true
  })
  street: string; // street

  @Column({
    type: 'text',
    nullable: true
  })
  monexClabe: string; // monexCode

  @Column({
    type: 'text',
    default: 'MX',
  })
  country: string; // country

  @Column({
    type: 'enum',
    enum: TaxSystemValues,
    nullable: true,
    default: '616'
  })
  tax_system: TaxSystem; // tax_system

  @Column({
    type: 'text',
    nullable: true
  })
  legal_name: string; // legal_name

  @Column({
    type: 'enum',
    enum: CFDI_KEYS_ENUM,
    nullable: true,
    default: CFDI_KEYS_ENUM.G03
  })
  use_cfdi: CFDI_KEYS_TYPE;

  @Column({
    type: 'text',
    nullable: true
  })
  zip: string; // zip

  @Column({
    type: 'enum',
    enum: RegionsEnum,
  })
  region: RegionsType; // Mexico cities are being stored as region.

  @Column({
    type: 'text',
    nullable: true
  })
  city: string; // For USA and other countries

  @Column({
    type: 'text',
    nullable: true
  })
  state: string; // For USA and other countries

  @Column({
    type: 'text',
    nullable: true
  })
  gigId: string; // gigId

  @Column({
    type: 'text',
    default: false,
  })
  associateId: string; // associateId

  @Column({
    type: 'boolean',
    default: true,
  })
  isActive: boolean; // isActive

  @Column({ type: 'jsonb', default: {} })
  metadata: any;

  @Column({ type: 'varchar', nullable: true })
  facturapiId: string;

  @Column({ type: 'boolean', nullable: true, default: false })
  is_valid_tax_info: boolean;

  @OneToOne(() => SubscriptionEntity, subscriptions => subscriptions.client)
  subscriptions: SubscriptionEntity;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;
 
  @OneToMany(() => NotificationToken, (notificationToken) => notificationToken.client)
  notificationTokens: NotificationToken[]

  @OneToMany(() => Notifications, (notification) => notification.client)
  notifications: Notifications[]


  @OneToOne(() => StripeCustomerInfoEntity, (stripeCustomerInfo) => stripeCustomerInfo.client,{
    nullable: true
  })
  stripeCustomer: StripeCustomerInfoEntity


  @DeleteDateColumn()
  deletedAt: Date;
}

