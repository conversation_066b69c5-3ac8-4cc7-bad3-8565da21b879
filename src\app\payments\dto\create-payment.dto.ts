import { ClientEntity } from '@/app/clients/entities/client.entity';
import { SubscriptionProductEntity } from '@/app/subscription-products/entities/subscription-product.entity';
import { SubscriptionEntity } from '@/app/subscriptions/entities/subscription.entity';
import { PaymentFormType } from '@/facturapi/Invoices';
import { PaymentFormEnum } from '@/facturapi/enums';
import { Transform, Type } from 'class-transformer';
import { ArrayMinSize, ArrayNotEmpty, IsArray, IsBoolean, IsDefined, IsEnum, IsNumber, IsOptional, IsString, IsUUID, Min, ValidateNested, isUUID } from 'class-validator';


export class CreatePaymentDto {

  @IsDefined()
  @IsNumber()
  total: number;

  @IsDefined()
  @IsNumber()
  subTotal: number;

  @IsDefined()
  @IsNumber()
  tax: number;

  // @IsDefined()
  // // @IsString()
  // subscription: SubscriptionEntity;

  @IsDefined()
  // @IsString()
  @Type(() => SubscriptionEntity)
  subscription: SubscriptionEntity;

  @IsDefined()
  // @IsString()
  @Type(() => ClientEntity)
  client: ClientEntity;

  @IsOptional()
  @IsString()
  createdFrom?: string;

  // @IsDefined()
  // @IsArray()
  // @ValidateNested({ each: true })
  // @Type(() => SubscriptionProductEntity)
  // products: SubscriptionProductEntity[];

}


export class CreateIsolatedPayment {
  @IsDefined()
  @IsUUID()
  readonly clientId: string;

  // @IsDefined()
  // @IsString()
  // productId: string;

  @IsDefined()
  @IsArray()
  @ArrayNotEmpty(/* { message: 'At least one product is required' } */)
  @ArrayMinSize(1/* , { message: 'At least one product is required' } */)
  @ValidateNested()
  @Type(() => ProductElement)
  readonly products: ProductElement[];

  @IsDefined()
  // @IsString()
  @IsEnum(PaymentFormEnum)
  readonly payment_form: PaymentFormType;

  @IsOptional()
  @IsString()
  readonly cfdi_key?: string;

}

class ProductElement {

  @IsDefined({ message: 'Product id is required' })
  // @IsString()
  @IsUUID('4')
  readonly id: string;

  @IsOptional()
  @IsNumber()
  @Min(1, { message: 'Quantity must be at least 1' })
  @Transform(({ value }) => value ?? 1)
  readonly quantity?: number = 1;

}

export class UnlockPaymentDto {
  @IsDefined()
  @IsString()
  readonly clientId: string;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  readonly total: number = 100;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  readonly quantity: number = 1;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  readonly taxRate: number = 0.16;

}

export class SoldPaymentDto {
  @IsDefined()
  @IsString()
  readonly clientId: string;

  @IsDefined()
  @IsString()
  readonly clabe: string;

  @IsDefined()
  @IsNumber()
  @Type(() => Number)
  readonly total: number;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  readonly taxRate: number = 0.16;

}

export class UnlockPaymentPlateDto {
  @IsDefined()
  @IsString()
  readonly plates: string;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  readonly total: number = 100;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  readonly quantity: number = 1;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  readonly taxRate: number = 0.16;

  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  readonly isCron: boolean = false;

  constructor(plates: string,isCron : boolean = false) {
    this.plates = plates;
    this.isCron = isCron
  }

}

export class Validate2LastPaymentsDto {
  // @IsDefined()
  // @IsString()
  // readonly clientId: string;

  @IsDefined()
  @IsString()
  readonly paymentId: string;

  @IsDefined()
  @IsString()
  readonly reactivationPaymentId: string;

}