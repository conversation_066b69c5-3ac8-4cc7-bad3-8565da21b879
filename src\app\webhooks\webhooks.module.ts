import { Modu<PERSON> } from '@nestjs/common';
import { WebhooksController } from './controllers/webhooks.controller';
import { WebhooksService } from './services/webhooks.service';
import { AutoInvoiceWebhookController } from './controllers/auto-invoice.controller';
import { AutoInvoiceService } from './services/auto-invoice.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PaymentsEntity } from '../payments/entities/payment.entity';
import { ClientEntity } from '../clients/entities/client.entity';
import { AutoInvoiceEntity } from './models/auto-invoice.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([PaymentsEntity, ClientEntity, AutoInvoiceEntity]),
    // PaymentsModule,
  ],
  controllers: [WebhooksController, AutoInvoiceWebhookController],
  providers: [WebhooksService, AutoInvoiceService],
  exports: [WebhooksService],
})
export class WebhooksModule { }
