// src/health/redis.health.ts
import { REDIS_URL } from '@/constants';
import { HttpStatus, Injectable, Logger } from '@nestjs/common';
import { HealthIndicator, HealthIndicatorResult, HealthCheckError } from '@nestjs/terminus';
import Redis from 'ioredis';

@Injectable()
export class RedisHealthIndicatorService extends HealthIndicator {
  private client: Redis;
  private readonly logger = new Logger(RedisHealthIndicatorService.name);
  constructor() {
    super();
    this.client = new Redis(REDIS_URL);
  }

  async isHealthy(key: string): Promise<HealthIndicatorResult> {
    try {
      const ping = await this.client.ping();
      if (ping === 'PONG') {
        this.logger.log({
          message: 'Redis is up and running',
          status: HttpStatus.OK
        });
        return this.getStatus(key, true);
      }
      this.logger.log({
        message: 'Redis ping failed',
        status: HttpStatus.INTERNAL_SERVER_ERROR
      });
      throw new Error('Redis ping failed');
    } catch (err) {
      throw new HealthCheckError('Redis health check failed', this.getStatus(key, false));
    }
  }
}
