import { getISOWeek, getYear, sub } from "date-fns";
import { CreateSubscriptionPaymentDto, InitiatePayFlowDto } from "./../dto/create-payment.dto";
import { SubscriptionsService } from "@/app/subscriptions/services/susbscriptions.service";
import { HttpException, Injectable, Logger } from '@nestjs/common';
import { PaymentsService } from '@/app/payments/services/payments.service';
import { SubscriptionEntity } from '../entities/subscription.entity';
import { sendPaymentWAMessage, sendPaymentWAMessageCronMX, sendPaymentWAMessagePAYFLOW } from '@/hilos-whatsapp/payment-message';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In, LessThanOrEqual } from 'typeorm';
import { NODE_ENV, PAYMENTS_CHANNEL } from '@/constants';
import { getLocalDate, getTimezoneDate } from '@/lib/dates';
import { ProductsService } from '@/app/products/services/products.service';
import { PaymentTypeEnum, PaymentsEntity } from '@/app/payments/entities/payment.entity';
import { startOfDay, getDay, addDays } from 'date-fns';
import { ClientEntity } from '@/app/clients/entities/client.entity';
import paymentLinkEmail, { paymentLinkEmailCronMX } from '@/providers/nodemailer/templates/paymentLink.template';
import { transporter } from '@/providers/nodemailer';
import { NotificationService } from "@/app/notification/services/notification.service";
import { CountriesEnum, CountriesShortNames } from "@/app/clients/dto/create-client.dto";
import { StripePaymentService } from "@/app/stripe-payment/stripe-payment.service";
import { Cron, CronExpression } from "@nestjs/schedule";
import { slackApi } from '@/logger.config';
import { WeeklyTracking } from '../entities/weekly-tracking.entity';
import { WeeklyRecord } from '../entities/weekly-record.entity';
import { PaymentQueueService } from "@/app/queue/payment-queue/payment-queue.service";

const MAX_EMAILS_PER_SECOND = 8;
const EMAIL_DELAY = 1000 / MAX_EMAILS_PER_SECOND;
async function sleep(ms: number) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

const US_CLIENTS_PAYMENTS_CRON_EVERY_FRIDAY_FIVE_PM = "0 17 * * 5";
const MX_CLIENTS_PAYMENTS_CRON_EVERY_THURSDAY_FIVE_PM = NODE_ENV !== 'production' ? "0 12 * * 4" : "0 17 * * 4";
const timeZone = NODE_ENV !== 'production' ? 'Asia/Karachi' : 'America/Mexico_City';

console.log('MX_CLIENTS_PAYMENTS_CRON_EVERY_THURSDAY_FIVE_PM', MX_CLIENTS_PAYMENTS_CRON_EVERY_THURSDAY_FIVE_PM);
console.log('timeZone', timeZone);
const isDev = NODE_ENV !== 'production';

@Injectable()
export class CreateSubscriptionPayments {
  private readonly logger = new Logger(CreateSubscriptionPayments.name);
  constructor(
    @InjectRepository(PaymentsEntity) private readonly paymentsRepository: Repository<PaymentsEntity>,
    @InjectRepository(ClientEntity) private readonly clientsRepository: Repository<ClientEntity>,
    @InjectRepository(SubscriptionEntity) private readonly subscriptionsRepository: Repository<SubscriptionEntity>,
    @InjectRepository(WeeklyTracking) private readonly weeklyTrackingRepository: Repository<WeeklyTracking>,
    @InjectRepository(WeeklyRecord) private readonly weeklyRecordRepository: Repository<WeeklyRecord>,
    private readonly paymentQueueService: PaymentQueueService,
    private readonly paymentsService: PaymentsService,
    private readonly subscriptionsService: SubscriptionsService,
    private readonly productService: ProductsService,
    private readonly notificationService: NotificationService,
    private readonly stripePaymentService: StripePaymentService
  ) { }

  async createSinglePayment(subscriptionId: string) {
    const { data: subscription } = await this.subscriptionsService.findOne(subscriptionId);

    const payment = await this.paymentsService.create({
      total: subscription.total,
      subTotal: subscription.subTotal,
      tax: subscription.tax,
      client: subscription.client,
      subscription: subscription,
    });

    this.logger.log(`[createSinglePayment] - sending whatsApp message to client ${subscription.client.name}`);
    await sendPaymentWAMessage(subscription.client.name, payment.data.id, subscription.client.phone);
    this.logger.log(`[createSinglePayment] - whatsApp message sent to client ${subscription.client.name}`);
  
    this.logger.log(`[createSinglePayment] - sending payment link via email to client ${subscription.client.name}`);
    await paymentLinkEmail({ email: subscription.client.email, paymentId: payment.data.id, name: subscription.client.name });
    this.logger.log(`[createSinglePayment] - payment link sent to client ${subscription.client.name} via email`);

    this.logger.log(`[createSinglePayment] - sending push notification to client ${subscription.client.name}`);
    await this.notificationService.sendPush(
      subscription.client,
      `!Hola ${subscription.client.name}`,
      "Adjuntamos el enlace de pago correspondiente a el pago de la renta semanal de la siguiente semana.\nRecuerda que debes realizado el día lunes antes de las 5pm.\nSaludos!",
      {
        type: JSON.stringify({
          operation: 'payment',
          action: 'paymentCreated',
        }),
        link: 'https://pagos.onecarnow.com/pago/' + payment.data.id
      },
    );
    this.logger.log(`[createSinglePayment] - notification sent to client ${subscription.client.name}`);

    return {
      message: 'Payment created',
      data: payment.data,
    };
  }

  async createPayments(query?: CreateSubscriptionPaymentDto) {
    // need to query with todaydate being the timezone date at 23:59 of the current day in mexico city timezone
    // but the date should be the utc date transformed
    const todayDate = getTimezoneDate({
      timezone: 'America/Mexico_City',
      hour: 23,
      minute: 59,
    })
    this.logger.log({
      message: `[createPayments] - Starting MX Payment Link Send Cron Job, Date for today ${todayDate}`,
    })

    const where = {
      isActive: true,
      startDate: LessThanOrEqual(todayDate),
      client:{
        country: In([CountriesShortNames[CountriesEnum.Mexico], CountriesEnum.Mexico]) 
      }
    }

    if (query?.clientId) where['client'] = Object.assign(where.client,{ id: query.clientId });

    if (query?.subscriptionId) where['id'] = query.subscriptionId

    const { data: subscriptions } = await this.subscriptionsService.findAll({
      where,
    });
    this.logger.log({
      message: `[createPayments] - Found ${subscriptions.length} active subscriptions for clients with country MX`,
    })
    
    /**
     * Just for logging purpose to get a count of clients with country value as full string Mexico
     */
    let totalClientsSubscriptionWithCountryMexico = 0;
    try {
      const { data: subscriptionsMexico } =
        await this.subscriptionsService.findAll({
          where: {
            isActive: true,
            startDate: LessThanOrEqual(todayDate),
            client:{
              country: CountriesEnum.Mexico, 
            }
          },
        });
      this.logger.log({
        message: `[createPayments] - Found ${subscriptionsMexico.length} active subscriptions for clients with country Mexico`,
      });
      totalClientsSubscriptionWithCountryMexico = subscriptionsMexico.length;
    } catch (err: any) {
      this.logger.error({
        message: `[createPayments] - Error occured while fetching subscriptions for clients with country Mexico`,
        error: err?.stack,
      });
    }
    subscriptions.map(async (subscription)=>{
      await this.paymentQueueService.createPayment(subscription)
    })
  }

  /* CRONJOB FOR CREATE PAYMENTS */
  @Cron(MX_CLIENTS_PAYMENTS_CRON_EVERY_THURSDAY_FIVE_PM, {
    timeZone,
  })
  async handleWeeklyThursdayPayments() {
    if (process.env.RUN_MX_CRON_JOB === 'true') {
      this.logger.log('Running MX Clients Payments Cron Job');
      await this.createPayments();
    }
  }

  async initiatePayFlow(body?: InitiatePayFlowDto) {

    // const { data: client } = await this.clientService.findOne(body.clientId);
    const client = await this.clientsRepository.findOne({
      where: {
        id: body.clientId,
      },
      relations: ['subscriptions','stripeCustomer'],
      select: {
        id: true,
        name: true,
        phone: true,
        email: true,
        region: true,
        subscriptions: {
          id: true,
          isActive: true,
        },
        country: true,
        state:true,
        city:true,
        stripeCustomer:{
          id:true,
          isCustomerBankAccountLinked:true,
          stripeCustomerId:true
        }
      }
    });
    if(!client){
      throw new HttpException(`Client does not exist with id ${body.clientId}`, 400);
    }

    console.log('STARTING PAY FLOW ------------------------------------------------')
    // console.log('CLIENT', client);

    const subscriptionFound = await this.subscriptionsRepository.findOne({
      where: {
        client: { id: body.clientId },
      }
    });

    if (subscriptionFound) {
      throw new HttpException('Subscription already exists', 409);
    }

    const { products, startDate, endDate, downPaymentProduct, depositProduct } = body;

    // console.log('body', body)


    if(client.country ===  CountriesShortNames[CountriesEnum["United States"]]){
      this.logger.log(`Subscription Payment initiated using Stripe for client with id ${client.id}`);
      const newPayment = await this.usClientsSubscriptionPaymentFlow(body,client);
      return {
        message: 'Payments initiated',
        data: newPayment,
      }
    }


    const startDate2 = getLocalDate(new Date(startDate));

    const paymentProducts = [];
    let totalPaymentAmount = 0;
    let subTotalPaymentAmount = 0;
    let taxPaymentAmount = 0;

    const textBody = JSON.stringify(body);

    for (const p of products) {
      // const { data: product } = await this.productsService.findOne(p.id);

      const { data: product } = await this.productService.findOne(p.id);

      // console.log('product', product)
      // Use price from body instead of database
      const priceFromBody = p.price || product.price;
      const total = priceFromBody * p.quantity;

      // Use tax values from body if available, otherwise calculate from product
      const taxRate = p.taxRate || product.taxRate;
      const subTotal = p.subTotal || (total * (1 - (taxRate === 1 ? 0 : taxRate)));
      const tax = p.tax || (total - subTotal);

      totalPaymentAmount += total;
      subTotalPaymentAmount += subTotal;
      taxPaymentAmount += tax;

      const newProduct = {
        id: product.id,
        name: product.name,
        total: total,
        subTotal,
        quantity: p.quantity,
        tax,
        taxRate,
        taxFactor: product.taxFactor,
        taxType: product.taxType,
        product_key: product.product_key,
        unit_key: product.unit_key,
        measurementUnit: product.measurementUnit,
      }

      paymentProducts.push(newProduct);
    }

    // console.log('paymentProducts', paymentProducts)
    // console.log('totalPaymentAmount', totalPaymentAmount)
    // console.log('subTotalPaymentAmount', subTotalPaymentAmount)
    // console.log('taxPaymentAmount', taxPaymentAmount)

    const textProducts = JSON.stringify(paymentProducts);

    const amounts = `Total: ${totalPaymentAmount}, SubTotal: ${subTotalPaymentAmount}, Tax: ${taxPaymentAmount}`;

    const d = getStartDate(startDate2);

    const start = getLocalDate(d);

    if (downPaymentProduct) {
      const { data: product } = await this.productService.findOne(downPaymentProduct.id);

      // Use price from body instead of database
      const priceFromBody = downPaymentProduct.price || product.price;
      const total = priceFromBody * downPaymentProduct.quantity;

      // Use tax values from body if available, otherwise calculate from product
      const taxRate = downPaymentProduct.taxRate || product.taxRate;
      const subTotal = downPaymentProduct.subTotal || (total * (1 - (taxRate === 1 ? 0 : taxRate)));
      const tax = downPaymentProduct.tax || (total - subTotal);

      totalPaymentAmount += total;
      subTotalPaymentAmount += subTotal;
      taxPaymentAmount += tax;

      const newProduct = {
        id: product.id,
        name: product.name,
        total: total,
        subTotal,
        quantity: downPaymentProduct.quantity,
        tax,
        taxRate,
        taxFactor: product.taxFactor,
        taxType: product.taxType,
        product_key: product.product_key,
        unit_key: product.unit_key,
        measurementUnit: product.measurementUnit,
      }

      paymentProducts.push(newProduct);
    }

    const createSubscription = {
      clientId: body.clientId,
      products: paymentProducts,
      startDate: start.toISOString(),
      endDate,
      region: client.region,
    }
    console.log('-----------------------------------------------------------------')
    console.log('createSubscription', createSubscription);
    console.log('-----------------------------------------------------------------')

    const { data: subscription } = await this.subscriptionsService.createWithProcessedProducts(createSubscription);

    const textSubscriptionCreated = JSON.stringify(subscription);

    try {
      await slackApi.chat.postMessage({
        blocks: [
          {
            type: "section",
            text: {
              type: "mrkdwn",
              text: `*INCOMING INITIATE PAY FLOW*, CLIENT: ${client.name}, REGION: ${client.region}`
            }
          },
          {
            type: "section",
            text: {
              type: "mrkdwn",
              text: `*Body*\n\n${textBody}`
            }
          },
          {
            type: "section",
            text: {
              type: "mrkdwn",
              text: `*Subscription Created*\n\n${textSubscriptionCreated}`
            }
          },
          {
            type: "section",
            text: {
              type: "mrkdwn",
              text: `*Products*\n\n${textProducts}`
            }
          },
          {
            type: "section",
            text: {
              type: "mrkdwn",
              text: `*Amounts*\n\n${amounts}`
            }
          },

        ],
        channel: process.env.CREATE_SUBSCRIPTION_CHANNEL!
      })
    } catch (error) {
      // dont do anything if slack fails
    }

    const newPaymentInstance = this.paymentsRepository.create({
      total: totalPaymentAmount,
      subTotal: subTotalPaymentAmount,
      tax: taxPaymentAmount,
      client,
      products: paymentProducts,
    });


    const newPayment = await this.paymentsRepository.save(newPaymentInstance);
    await sendPaymentWAMessagePAYFLOW(client.name, newPayment.id, client.phone);
    await paymentLinkEmail({ email: client.email, paymentId: newPayment.id, name: client.name });
    await this.paymentsRepository.update(newPayment.id, { subscription, type: PaymentTypeEnum.subscription });

    // delete last dowPaymentProduct of subscription products

    if (downPaymentProduct) {

      paymentProducts.pop(); // Remove the last element (downPaymentProduct)


      // const editSubscription = {
      //   ...createSubscription,
      //   products: paymentProducts,
      // }

      const { total, subTotal, tax } = paymentProducts.reduce((acc, p) => {
        acc.total += p.total;
        acc.subTotal += p.subTotal;
        acc.tax += p.tax;
        return acc;
      }, { total: 0, subTotal: 0, tax: 0 });

      subscription.products = paymentProducts;
      subscription.total = total;
      subscription.subTotal = subTotal;
      subscription.tax = tax;

      await this.subscriptionsRepository.save(subscription);
      // await this.subscriptionsService.update(subscription.id, editSubscription);
      // await this.subscriptionsRepository.update(subscription.id, subscription);
    }


    if (depositProduct) {
      const { data: product } = await this.productService.findOne(depositProduct.id);

      // Use price from body instead of database
      const priceFromBody = depositProduct.price || product.price;
      const total = priceFromBody;

      // Use tax values from body if available, otherwise calculate from product
      const taxRate = depositProduct.taxRate || product.taxRate;
      const subTotal = depositProduct.subTotal || (total * (1 - (taxRate === 1 ? 0 : taxRate)));
      const tax = depositProduct.tax || (total - subTotal);

      const newDepositProduct = {
        id: product.id,
        name: product.name,
        total: total,
        subTotal,
        quantity: depositProduct.quantity,
        tax,
        taxRate,
        taxFactor: product.taxFactor,
        taxType: product.taxType,
        product_key: product.product_key,
        unit_key: product.unit_key,
        measurementUnit: product.measurementUnit,
        client,
      }


      const newDepositPaymentInstance = this.paymentsRepository.create(newDepositProduct);

      newDepositPaymentInstance.products = [newDepositProduct];
      // newDepositPaymentInstance.client = client;
      await this.paymentsRepository.save(newDepositPaymentInstance);

      // Send WhatsApp message
      await sendPaymentWAMessagePAYFLOW(client.name, newDepositPaymentInstance.id, client.phone);

      await paymentLinkEmail({ email: client.email, paymentId: newDepositPaymentInstance.id, name: client.name });
    }

    return {
      message: 'Payments initiated',
      data: newPayment,
    }

  }

  @Cron(US_CLIENTS_PAYMENTS_CRON_EVERY_FRIDAY_FIVE_PM,{
    timeZone:'America/Mexico_City',
    name: "US_CLIENTS_PAYMENTS_CRON_EVERY_FRIDAY_FIVE_PM"
  })
  async createPaymentsForUsClients() {

    this.logger.log('Starting Cron Job for US clients payments flow');
    const todayDate = getLocalDate(undefined, {
      hours: 23,
      minutes: 59,
    });
    this.logger.log(
      `Starting Cron Job for US clients payments flow, date=${todayDate}`,
    );

    const { data: subscriptions } = await this.subscriptionsService.findAll({
      where: {
        isActive: true,
        startDate: LessThanOrEqual(todayDate),
        client:{
          country: CountriesShortNames[CountriesEnum["United States"]]
        }
      },
      relations: {
        client: true,
        products:true
      },
    });

    if (subscriptions.length === 0) {
      this.logger.log('No subscriptions found for US clients');
      return;
    }

    await Promise.all(
      subscriptions.map(async (subscription) => {
        const newPaymentInstance = this.paymentsRepository.create({
          total: subscription.total,
          subTotal: subscription.subTotal,
          tax: subscription.tax,
          client: subscription.client,
          products: subscription.products,
        });

        const payment = await this.paymentsRepository.save(newPaymentInstance);
        const client = subscription.client;

        try {
          this.logger.log(
            `Starting Stripe ACH Direct Debiting flow for client with id ${client.id}`,
          );
          await this.stripePaymentService.createStripePaymentTransaction({
            client: client.id,
            amountInUSDollars: subscription.subTotal,
            paymentId: payment.id,
          });

        } catch (err) {
          this.logger.error(
            `Error occurred while debiting client using stripe with id ${client.id}. Error: ${err}`,
          );
        }
      }),
    );
  }

  private async usClientsSubscriptionPaymentFlow(body:any, client:ClientEntity){
    const { products, startDate, endDate, downPaymentProduct, depositProduct } =
      body;
    const paymentProducts = [];
    let totalPaymentAmount = 0;
    let subTotalPaymentAmount = 0;
    let taxPaymentAmount = 0;

    for (const p of products) {
      const { data: product } = await this.productService.findOne(p.id);

      const total = product.price;
      const subTotal = product.subTotal;
      const tax = product.tax;

      totalPaymentAmount += total;
      subTotalPaymentAmount += subTotal;
      taxPaymentAmount += tax;

      const newProduct = {
        id: product.id,
        name: product.name,
        total: total,
        subTotal: subTotal,
        quantity: p.quantity,
        tax: tax,
        taxRate: product.taxRate,
        taxFactor: product.taxFactor,
        taxType: product.taxType,
        product_key: product.product_key,
        unit_key: product.unit_key,
        measurementUnit: product.measurementUnit,
      };
      paymentProducts.push(newProduct);
    }
    const startDateLocalFormat = getLocalDate(new Date(startDate));
    const _paymentStartDate = getStartDate(startDateLocalFormat);
    const start = getLocalDate(_paymentStartDate);

    const createSubscription = {
      clientId: body.clientId,
      products: paymentProducts,
      startDate: start.toISOString(),
      endDate,
      region: client.region,
    };

    const { data: subscription } =
      await this.subscriptionsService.createSubscriptionForUSClient(
        createSubscription,
      );

    const newPaymentInstance = this.paymentsRepository.create({
      total: totalPaymentAmount,
      subTotal: subTotalPaymentAmount,
      tax: taxPaymentAmount,
      client,
      products: paymentProducts,
    });
    const newPayment = await this.paymentsRepository.save(newPaymentInstance);    
    await this.paymentsRepository.update(newPayment.id, {
      subscription,
      type: PaymentTypeEnum.subscription,
    });
    try {
      this.logger.log(
        `Starting Stripe ACH Direct Debiting flow for client=${client.id}`,
      );
      await this.stripePaymentService.createStripePaymentTransaction({
        clientId: client.id,
        amountInUSDollars: subTotalPaymentAmount,
        paymentId: newPayment.id,
      });
    } catch (err) {
      this.logger.error(
        `Error occurred while debiting client using stripe clientId=${client.id}`,
        err,
      );
      throw err;
    }
    return newPayment;
  }
}


/**
 * Calculate the start date for a subscription based on the current date
 * if the current date is Monday, Tuesday, or Wednesday, the start date is the same date
 * if the current date is Thursday, Friday, Saturday, or Sunday, the start date is the next Thursday
 * @param date The current date
 * @returns The start date for the subscription
 */

export function getStartDate(date: Date) {
  const fecha = startOfDay(date); // Normaliza la hora al inicio del día
  const diaSemana = getDay(fecha); // Obtiene el día de la semana (0 es domingo, 1 es lunes, etc.)

  // Si es lunes (1), martes (2), o miércoles (3)
  if (diaSemana >= 1 && diaSemana <= 3) {
    return fecha; // Retorna la misma fecha
  }

  // Para cualquier otro día (jueves, viernes, sábado, domingo)
  return addDays(fecha, (11 - diaSemana) % 7); // Ajusta al siguiente jueves
}
