import { PaymentsEntity } from '@/app/payments/entities/payment.entity';
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
} from 'typeorm';

export enum PaymentIntentStatus {
  PENDING = 'PENDING',
  SUCCEEDED = 'SUCCEEDED',
  FAILED = 'FAILED',
}

export enum PaymentCurrency {
  USD = 'USD',
  MXN = 'MXN',
}

@Entity('stripe_payment_transaction')
export class StripePaymentTransactionEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({
    type: 'varchar',
    nullable: true,
  })
  stripePaymentIntentId: string;

  @Column({
    type: 'jsonb',
    nullable: true,
  })
  stripePaymentIntentObj: object;

  @Column({
    type: 'enum',
    enum: PaymentIntentStatus,
    default: PaymentIntentStatus.PENDING,
  })
  status: PaymentIntentStatus;

  @ManyToOne(() => PaymentsEntity, (payment) => payment.id)
  payment: PaymentsEntity;

  @Column({ type: 'float' })
  amount: number;

  @Column({ type: 'enum', enum: PaymentCurrency, default: PaymentCurrency.USD })
  currency: PaymentCurrency;

  @CreateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  createdAt: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP(6)',
    onUpdate: 'CURRENT_TIMESTAMP(6)',
  })
  updatedAt: Date;

  @Column({
    type: 'timestamp',
    nullable: true,
  })
  deletedAt: Date;
}
