import { BullModule } from '@nestjs/bullmq';
import { Module } from '@nestjs/common';
import { NotificationQueueService } from './notification-queue.service';
import { NotificationProcessor } from './notification-queue.processor';
import { BullMQAdapter } from '@bull-board/api/bullMQAdapter';
import { BullBoardModule } from '@bull-board/nestjs';
import { NotificationModule } from '../../notification/notification.module';
import { NOTIFICATION_QUEUE } from '@/constants';

@Module({
  imports: [
    BullModule.registerQueue({
      name: NOTIFICATION_QUEUE,
    }),
    BullBoardModule.forFeature({
      name: NOTIFICATION_QUEUE, // Register the queue with Bull Board
      adapter: BullMQAdapter,
    }),
    NotificationModule,
  ],
  providers: [NotificationQueueService, NotificationProcessor],
  exports: [NotificationQueueService],
})
export class NotificationQueueModule {}
