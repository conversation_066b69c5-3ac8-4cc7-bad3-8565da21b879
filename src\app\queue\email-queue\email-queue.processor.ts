import { OnWorkerEvent, Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { paymentLinkEmailCronMX } from '@/providers/nodemailer/templates/paymentLink.template';
import { EMAIL_QUEUE } from '@/constants';

@Processor({
  name: EMAIL_QUEUE,
})
export class EmailProcessor extends WorkerHost {
  private readonly logger = new Logger(EmailProcessor.name);

  async process(job: Job<any, any, string>): Promise<any> {
    const payment = job.data;
    // send email here
    this.logger.log({
      message: `[EmailProcessor] - Email is being processed: ${job.data} `,
    });
    await paymentLinkEmailCronMX({
      email: payment.client.email,
      paymentId: payment.id,
      name: payment.client.name,
    });
  }

  @OnWorkerEvent('active')
  onActive(job: Job) {
    this.logger.log(`[EmailProcessor] - Active ${job.id} ${job.data}`);
  }

  @OnWorkerEvent('completed')
  onCompleted(job: Job) {
    this.logger.log(`[EmailProcessor] - Completed ${job.id} ${job.data}`);
  }

  @OnWorkerEvent('failed')
  onFailed(job: Job) {
    this.logger.log(`[EmailProcessor] - Failed ${job.id} ${job.data}`);
  }
}
