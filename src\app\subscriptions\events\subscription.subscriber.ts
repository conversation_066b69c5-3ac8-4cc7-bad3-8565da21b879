import { EventSubscriber, EntitySubscriberInterface, InsertEvent, UpdateEvent, RemoveEvent, DataSource } from 'typeorm';
import { ClientEntity } from '@/app/clients/entities/client.entity';
import { SubscriptionEntity } from '../entities/subscription.entity';

@EventSubscriber()
export class SubscriptionSubscriber implements EntitySubscriberInterface<SubscriptionEntity> {


  constructor(
    private readonly dataSource: DataSource,
  ) {
    console.log('SubscriptionSubscriber initialized');
  }

  listenTo() {
    return SubscriptionEntity;
  }

  async beforeInsert(event: InsertEvent<SubscriptionEntity>) {
    const subscription = event.entity;

    if (subscription.client && subscription.client.id) {
      const client = await event.manager.findOne(ClientEntity, {
        where: {
          id: subscription.client.id,
        },
        select: {
          id: true,
          isActive: true,
        },
      });

      if (client) {
        client.isActive = true;
        await event.manager.save(client);
      }
    }
  }

  // private async handleEvent(event: InsertEvent<any> | UpdateEvent<any> | RemoveEvent<any>) {
  //   const entity = event.entity;

  //   const metadata = this.dataSource.getMetadata(entity.constructor);

  //   const entityRepository = this.dataSource.getRepository(metadata.target);

  //   const entityInstance = await entityRepository.findOne({
  //     where: {
  //       id: entity.id,
  //     },
  //   });

  // }

}
