import {
  IsEnum,
  <PERSON><PERSON>ptional,
  IsString,
  Min<PERSON><PERSON>th,
  IsPositive,
  IsNumber,
  IsArray,
  ValidateNested,
} from 'class-validator';
import {
  PaymentStatusEnum,
  PaymentStatusType,
} from '../entities/payment.entity';
import { RegionsEnum, RegionsType } from '@/types/entities.types';

export class UpdatePaymentDto {
  @IsOptional()
  @IsEnum(PaymentStatusEnum)
  readonly status?: PaymentStatusType;

  @IsOptional()
  @IsEnum(RegionsEnum)
  readonly region?: RegionsType;
}

// Define the class for a single selected transaction
export class SelectedTransactionDTO {
  @IsNumber()
  @IsPositive()
  id!: number;

  @IsString()
  @MinLength(1)
  depositant!: string;

  @IsString()
  depositantClabe!: string;

  @IsString()
  @MinLength(1)
  description!: string;

  @IsNumber()
  @IsPositive()
  amount!: number;

  @IsString()
  @MinLength(1)
  senderRfc!: string;

  @IsString()
  @MinLength(1)
  claveRastreo!: string;

  @IsString() // ISO datetime string
  depositDate!: string;
}

// Define the main class for the Mark as paid object
export class MarkAsPaidDTO {
  @IsString() // Payment ID
  paymentId!: string;

  @IsArray()
  @ValidateNested({ each: true }) // Validate each item in the array
  selectedTransactions!: SelectedTransactionDTO[];
}
