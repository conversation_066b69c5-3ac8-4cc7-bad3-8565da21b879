import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { EMAIL_QUEUE, JOB_SEND_EMAIL } from '@/constants';

@Injectable()
export class EmailQueueService {
  private readonly logger = new Logger(EmailQueueService.name);
  constructor(@InjectQueue(EMAIL_QUEUE) private emailQueue: Queue) {}

  async sendEmail(data: any) {
    this.logger.log({
      message: `[EmailQueueService] - Email added to queue for sending: ${data}`,
    });
    const job = await this.emailQueue.add(JOB_SEND_EMAIL, data);
    return job.id; // Return the job ID for tracking
  }
}
