import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { CreateProductDto } from '../dto/create-product.dto';
import { UpdateProductDto } from '../dto/update-product.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { ProductEntity } from '../entities/product.entity';
import { Like, Raw, Repository } from 'typeorm';
import { FindAllProductsQuery } from '../dto/find-product.dto';
import { CountriesEnum, CountriesShortNames, CountriesShortNamesEnum } from '@/app/clients/dto/create-client.dto';

@Injectable()
export class ProductsService {
  constructor(
    @InjectRepository(ProductEntity)
    private productsRepository: Repository<ProductEntity>,
  ) {}

  async create(product: CreateProductDto) {
    const _product = this.sanitizeProductDto(product);

    const newProduct = this.productsRepository.create(_product as unknown as ProductEntity);

    if(newProduct.country === CountriesShortNames[CountriesEnum.Mexico]){
      this.calculateTotalProduct(newProduct);
    }

    console.log('🚀 ~ ProductsService ~ create ~ newProduct:', newProduct)

    await this.productsRepository.save(newProduct);

    return {
      message: 'Product created',
      data: newProduct,
    };
  }

  async findAll(query?: FindAllProductsQuery) {
  console.log({query})
    const where: any = {};

  if(query.country){
      where['country'] = CountriesShortNames[query.country] ;
    }

   if (query?.name)
      where.name = Raw(
       (name) => `LOWER(${name}) ILIKE '%${query.name.toLowerCase().trim()}'`,
     );
    if (query?.region) where.region = query.region;

    const take = query?.selectOne ? 1 : undefined;

    const products = await this.productsRepository.find({
      where,
      take,
    });

    return {
      message: 'Products found',
      total: products.length,
      data: products,
    };
  }

  async findOne(id: string) {
    const product = await this.productsRepository.findOne({
      where: {
        id,
      },
    });

    if (!product) {
      throw new HttpException('Product not found', HttpStatus.NOT_FOUND);
    }

    return {
      message: 'Product found',
      data: product,
    };
  }

  async update(id: string, updateProductDto: UpdateProductDto) {
    const product = await this.productsRepository.findOne({
      where: {
        id,
      },
    });


    if (!product) {
      return {
        message: 'Product not found',
      };
    }

    const productToUpdate = {
      ...product,
      ...updateProductDto,
    };

    // Re calculate subTotal and tax of product if price or taxRate has changed
    if (
      updateProductDto.price !== product.price ||
      updateProductDto.taxRate !== product.taxRate
    ) {
      this.calculateTotalProduct(productToUpdate as ProductEntity);
    }

    const updatedProduct = await this.productsRepository.save(productToUpdate as ProductEntity);

    return {
      message: 'Product updated',
      data: updatedProduct,
    };

  }

  async remove(id: string) {

    const product = await this.productsRepository.findOne({
      where: {
        id,
      },
    });

    if (!product) {

      throw new HttpException('Product not found', HttpStatus.NOT_FOUND);

    }

    await this.productsRepository.remove(product);

    return {
      message: `Product ${product.name} removed`,
    };
  }

  calculateTotalProduct(product: ProductEntity) {

    const taxRate = product.taxRate;

    const rate = taxRate === 1 ? 0 : taxRate || 0.16;
    const tax = rate === 1 ? 0 : product.price * rate;

    const subTotal = product.price * (1 - rate);

    product.subTotal = Number(subTotal.toFixed(2));
    product.tax = Number(tax.toFixed(2));
  }

  private sanitizeProductDto(product: CreateProductDto) {
    if (product.country) {
      if (product.country === CountriesEnum['United States']) {
        delete product.taxType;
        delete product.taxFactor;
      }
      product.country = CountriesShortNamesEnum[product.country];
    }
    return product;
  }
}
