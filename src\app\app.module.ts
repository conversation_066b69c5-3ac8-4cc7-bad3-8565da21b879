import { MiddlewareConsumer, Module, RequestMethod } from '@nestjs/common';
import { AppController } from '@/app/app.controller';
import { AppService } from '@/app/app.service';
import { RequestLoggerMiddleware } from '@/middlewares/request-logger.middleware';
import { CorrelationIdMiddleware } from '@/middlewares/correlation-id.middleware';
import { AppGlobalConfigModule } from '@/config/config.module';
import { ClientsModule } from './clients/clients.module';
import { TypeOrmConfigModule } from '@/db/db-connection.module';
import { ProductsModule } from './products/products.module';
import { SubscriptionsModule } from './subscriptions/subscriptions.module';
import { AuthModule } from '@app/jwt/auth.module';
import { KeysModule } from '@app/keys/keys.module';
import { APP_GUARD } from '@nestjs/core';
import { KeyGuard } from './keys/guards/key.guard';
import { CacheModule } from '@nestjs/cache-manager';
import { WebhooksModule } from './webhooks/webhooks.module';
import { SchedulesModule } from './schedules/schedules.module';
import { NotificationModule } from './notification/notification.module';
import { StripePaymentModule } from './stripe-payment/stripe-payment.module';
import { ScheduleModule } from '@nestjs/schedule';
import { PaymentsModule } from './payments/payments.module';
import { HealthModule } from './health/health.module';
import { BullModule } from '@nestjs/bullmq';
import { BullBoardModule } from '@bull-board/nestjs';
import { FastifyAdapter } from '@bull-board/fastify';
import { PaymentQueueModule } from './queue/payment-queue/payment-queue.module';
import { EmailQueueModule } from './queue/email-queue/email-queue.module';
import { WhatsappQueueModule } from './queue/whatsapp-queue/whatsapp-queue.module';
import { NotificationQueueModule } from './queue/notification-queue/notification-queue.module';
import {
  QUEUE_ATTEMPTS,
  QUEUE_BACKOFF_DELAY,
  QUEUE_BACKOFF_TYPE,
  QUEUE_REMOVE_ON_COMPLETE,
  QUEUE_REMOVE_ON_FAIL,
  REDIS_HOST,
  REDIS_PORT,
} from '@/constants';
import { BullboardTokenValidatorMiddleware } from '@/middlewares/bullboard-token-validator.middleware';
import { MonthlyInvoicingModule } from './invoicing/monthly-invoicing.module';

@Module({
  imports: [
    BullModule.forRoot({
      connection: {
        host: REDIS_HOST,
        port: parseInt(REDIS_PORT),
      },
      defaultJobOptions: {
        removeOnComplete: parseInt(QUEUE_REMOVE_ON_COMPLETE),
        removeOnFail: parseInt(QUEUE_REMOVE_ON_FAIL),
        attempts: parseInt(QUEUE_ATTEMPTS),
        backoff: {
          type: QUEUE_BACKOFF_TYPE,
          delay: parseInt(QUEUE_BACKOFF_DELAY),
        },
      },
    }),
    BullBoardModule.forRoot({
      route: "/queues",
      adapter: FastifyAdapter
    }),
    ScheduleModule.forRoot(),
    AppGlobalConfigModule,
    CacheModule.register({
      isGlobal: true,
    }),
    AuthModule,
    TypeOrmConfigModule,
    ClientsModule,
    ProductsModule,
    SubscriptionsModule,
    WebhooksModule,
    KeysModule,
    SchedulesModule,
    NotificationModule,
    StripePaymentModule,
    PaymentsModule,
    HealthModule,
    PaymentQueueModule,
    EmailQueueModule,
    WhatsappQueueModule,
    NotificationQueueModule,
    MonthlyInvoicingModule,
  ],
  providers: [
    // KeysService,
    {
      provide: APP_GUARD,
      useClass: KeyGuard,
    },
    AppService
  ],
  controllers: [AppController],
})
export class AppModule {

  configure(consumer: MiddlewareConsumer) {
    // https://github.com/DennisSnijder/nestjs-bull-board/issues/1
    consumer
      .apply(BullboardTokenValidatorMiddleware)
      .forRoutes(
        { path: 'queues/*path', method: RequestMethod.ALL },
        { path: 'queues/', method: RequestMethod.ALL },
        { path: 'queues', method: RequestMethod.ALL },
        { path: '/queues/', method: RequestMethod.ALL },
        { path: '*path/queues/*path', method: RequestMethod.ALL },
      );
    consumer
      .apply(CorrelationIdMiddleware, RequestLoggerMiddleware)
      .forRoutes('*');
  }
}
