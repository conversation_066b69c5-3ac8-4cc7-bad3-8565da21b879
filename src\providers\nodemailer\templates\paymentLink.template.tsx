import {
  Body,
  Button,
  Container,
  // Head,
  Hr,
  // Html,
  // Img,
  // Preview,
  Section,
  Text,
  Tailwind,
  render
} from "@react-email/components";
import * as React from "react";
import { sendEmail, sendEmailCronMX } from '..';
import { PAYMENT_FRONT_URL } from '@/constants';

interface KoalaWelcomeEmailProps {
  name: string;
  url: string;
  bodyText?: string;
  subBodyText?: string;
}

interface SoldPaymentEmailProps {
  name: string;
  url: string;
  paymentId: string;
  email?: string;
}


export const KoalaWelcomeEmail = ({
  name,
  url,
  bodyText,
  subBodyText,
}: KoalaWelcomeEmailProps) => (
  <Tailwind>
    {/* <Head /> */}
    <Body style={main}>
      <Container style={container}>

        <Text style={paragraph}>¡Hola {name}¡</Text>


        {
          bodyText || subBodyText ? (
            <>
              <Text style={paragraph}>
                {bodyText}
              </Text>
              <div className=' flex gap-2 items-center '>

                <Text
                  style={{ ...paragraph, /* verticalAlign: 'middle' */ }}
                  // add center text and icons
                  className=' my-2 mx-0  '
                // className=' my-2 mx-0  '
                >
                  {subBodyText}
                  <span className='align-text-bottom '>🚗</span>
                </Text>
              </div>
            </>
          ) :
            <>

              <Text className='font-bold text-bold text-[18px] text-center ' >
                {/* Adjuntamos el enlace de pago correspondiente a el pago de la renta semanal de la siguiente semana. */}
                Aviso importante
              </Text>

              <Text style={paragraph}>
                {/* Adjuntamos el enlace de pago correspondiente a el pago de la renta semanal de la siguiente semana. */}
                Hicimos un cambio de cuenta de banco a la que se debe depositar.
              </Text>
              <Text style={paragraph} className=' my-2 mx-0 '>
                {/* Recuerda que debes realizarlo el dia lunes antes de las 5pm */}
                Te pedimos revisar la cuenta en el enlace antes de realizar el pago de tu semanalidad ya que en caso de que lo realices a la cuenta anterior será complicado asociarlo a el pago semanal del auto actual.
              </Text>
              <Text style={paragraph} className=' my-4 mx-0 '>
                {/* Recuerda que debes realizarlo el dia lunes antes de las 5pm */}
                Recuerda que debes realizarlo el dia lunes antes de las 5pm
              </Text>
            </>
        }

        { url &&
          <Section style={btnContainer}>
            <Button style={button} href={url}>
              Pagar ahora
            </Button>
          </Section>
        }

        <Text style={paragraph}>
          Saludos,
          <br />
          OneCarNow! team
        </Text>
        <Hr style={hr} />
        <Text style={footer}>
          © {new Date().getFullYear()} OneCarNow! All rights
        </Text>
      </Container>
    </Body>
  </Tailwind>
);

export const SoldPaymentEmail = ({
  name,
  url,
}: SoldPaymentEmailProps) => (
  <Tailwind>
    <Body style={main}>
      <Container style={container}>
        <Text style={paragraph}>¡Hola {name}¡</Text>
        <Text style={paragraph}>
          Ya puedes realizar el pago del monto de compra de tu vehículo.
        </Text>
        <Text style={paragraph}>
          Aquí te dejamos el link para hacerlo:
        </Text>
        <Text style={paragraph}>
          {url}
        </Text>
        <Text style={paragraph}>
          Una vez recibido el pago, te enviaremos el formato de compra - venta para que lo firmes y así podamos finalizar el proceso contigo.
        </Text>
        <Text style={paragraph}>
          ¡Felicidades por este gran paso! Si necesitas algo, estamos aquí para ayudarte.
        </Text>
      </Container>
    </Body>
  </Tailwind>
);

const main = {
  backgroundColor: "#ffffff",
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif',
};

const container = {
  margin: "0 auto",
  padding: "20px 0 48px",
};

const logo = {
  margin: "0 auto",
};

const paragraph = {
  fontSize: "16px",
  lineHeight: "26px",
};

const btnContainer = {
  textAlign: "center" as const,
};

const button = {
  backgroundColor: "#5F51E8",
  borderRadius: "3px",
  color: "#fff",
  fontSize: "16px",
  textDecoration: "none",
  textAlign: "center" as const,
  display: "block",
  padding: "12px",
};

const hr = {
  borderColor: "#cccccc",
  margin: "20px 0",
};

const footer = {
  color: "#8898aa",
  fontSize: "12px",
};


interface PaymentLinkProps {
  email: string;
  paymentId: string;
  name: string;
  subject?: string;
  amount?: number;
}

export default async function paymentLinkEmail(data: PaymentLinkProps) {
  // console.log('sending email')
  // const html = render(KoalaWelcomeEmail({ name: 'PEDRO MARTINEZ, TEST', url: 'https://demo.onecarnow.com/payment/payment-f6J-CsaGxafncIKj' }))

  const url = `${PAYMENT_FRONT_URL}/pago/${data.paymentId}`;

  const amount = data.amount || 0;

  let bodyText = '';
  let subBodyText = '';

  if (amount === 100) {
    bodyText = 'Debido a la falta de pago de la renta semanal, adjuntamos el enlace correspondiente a el desbloqueo'

    subBodyText = 'Recuerda que debes de pagar la renta y el desbloqueo por aparte para poder activar la unidad  '

  }

  const subject = amount === 100 ? 'Enlace de desbloqueo OneCarNow!' : 'Enlace de pago OneCarNow!';

  const html = await render(KoalaWelcomeEmail({ name: data.name, url, bodyText, subBodyText }))


  await sendEmail(data.email, subject, html);
}

export async function soldPaymentEmail(data: SoldPaymentEmailProps) {
  const html = await render(SoldPaymentEmail({ name: data.name, url:data.url, paymentId: data.paymentId, email: data.email }))
  await sendEmail(data.email, 'Enlace de pago OneCarNow!', html)
}


export async function paymentLinkEmailCronMX(data: PaymentLinkProps) {
  // console.log('sending email')
  // const html = render(KoalaWelcomeEmail({ name: 'PEDRO MARTINEZ, TEST', url: 'https://demo.onecarnow.com/payment/payment-f6J-CsaGxafncIKj' }))

  const url = `${PAYMENT_FRONT_URL}/pago/${data.paymentId}`;

  const amount = data.amount || 0;

  let bodyText = '';
  let subBodyText = '';

  if (amount === 100) {
    bodyText = 'Debido a la falta de pago de la renta semanal, adjuntamos el enlace correspondiente a el desbloqueo'

    subBodyText = 'Recuerda que debes de pagar la renta y el desbloqueo por aparte para poder activar la unidad  '

  }

  const subject = amount === 100 ? 'Enlace de desbloqueo OneCarNow!' : 'Enlace de pago OneCarNow!';

  const html = await render(KoalaWelcomeEmail({ name: data.name, url, bodyText, subBodyText }))


  await sendEmailCronMX(data.email, subject, html);
}