import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PaymentsEntity } from '@/app/payments/entities/payment.entity';
import { ClientEntity } from '@/app/clients/entities/client.entity';
import { MonthlyInvoicingService } from './services/monthly-invoicing.service';
import { MonthlyInvoicingController } from './controllers/monthly-invoicing.controller';
import { InvoiceQueueModule } from '../queue/invoice-queue/invoice-queue.module';
import { InvoiceEmailQueueModule } from '../queue/invoice-email-queue/invoice-email-queue.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([PaymentsEntity, ClientEntity]),
    InvoiceQueueModule,
    InvoiceEmailQueueModule,
  ],
  providers: [MonthlyInvoicingService],
  controllers: [MonthlyInvoicingController],
  exports: [MonthlyInvoicingService],
})
export class MonthlyInvoicingModule {}
