import { OnWorkerEvent, Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { NotificationService } from '../../notification/services/notification.service';
import { NOTIFICATION_QUEUE } from '@/constants';

@Processor({
  name: NOTIFICATION_QUEUE
})
export class NotificationProcessor extends WorkerHost {
  private readonly logger = new Logger(NotificationProcessor.name);
  constructor(private readonly notificationService: NotificationService) {
    super();
  }

  async process(job: Job<any, any, string>): Promise<any> {
    const payment = job.data;
    // send notificaiton here
    this.logger.log({
      message: `[NotificationProcessor] - Notificaition is being processed: ${job.data}`
    });
    await this.notificationService.sendPush(
      payment.client,
      'Pago',
      'Se ha generado un nuevo enlace de pago.',
      { 
        type: JSON.stringify({
          operation: 'payment',
          action: 'paymentCreated',
        }),
        link: 'https://pagos.onecarnow.com/pago/' + payment.id
      },
    );
  }

  @OnWorkerEvent('active')
  onActive(job: Job) {
    this.logger.log(`[NotificationProcessor] - Active ${job.id} ${job.data}`);
  }

  @OnWorkerEvent('completed')
  onCompleted(job: Job) {
    this.logger.log(
      `[NotificationProcessor] - Completed ${job.id} ${job.data}`,
    );
  }

  @OnWorkerEvent('failed')
  onFailed(job: Job) {
    this.logger.log(`[NotificationProcessor] - Failed ${job.id} ${job.data}`);
  }
}
