import {
  PaymentsEntity,
} from '../entities/payment.entity';
import { HttpException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SubscriptionEntity } from '@/app/subscriptions/entities/subscription.entity';
import { PaymentTransactionService } from './payment-transactions.service';
import { getLocalDate } from '@/lib/dates';
import { PaymentTransactionStatusEnum } from '../entities/payment-transactions.entity';
import { MarkAsPaidDTO } from '../dto/update-payment.dto';
import { Wire4WebhookDataEntity } from '../entities/wire4-webhook.entity';

@Injectable()
export class UpdatePaymentService {
  constructor(
    @InjectRepository(PaymentsEntity)
    private readonly paymentRepository: Repository<PaymentsEntity>,
    @InjectRepository(SubscriptionEntity)
    private readonly subscriptionRepository: Repository<SubscriptionEntity>,
    @InjectRepository(Wire4WebhookDataEntity)
    private readonly wire4DataRepository: Repository<Wire4WebhookDataEntity>,
    private readonly paymentTransactionService: PaymentTransactionService,
  ) {}

  /**
   * Service to mark a payment as paid from the admin panel
   * @param id Payment ID
   */

  async markAsPaid(paymentDto: MarkAsPaidDTO) {
    const payment = await this.paymentRepository.findOne({
      where: { id: paymentDto.paymentId },
      relations: ['client', 'subscription.products'],
    });
    if (!payment) {
      throw new HttpException('Payment not found', 404);
    }

    // check if payment transactions for this payment id are present
    const paymentTransactions =
      await this.paymentTransactionService.getPaymentTransactionsForPaymentId(
        payment.id,
      );
    
    // loop over the selected transactions
    paymentDto.selectedTransactions.map(async (transaction) => {
      const wire4Payment = await this.wire4DataRepository.findOne({
        where: { id: transaction.id },
      });
      if (paymentTransactions && paymentTransactions.length > 0) {
        // check if those payment transaction contain the same wire4webhook data as the selected transaction
        const existingPaymentTransaction = paymentTransactions.filter(
          (paymentTransaction) => {
            return (
              JSON.stringify(paymentTransaction.monexData) ===
              JSON.stringify(wire4Payment.body.data)
            );
          },
        );
        if (
          existingPaymentTransaction &&
          existingPaymentTransaction.length > 0
        ) {
          //payment transaction already exits
        } else {
          // create payment transaction for those that dont exist
          await this.paymentTransactionService.create({
            payment: payment,
            monexData: wire4Payment.body.data,
            description: 'Pago realizado correctamente',
            amount: wire4Payment.body.data.amount,
            status: PaymentTransactionStatusEnum.success,
          });
        }
      } else {
        // create payment transactions for all selected wire4webhook data
        await this.paymentTransactionService.create({
          payment: payment,
          monexData: wire4Payment.body.data,
          description: 'Pago realizado correctamente',
          amount: wire4Payment.body.data.amount,
          status: PaymentTransactionStatusEnum.success,
        });
      }
    });
    
    // mark the payment as paid
    const today = getLocalDate();
    Object.assign(payment, { isPaid: true, status: 'success', paidAt: today });
    await this.paymentRepository.update(payment.id, payment);
    const hasSubscription = Boolean(payment.subscription);
    const subscription = hasSubscription
      ? null
      : await this.subscriptionRepository.findOne({
          where: { client: { id: payment.client.id } },
        });
    payment.subscription = subscription || payment.subscription;
    if (payment.subscription) {
      await this.subscriptionRepository.update(payment.subscription.id, {
        paymentNumber: payment.subscription.paymentNumber + 1,
      });
    }
    return { message: 'Payment marked as paid' };
  }
}
