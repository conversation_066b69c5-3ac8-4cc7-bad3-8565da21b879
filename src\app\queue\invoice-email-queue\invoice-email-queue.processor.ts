import { OnWorkerEvent, Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import facturapi from '@/facturapi';

const INVOICE_EMAIL_QUEUE = 'invoice-email-queue';

interface InvoiceEmailData {
  invoiceId: string;
  paymentId: string;
  clientEmail: string;
  clientName: string;
}

@Processor({
  name: INVOICE_EMAIL_QUEUE,
})
export class InvoiceEmailProcessor extends WorkerHost {
  private readonly logger = new Logger(InvoiceEmailProcessor.name);

  async process(job: Job<InvoiceEmailData, any, string>): Promise<any> {
    const { invoiceId, clientEmail, clientName, paymentId } = job.data;

    this.logger.log({
      message: `[InvoiceEmailProcessor] - Enviando correo de factura ${invoiceId} para pago ${paymentId} a ${clientEmail}`,
    });

    try {
      // Enviar correo electrónico con la factura
      const result = await facturapi.invoices.sendInvoiceByEmail(invoiceId, {
        email: clientEmail,
        // message: `Estimado(a) ${clientName}, adjuntamos su factura correspondiente al pago ${paymentId}.`
      });

      return { success: true, result };
    } catch (error: any) {
      this.logger.error(`Error al enviar correo de factura ${invoiceId}: ${error.message}`, error.stack);
      return { success: false, error: error.message };
    }
  }

  @OnWorkerEvent('active')
  onActive(job: Job) {
    this.logger.log(`[InvoiceEmailProcessor] - Activo ${job.id}`);
  }

  @OnWorkerEvent('completed')
  onCompleted(job: Job) {
    this.logger.log(`[InvoiceEmailProcessor] - Completado ${job.id}`);
  }

  @OnWorkerEvent('failed')
  onFailed(job: Job, error: Error) {
    this.logger.error(`[InvoiceEmailProcessor] - Fallido ${job.id}: ${error.message}`, error.stack);
  }
}

