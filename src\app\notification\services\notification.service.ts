import { HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Notifications } from '../entities/notification.entity';
import { Repository } from 'typeorm';
import * as firebase from 'firebase-admin';
import * as path from 'path';
import { NotificationToken } from '../entities/notification-token.entity';
import { RegisterNotificationDto } from '../dto/register-notification.dto';
import { UpdateNotificationDto } from '../dto/update-notification.dto';
import { ClientEntity } from '@/app/clients/entities/client.entity';
import * as serviceAccount from '../../../config/firebase-config.json';
import { response } from 'express';

firebase.initializeApp({
  credential: firebase.credential.cert(
    serviceAccount as firebase.ServiceAccount,
  ),
});

@Injectable()
export class NotificationService {
  private readonly logger = new Logger(NotificationService.name);
  constructor(
    @InjectRepository(ClientEntity)
    private readonly clientsRepo: Repository<ClientEntity>,
    @InjectRepository(Notifications)
    private readonly notificationsRepo: Repository<Notifications>,
    @InjectRepository(NotificationToken)
    private readonly notificationTokenRepo: Repository<NotificationToken>,
  ) {}

  registerForPushNotification = async (
    associateId: string,
    notification_dto: RegisterNotificationDto,
  ): Promise<any> => {
    try {
      const client = await this.clientsRepo.findOne({
        where: { associateId: associateId },
      });
      if (!client) {
        throw new HttpException('Client not found', HttpStatus.NOT_FOUND);
      }
      const notificationTokens = await this.notificationTokenRepo.find({
        where: {
          client: { id: client.id },
          notification_token: notification_dto.notification_token,
        },
      });
      if (notificationTokens && notificationTokens.length > 0) {
        return { message: 'Client with token already registered' };
      }
      // save to db
      const notification_token = await this.notificationTokenRepo.save({
        client: client,
        device_type: notification_dto.device_type,
        notification_token: notification_dto.notification_token,
        status: 'ACTIVE',
      });
      return notification_token;
    } catch (error) {
      this.logger.log(error);
      throw new HttpException(
        'Internal Server Error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  };

  activatePushNotification = async (
    associateId: string,
    update_dto: UpdateNotificationDto,
  ): Promise<any> => {
    try {
      const client = await this.clientsRepo.findOne({
        where: { associateId: associateId },
      });
      if (!client) {
        throw new HttpException('Client not found', HttpStatus.NOT_FOUND);
      }
      await this.notificationTokenRepo.update(
        {
          client: { id: client.id },
          notification_token: update_dto.notification_token,
        },
        {
          status: 'ACTIVE',
        },
      );
      return { message: 'Successfully activated push notification' };
    } catch (error) {
      this.logger.log(error);
      throw new HttpException(
        'Internal Server error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  };

  disablePushNotification = async (
    associateId: string,
    update_dto: UpdateNotificationDto,
  ): Promise<any> => {
    try {
      const client = await this.clientsRepo.findOne({
        where: { associateId: associateId },
      });
      if (!client) {
        throw new HttpException('Client not found', HttpStatus.NOT_FOUND);
      }
      await this.notificationTokenRepo.update(
        {
          client: { id: client.id },
          notification_token: update_dto.notification_token,
        },
        {
          status: 'INACTIVE',
        },
      );
      return { message: 'Successfully disabled push notification' };
    } catch (error) {
      this.logger.log(error);
      throw new HttpException(
        'Internal Server error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  };

  getNotifications = async (): Promise<any> => {
    return await this.notificationsRepo.find();
  };

  sendPushViaAssociateId = async (
    associateId: string,
    title: string,
    body: string,
    data: any,
  ) => {
    const client = await this.clientsRepo.findOne({
      where: { associateId: associateId },
    });
    if (!client) {
      throw new HttpException('Client not found', HttpStatus.NOT_FOUND);
    }
    try {
      this.sendPush(client, title, body, data); 
    } catch(error) {
      throw error;
    }
  }

  sendPush = async (
    client: ClientEntity,
    title: string,
    body: string,
    data: any,
  ): Promise<void> => {
    try {
      const notificationTokens = await this.notificationTokenRepo.find({
        where: {
          client: { id: client.id },
          status: 'ACTIVE',
        },
      });
      if (!notificationTokens || notificationTokens.length === 0) {
        throw new HttpException(
          'Device not registered for user',
          HttpStatus.NOT_FOUND,
        );
      }
      notificationTokens.forEach(async (notificationToken) => {
        const notification = await this.notificationsRepo.save({
          notification_token: notificationToken.notification_token,
          title,
          body,
          status: 'pending',
          client: client,
        });
        await firebase
          .messaging()
          .send({
            notification: { title, body },
            data,
            token: notificationToken.notification_token,
            android: { priority: 'high' },
          })
          .then(async (response) => {
            this.logger.log('Successfully sent message:', response);
            await this.notificationsRepo.update(
              { id: notification.id },
              { ...notification, status: 'success' },
            );
          })
          .catch(async (error: any) => {
            this.logger.log(error);
            await this.notificationsRepo.update(
              { id: notification.id },
              { ...notification, status: 'failed' },
            );
          });
      });
    } catch (error) {
      this.logger.log(error);
      throw new HttpException(
        'Internal server error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  };

  sendPushNotificationToClient = async (
    client: ClientEntity,
    title: string,
    body: string,
    data: any,
  ): Promise<void> => {
    const notificationTokens = await this.notificationTokenRepo.find({
      where: {
        client: { id: client.id },
        status: 'ACTIVE',
      },
    });
    if (!notificationTokens || notificationTokens.length === 0) {
      throw new HttpException(
        'Device not registered for user',
        HttpStatus.NOT_FOUND,
      );
    }
    try {
      notificationTokens.forEach(async (notificationToken) => {
        const notification = await this.notificationsRepo.save({
          notification_token: notificationToken.notification_token,
          title,
          body,
          status: 'pending',
          client: client,
        });
        await firebase
          .messaging()
          .send({
            notification: { title, body },
            data,
            token: notificationToken.notification_token,
            android: { priority: 'high' },
          })
          .then(async (response) => {
            this.logger.log('Successfully sent message:', response);
            await this.notificationsRepo.update(
              { id: notification.id },
              { ...notification, status: 'success' },
            );
          })
          .catch(async (error: any) => {
            this.logger.log(error);
            if (error.code === 'messaging/registration-token-not-registered') {
              await this.notificationTokenRepo
                .delete({ id: notification.id })
                .then(() => {
                  this.logger.log(
                    `Invalid token with id ${notification.id} removed from database`,
                  );
                })
                .catch((error) => {
                  this.logger.log(
                    `Unable to delete notification token with id ${notification.id} due to ${error.message}`,
                  );
                });
            } else {
              await this.notificationsRepo.update(
                { id: notification.id },
                { ...notification, status: 'failed' },
              );
            }
          });
      });
    } catch (error) {
      this.logger.log(error);
      throw new HttpException(
        'Internal server error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  };
}
