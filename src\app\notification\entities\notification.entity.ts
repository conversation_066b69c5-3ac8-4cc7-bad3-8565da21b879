import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, PrimaryGeneratedC<PERSON>umn, <PERSON>in<PERSON><PERSON><PERSON>n, ManyToOne, OneToOne } from 'typeorm';
import { NotificationToken } from './notification-token.entity';
import { ClientEntity } from '@/app/clients/entities/client.entity';

@Entity({ name: 'notifications' })
export class Notifications {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column()
  notification_token: string;

  @Column()
  title: string;

  @Column({ nullable: true })
  body: string;

  @ManyToOne(() => ClientEntity, (client) => client.notifications)
  client: ClientEntity;

  @Column()
  status: string;
}