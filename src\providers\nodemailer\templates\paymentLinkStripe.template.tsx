import {
  Body,
  Button,
  Container,
  Hr,
  Section,
  Text,
  Tailwind,
  render,
} from '@react-email/components';
import * as React from 'react';
import { sendEmail } from '..';
import { PAYMENT_FRONT_URL } from '@/constants';

interface KoalaWelcomeEmailProps {
  name: string;
  url: string;
  bodyText?: string;
  subBodyText?: string;
}

export const KoalaWelcomeEmail = ({
  name,
  url,
  bodyText,
  subBodyText,
}: KoalaWelcomeEmailProps) => (
  <Tailwind>
    {/* <Head /> */}
    <Body style={main}>
      <Container style={container}>
        <Text style={paragraph}>Hi {name}¡</Text>

        {bodyText || subBodyText ? (
          <>
            <Text style={paragraph}>{bodyText}</Text>
            <div className=" flex gap-2 items-center ">
              <Text style={{ ...paragraph }} className=" my-2 mx-0">
                {subBodyText}
                <span className="align-text-bottom ">🚗</span>
              </Text>
            </div>
          </>
        ) : (
          <>
            <Text className="font-bold text-bold text-[18px] text-center ">
              Please link your Stripe ACH Debit with us
            </Text>
          </>
        )}

        <Section style={btnContainer}>
          <Button style={button} href={url}>
            Link your Stripe ACH Debit with us
          </Button>
        </Section>
        <Text style={paragraph}>
          Greetings,
          <br />
          OneCarNow! team
        </Text>
        <Hr style={hr} />
        <Text style={footer}>
          © {new Date().getFullYear()} OneCarNow! All rights
        </Text>
      </Container>
    </Body>
  </Tailwind>
);

const main = {
  backgroundColor: '#ffffff',
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif',
};

const container = {
  margin: '0 auto',
  padding: '20px 0 48px',
};

const logo = {
  margin: '0 auto',
};

const paragraph = {
  fontSize: '16px',
  lineHeight: '26px',
};

const btnContainer = {
  textAlign: 'center' as const,
};

const button = {
  backgroundColor: '#5F51E8',
  borderRadius: '3px',
  color: '#fff',
  fontSize: '16px',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'block',
  padding: '12px',
};

const hr = {
  borderColor: '#cccccc',
  margin: '20px 0',
};

const footer = {
  color: '#8898aa',
  fontSize: '12px',
};

interface StripeAccountLinkProps {
  email: string;
  clientId: string;
  name: string;
  subject?: string;
}

export default async function stripeAccountLinkEmail(
  data: StripeAccountLinkProps,
) {
  const url = `${PAYMENT_FRONT_URL}/stripe-account-link/${data.clientId}`;
  const bodyText =
    'Please link your Stripe ACH Debit with us to activate your account';
  const subBodyText =
    'Remember that you must link your Stripe ACH Debit with us to activate your account and start using our services.';
  const subject = 'OneCarNow! Stripe ACH Debit Bank Account Linking';

  const html = await render(
    KoalaWelcomeEmail({ name: data.name, url, bodyText, subBodyText }),
  );
  await sendEmail(data.email, subject, html);
}
