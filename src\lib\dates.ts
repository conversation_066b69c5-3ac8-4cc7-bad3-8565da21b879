import { isThursday } from 'date-fns';
import { DateTime } from 'luxon';

export function getLocalDate(date: Date = new Date(), customTime?: { hours?: number, minutes?: number, seconds?: number, ms?: number }): Date {

  const isoString = date.toISOString();

  if (isoString.endsWith('T00:00:00.000Z') || customTime?.hours || customTime?.minutes || customTime?.seconds) {
    // console.log('ISO STRING', isoString)
    if (customTime) {
      // date.setHours(customTime.hours || 0);
      // date.setMinutes(customTime.minutes || 0);
      // date.setSeconds(customTime.seconds || 0);
      // date.setMilliseconds(customTime.ms || 0);
      // console.log('date', date)

      const hour = (customTime.hours || 0).toString().padStart(2, '0');
      const minute = (customTime.minutes || 0).toString().padStart(2, '0');
      const second = (customTime.seconds || 0).toString().padStart(2, '0');
      const ms = (customTime.ms || 0).toString().padStart(3, '0');

      const localDate = new Date(`${isoString.slice(0, 10)}T${hour}:${minute}:${second}.${ms}Z`);
      return localDate;
    }

    return date;
  }

  const offset = date.getTimezoneOffset();

  const desplazamientoMilisegundos = offset * 60 * 1000;
  const fechaLocal = new Date(date.getTime() - desplazamientoMilisegundos);

  return fechaLocal;
}

export function getTimezoneDate({
  timezone,
  hour,
  minute,
  fromIsoString,
  fromDate,
}: {
  timezone?: string;
  fromIsoString?: string;
  fromDate?: Date;
  hour?: number;
  minute?: number;
} = { timezone: 'America/Mexico_City' }) {

  // const now = DateTime.now().setZone(timezone).set({ hour, minute }).toISO()

  if (fromIsoString) {
    const iso = DateTime.fromISO(fromIsoString).setZone(timezone).set({ hour, minute }).toISO()!.split('-').slice(0, 3).join('-').toString() + 'Z';

    console.log('iso', iso, new Date(iso));
    return new Date(iso);
  }

  if (fromDate) {
    const date = DateTime.fromJSDate(fromDate).setZone(timezone).set({ hour, minute }).toISO()!.split('-').slice(0, 3).join('-').toString() + 'Z';
    console.log('date', date, new Date(date));
    return new Date(date);
  }

  const now = DateTime.now().setZone(timezone).set({ hour, minute }).toISO()!.split('-').slice(0, 3).join('-').toString() + 'Z';

  console.log('now', now, new Date(now));
  return new Date(now);
}

// getTimezoneDate({
//   fromIsoString: '2024-09-30',
//   hour: 23,
//   minute: 59,
// });

export const getLastDay = () => {
  const today = getLocalDate();
  const yesterday = new Date(today);
  yesterday.setDate(today.getDate() - 1);
  return yesterday;
}

export const getLastThursday = (date: Date = getLocalDate()) => {

  while (!isThursday(date)) {
    date.setDate(date.getDate() - 1);
  }

  return date;
}



export const getLastMonth = () => {
  const today = getLocalDate();
  const firstDay = getLocalDate(new Date(today.getFullYear(), today.getMonth() - 1, 1));
  const lastDay = getLocalDate(new Date(today.getFullYear(), today.getMonth(), 0));
  return { firstDay, lastDay };
}

export const getLastYear = () => {
  const today = getLocalDate();
  const firstDay = getLocalDate(new Date(today.getFullYear() - 1, 0, 1));
  const lastDay = getLocalDate(new Date(today.getFullYear(), 0, 0));
  return { firstDay, lastDay };
}


export function getFirstDayOfCurrentMonth({
  month,
  year,
}: {
  month?: number;
  year?: number;
} = {}) {
  year = year || new Date().getFullYear();
  if (month) {
    return getLocalDate(new Date(year, month - 1, 1));
  }


  const today = getLocalDate(undefined, {
    hours: 23,
    minutes: 59,
  });
  const firstDay = getLocalDate(new Date(today.getFullYear(), today.getMonth(), 1));
  return firstDay;
}
