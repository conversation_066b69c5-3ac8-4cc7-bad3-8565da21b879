version: '3.8'

name: payments

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: payments
    env_file:
      - .env
    ports:
      - "${PORT}:${PORT}"
    depends_on:
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:${PORT}/health"] # Ensure this endpoint exists in your NestJS app
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    volumes:
      - ./logs:/var/log/app
      - ./app:/usr/src/app
    networks:
      - default
