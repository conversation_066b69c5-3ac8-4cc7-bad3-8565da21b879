import { Controller, Get, Post, Body, Patch, Param, Delete, Res, Query } from '@nestjs/common';
import { CreateProductDto } from '../dto/create-product.dto';
import { UpdateProductDto } from '../dto/update-product.dto';
import { Response } from 'express';
import { tryCatchResponse } from '@/lib/defaultResponse';
import { ApiTags } from '@nestjs/swagger';
import { ProductsService } from '../services/products.service';
import { FindAllProductsQuery } from '../dto/find-product.dto';

@ApiTags('products')
@Controller('products')
export class ProductsController {
  constructor(private readonly productsService: ProductsService) { }

  @Post()
  async create(@Body() product: CreateProductDto, @Res() res: Response) {
    return await tryCatchResponse(res, () => this.productsService.create(product));
  }

  @Get()
  async findAll(@Res() res: Response, @Query() query: FindAllProductsQuery) {
    return await tryCatchResponse(res, () => this.productsService.findAll(query));
  }

  @Get(':id')
  async findOne(@Param('id') id: string, @Res() res: Response) {
    return tryCatchResponse(res, () => this.productsService.findOne(id));
  }

  @Patch(':id')
  async update(@Param('id') id: string, @Body() updateProductDto: UpdateProductDto, @Res() res: Response) {
    return await tryCatchResponse(res, () => this.productsService.update(id, updateProductDto));
  }

  @Delete(':id')
  async remove(@Param('id') id: string, @Res() res: Response) {
    return await tryCatchResponse(res, () => this.productsService.remove(id));
  }
}