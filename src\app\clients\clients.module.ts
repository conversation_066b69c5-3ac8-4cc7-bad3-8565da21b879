import { <PERSON><PERSON><PERSON>, OnApplicationBootstrap, OnModuleInit } from '@nestjs/common';
import { ClientsService } from './services/clients.service';
import { ClientsController } from './controllers/clients.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ClientEntity } from './entities/client.entity';
import { ClientsUpdater } from './updates/clients.updates';
import { RegionsEnum } from '@/types/entities.types';
// import { ClientsUpdaterV2 } from './updates/clients-update-v2';
import { SubscriptionEntity } from '../subscriptions/entities/subscription.entity';
import { ProductEntity } from '../products/entities/product.entity';
import { SubscriptionProductEntity } from '../subscription-products/entities/subscription-product.entity';
import { NotificationModule } from '../notification/notification.module';
import { StripePaymentModule } from '../stripe-payment/stripe-payment.module';
import { SubscriptionsModule } from '../subscriptions/subscriptions.module';
import { PaymentsEntity } from '../payments/entities/payment.entity';
import { MongoDbModule } from '@/db/mongodb.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([ClientEntity, SubscriptionEntity, ProductEntity, SubscriptionProductEntity, PaymentsEntity]),
    NotificationModule,
    // SubscriptionsModule,
    StripePaymentModule,
    MongoDbModule,
  ],
  controllers: [ClientsController],
  providers: [ClientsService, ClientsUpdater, /* ClientsUpdaterV2 */],
  exports: [ClientsService],
})
export class ClientsModule implements OnApplicationBootstrap {
  constructor(
    private readonly clientUpdater: ClientsUpdater,
    // private readonly clientUpdaterV2: ClientsUpdaterV2
  ) { }
  async onApplicationBootstrap() {
    // console.log('onApplicationBootstrap');

    // const missing = ['2108']

    // await this.clientUpdaterV2.getMongoDBData(RegionsEnum.GDL, missing);

    // await this.clientUpdaterV2.createSubscriptionsForClientsWithoutSubscriptions(RegionsEnum.GDL, missing);

    // await this.clientUpdater.getGigstackUsers(RegionsEnum.QRO);
    /* EXAMPLE: */
    // await this.clientUpdater.updateData(); 
  }

}
