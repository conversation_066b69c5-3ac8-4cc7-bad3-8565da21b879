import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
} from 'typeorm';
import { StripeCustomerInfoEntity } from './stripe-customer.entity';

export enum SetupIntentStatus {
  INITIATED = 'INITIATED',
  SUCCEEDED = 'SUCCEEDED',
  FAILED = 'FAILED',
}

@Entity('stripe_setup_intent')
export class StripeSetupIntentEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(
    () => StripeCustomerInfoEntity,
    (stripeCustomerInfo) => stripeCustomerInfo.stripeSetupIntent,
  )
  stripeCustomerInfo: StripeCustomerInfoEntity;

  @Column({
    type: 'varchar',
    nullable: true,
  })
  stripeSetupIntentId: string;

  @Column({
    type: 'jsonb',
    nullable: true,
  })
  stripeSetupIntentObj: object;

  @Column({
    type: 'enum',
    enum: SetupIntentStatus,
    default: SetupIntentStatus.INITIATED,
  })
  status: SetupIntentStatus;

  @CreateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  createdAt: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  updatedAt: Date;

  @Column({
    type: 'timestamp',
    nullable: true,
  })
  deletedAt: Date;
}
