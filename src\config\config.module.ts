import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { configLoader } from './config-loader';
import { NODE_ENV } from '@/constants';

@Module({
  imports: [
    ConfigModule.forRoot({
      load: [configLoader],
      envFilePath: [`env/.env.${NODE_ENV}`, '.env'],
      isGlobal: true,
    }),
  ],
  exports: [ConfigModule],
})
export class AppGlobalConfigModule { }