import { IsDefined, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>al, <PERSON>String, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>teI<PERSON> } from 'class-validator';

export class FindPaymentInvoicesDto {

  @IsUUID()
  @ValidateIf((object) => !object.facturapiId)
  readonly clientId: string;


  @IsString()
  @ValidateIf((object) => !object.clientId)
  readonly facturapiId: string;

}


export class FindPaymentReceiptsDto {

  @IsUUID()
  @ValidateIf((object) => !object.facturapiId)
  readonly clientId: string;

  @IsString()
  @ValidateIf((object) => !object.clientId)
  readonly facturapiId: string;

}


export class TypeOfFileDto {

  @IsOptional()
  @IsEnum(['pdf', 'xml'])
  readonly type: 'pdf' | 'xml';

}