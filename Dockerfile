FROM node:20 as builder

WORKDIR /myapp
COPY package.json .
COPY prisma ./prisma

ENV PRISMA_BINARY_TARGETS='["linux-musl-openssl-3.0.x"]'
RUN npm install

COPY . .
RUN npm run build
RUN npm run uninstallDevDependencies

# Etapa 2:

FROM node:20-alpine

WORKDIR /myapp

COPY --from=builder /myapp/build ./build
COPY --from=builder /myapp/node_modules ./node_modules
COPY --from=builder /myapp/package*.json ./
COPY --from=builder /myapp/uploads ./uploads

EXPOSE 4000

CMD ["npm", "start"]