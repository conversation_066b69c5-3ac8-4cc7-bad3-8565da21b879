name:  Deploy CI/CD Pipeline 🚀
on:
  push:
    branches: [ "master", "develop", "staging" ]
  pull_request:
    branches: [ "master", "staging" ]

jobs:
  create-docker-image:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [18.x]

    steps:
      - name: Checkout source
        uses: actions/checkout@v3

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v2
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.TOKEN_DEPLOY }}

      - name: Determine Branch Name
        id: branch_name
        run: echo ::set-output name=branch::$(basename ${{ github.ref }})

      - name: Build Docker Image
        run: |
          if [ "${{ steps.branch_name.outputs.branch }}" == "master" ]; then
            docker build . --tag ghcr.io/one-car-now/payments:latest
          
          elif [ "${{ steps.branch_name.outputs.branch }}" == "staging" ]; then
            docker build . --tag ghcr.io/one-car-now/payments-staging:latest

          else
            docker build . --tag ghcr.io/one-car-now/payments-dev:latest
          fi

      - name: Push Docker Image
        run: |
          if [ "${{ steps.branch_name.outputs.branch }}" == "master" ]; then
            docker push ghcr.io/one-car-now/payments:latest
          
          elif [ "${{ steps.branch_name.outputs.branch }}" == "staging" ]; then
            docker push ghcr.io/one-car-now/payments-staging:latest

          else
            docker push ghcr.io/one-car-now/payments-dev:latest
          fi
    
  devDeploy: 
    if: github.ref == 'refs/heads/develop'
    needs: create-docker-image
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up SSH
        run: |
            mkdir -p ~/.ssh
            echo "${{ vars.PRIVATE_KEY_DEV }}" > ~/.ssh/id_rsa
            chmod 600 ~/.ssh/id_rsa
            ssh-keyscan -t rsa ${{ vars.DEV_IP }} >> ~/.ssh/known_hosts

      - name: SSH to remote server and execute script
        run: |
          ssh -i ~/.ssh/id_rsa -o "StrictHostKeyChecking=no" ${{ vars.AUTH_SERVER }} << EOF
          echo "SSH into server..."

          echo "Docker Login..."
          docker login ghcr.io -u ${{ github.actor }} -p ${{ secrets.TOKEN_DEPLOY }}

          echo "Delete old container..."
          CONTAINER_NAME="payments-dev"
          CONTAINER_NAME_2="payments-dev-2"
          CONTAINER_NAME_REDIS="redis"

          echo "Check and create if network payments-network does not exists"
          if docker network ls --filter name=payments-network | grep -q payments-network; then
            echo "payments-network exists."
          else
            docker network create \
            --driver bridge \
            payments-network
            echo "payments-network created."
          fi

          echo "Checking Redis container..."
          if docker ps --format '{{.Names}}' | grep -wq "${CONTAINER_NAME_REDIS}"; then
            echo "✅ Redis is already running. No changes needed."
          elif docker ps -a --format '{{.Names}}' | grep -wq "${CONTAINER_NAME_REDIS}"; then
            echo "⏳ Redis exists but is stopped. Restarting it..."
            docker start ${CONTAINER_NAME_REDIS}
          else
            echo "❌ Redis container does not exist. Creating a new one..."
            docker run --name redis -d -v redis-data:/data --network payments-network -p 6379:6379 --restart unless-stopped redis:latest
          fi

          echo "Getting the latest image update..."
          docker pull ghcr.io/one-car-now/payments-dev:latest

          # Verificar si el PRIMER contenedor existe antes de intentar detenerlo y eliminarlo
          if docker ps -a --format '{{.Names}}' | grep -Eq "^\$CONTAINER_NAME$"; then
            docker stop \$CONTAINER_NAME
            docker rm \$CONTAINER_NAME
            echo "Container \$CONTAINER_NAME stopped and removed."
          else
            echo "Container \$CONTAINER_NAME does not exist."
          fi

          docker run -d -p 4000:4000 \
          --name \$CONTAINER_NAME \
          --env-file ${{ vars.ENV_PATH }} \
          -e RUN_MX_CRON_JOB=true \
          --network payments-network \
          ghcr.io/one-car-now/payments-dev:latest

          # Esperar 1 minuto
          echo "Waiting for 6 seconds to completly start the server..."
          sleep 20


          # Verificar si el SEGUNDO contenedor existe antes de intentar detenerlo y eliminarlo
          if docker ps -a --format '{{.Names}}' | grep -Eq "^\$CONTAINER_NAME_2$"; then
          docker stop \$CONTAINER_NAME_2
          docker rm \$CONTAINER_NAME_2
          echo "Container \$CONTAINER_NAME_2 stopped and removed."
          else
            echo "Container \$CONTAINER_NAME_2 does not exist."
          fi

          docker run -d -p 4001:4001 \
            --name \$CONTAINER_NAME_2 \
            --env-file ${{ vars.ENV_PATH }} \
            -e PORT=4001 \
            --network payments-network \
            ghcr.io/one-car-now/payments-dev:latest

          echo "Clean Docker artifacts..."
          # Eliminar contenedores detenidos
          docker container prune -f

          # Eliminar imágenes sin etiqueta, si las hay
          DOCKER_IMAGES=\$(docker images -q -f "dangling=true")
          if [ -n "\$DOCKER_IMAGES" ]; then
            echo "Eliminando imágenes sin etiqueta..."
            echo "\$DOCKER_IMAGES" | xargs docker rmi -f
          else
            echo "No hay imágenes sin etiqueta para eliminar."
          fi
          EOF

  prodDeploy: 
    if: github.ref == 'refs/heads/master'
    needs: create-docker-image
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up SSH
        run: |
            mkdir -p ~/.ssh
            echo "${{ vars.PRIVATE_KEY_PROD }}" > ~/.ssh/id_rsa
            chmod 600 ~/.ssh/id_rsa
            ssh-keyscan -t rsa ${{ vars.PROD_IP }} >> ~/.ssh/known_hosts

      - name: SSH to remote server and execute script
        run: |
          ssh -i ~/.ssh/id_rsa -o "StrictHostKeyChecking=no" ${{ vars.AUTH_SERVER_PROD }} << EOF
          echo "SSH into server..."

          echo "Docker Login..."
          docker login ghcr.io -u ${{ github.actor }} -p ${{ secrets.TOKEN_DEPLOY }}

          echo "Delete old container..."
          CONTAINER_NAME="payments"
          CONTAINER_NAME_2="payments-2"
          CONTAINER_NAME_REDIS="redis"

          echo "Check and create if network payments-network does not exists"
          if docker network ls --filter name=payments-network | grep -q payments-network; then
            echo "payments-network exists."
          else
            docker network create \
            --driver bridge \
            payments-network
            echo "payments-network created."
          fi

          echo "Checking Redis container..."
          CONTAINER_NAME_REDIS="redis"
          if docker ps --format '{{.Names}}' | grep -w "^${CONTAINER_NAME_REDIS}$"; then
            echo "✅ Redis is already running. No changes needed."
          elif docker ps -a --format '{{.Names}}' | grep -w "^${CONTAINER_NAME_REDIS}$"; then
            echo "⏳ Redis exists but is stopped. Restarting it..."
            docker start ${CONTAINER_NAME_REDIS}
          else
            echo "❌ Redis container does not exist. Creating a new one..."
            docker run --name redis -d -v redis-data:/data --network payments-network -p 6379:6379 --restart unless-stopped redis:latest
          fi
          
          echo "Getting the latest image update..."
          docker pull ghcr.io/one-car-now/payments:latest

          # Verificar si el PRIMER contenedor existe antes de intentar detenerlo y eliminarlo
          if docker ps -a --format '{{.Names}}' | grep "^\$CONTAINER_NAME$"; then
            docker stop \$CONTAINER_NAME
            docker rm \$CONTAINER_NAME
            echo "Container \$CONTAINER_NAME stopped and removed."
          else
            echo "Container \$CONTAINER_NAME does not exist."
          fi


          docker run -d -p 4000:4000 \
            --name \$CONTAINER_NAME \
            --env-file ${{ vars.ENV_PATH }} \
            -e RUN_MX_CRON_JOB=true \
            --network payments-network \
            ghcr.io/one-car-now/payments:latest

          # Esperar 1 minuto
          echo "Waiting for 6 seconds to start first container..."
          sleep 20


          # Verificar si el SEGUNDO contenedor existe antes de intentar detenerlo y eliminarlo
          if docker ps -a --format '{{.Names}}' | grep "^\$CONTAINER_NAME_2$"; then
            docker stop \$CONTAINER_NAME_2
            docker rm \$CONTAINER_NAME_2
            echo "Container \$CONTAINER_NAME_2 stopped and removed."
          else
            echo "Container \$CONTAINER_NAME_2 does not exist."
          fi

          docker run -d -p 4001:4001 \
            --name \$CONTAINER_NAME_2 \
            --env-file ${{ vars.ENV_PATH }} \
            -e PORT=4001 \
            --network payments-network \
            ghcr.io/one-car-now/payments:latest

          echo "Clean Docker artifacts..."
          # Eliminar contenedores detenidos
          docker container prune -f

          # Eliminar imágenes sin etiqueta, si las hay
          DOCKER_IMAGES=\$(docker images -q -f "dangling=true")
          if [ -n "\$DOCKER_IMAGES" ]; then
            echo "Eliminando imágenes sin etiqueta..."
            echo "\$DOCKER_IMAGES" | xargs docker rmi -f
          else
            echo "No hay imágenes sin etiqueta para eliminar."
          fi
          EOF
          
  DRDeploy: 
    if: github.ref == 'refs/heads/master'
    needs: create-docker-image
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up SSH
        run: |
            mkdir -p ~/.ssh
            echo "${{ vars.PRIVATE_KEY_DR }}" > ~/.ssh/id_rsa
            chmod 600 ~/.ssh/id_rsa
            ssh-keyscan -t rsa ${{ vars.DR_IP }} >> ~/.ssh/known_hosts

      - name: SSH to remote server and execute script
        run: |
          ssh -i ~/.ssh/id_rsa -o "StrictHostKeyChecking=no" ${{ vars.AUTH_SERVER_DR }} << EOF
          echo "SSH into server..."

          echo "Docker Login..."
          docker login ghcr.io -u ${{ github.actor }} -p ${{ secrets.TOKEN_DEPLOY }}

          echo "Delete old container..."
          CONTAINER_NAME="payments-DR"
          CONTAINER_NAME_2="payments-DR-2"
          CONTAINER_NAME_REDIS="redis"

          echo "Check and create if network payments-network does not exists"
          if docker network ls --filter name=payments-network | grep -q payments-network; then
            echo "payments-network exists."
          else
            docker network create \
            --driver bridge \
            payments-network
            echo "payments-network created."
          fi

          echo "Checking Redis container..."
          CONTAINER_NAME_REDIS="redis"
          if docker ps --format '{{.Names}}' | grep -w "^${CONTAINER_NAME_REDIS}$"; then
            echo "✅ Redis is already running. No changes needed."
          elif docker ps -a --format '{{.Names}}' | grep -w "^${CONTAINER_NAME_REDIS}$"; then
            echo "⏳ Redis exists but is stopped. Restarting it..."
            docker start ${CONTAINER_NAME_REDIS}
          else
            echo "❌ Redis container does not exist. Creating a new one..."
            docker run --name redis -d -v redis-data:/data --network payments-network -p 6379:6379 --restart unless-stopped redis:latest
          fi
          
          echo "Getting the latest image update..."
          docker pull ghcr.io/one-car-now/payments:latest

          # Verificar si el PRIMER contenedor existe antes de intentar detenerlo y eliminarlo
          if docker ps -a --format '{{.Names}}' | grep "^\$CONTAINER_NAME$"; then
            docker stop \$CONTAINER_NAME
            docker rm \$CONTAINER_NAME
            echo "Container \$CONTAINER_NAME stopped and removed."
          else
            echo "Container \$CONTAINER_NAME does not exist."
          fi


          docker run -d -p 4000:4000 \
            --name \$CONTAINER_NAME \
            --env-file ${{ vars.ENV_PATH }} \
            --network payments-network \
            ghcr.io/one-car-now/payments:latest

          # Esperar 1 minuto
          echo "Waiting for 6 seconds to start first container..."
          sleep 20


          # Verificar si el SEGUNDO contenedor existe antes de intentar detenerlo y eliminarlo
          if docker ps -a --format '{{.Names}}' | grep "^\$CONTAINER_NAME_2$"; then
            docker stop \$CONTAINER_NAME_2
            docker rm \$CONTAINER_NAME_2
            echo "Container \$CONTAINER_NAME_2 stopped and removed."
          else
            echo "Container \$CONTAINER_NAME_2 does not exist."
          fi

          docker run -d -p 4001:4001 \
            --name \$CONTAINER_NAME_2 \
            --env-file ${{ vars.ENV_PATH }} \
            -e PORT=4001 \
            --network payments-network \
            ghcr.io/one-car-now/payments:latest

          echo "Clean Docker artifacts..."
          # Eliminar contenedores detenidos
          docker container prune -f

          # Eliminar imágenes sin etiqueta, si las hay
          DOCKER_IMAGES=\$(docker images -q -f "dangling=true")
          if [ -n "\$DOCKER_IMAGES" ]; then
            echo "Eliminando imágenes sin etiqueta..."
            echo "\$DOCKER_IMAGES" | xargs docker rmi -f
          else
            echo "No hay imágenes sin etiqueta para eliminar."
          fi
          EOF

  stagingDeploy: 
    if: github.ref == 'refs/heads/staging'
    needs: create-docker-image
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up SSH
        run: |
            mkdir -p ~/.ssh
            echo "${{ vars.PRIVATE_KEY_STAGING }}" > ~/.ssh/id_rsa
            chmod 600 ~/.ssh/id_rsa
            ssh-keyscan -t rsa ${{ vars.STAGING_IP }} >> ~/.ssh/known_hosts

      - name: SSH to remote server and execute script
        run: |
          ssh -i ~/.ssh/id_rsa -o "StrictHostKeyChecking=no" ${{ vars.AUTH_SERVER_STAGING }} << EOF
          echo "SSH into server..."

          echo "Docker Login..."
          docker login ghcr.io -u ${{ github.actor }} -p ${{ secrets.TOKEN_DEPLOY }}

          echo "Delete old container..."
          CONTAINER_NAME="payments-staging"
          CONTAINER_NAME_2="payments-staging-2"
          CONTAINER_NAME_REDIS="redis"

          echo "Check and create if network payments-network does not exists"
          if docker network ls --filter name=payments-network | grep -q payments-network; then
            echo "payments-network exists."
          else
            docker network create \
            --driver bridge \
            payments-network
            echo "payments-network created."
          fi

          echo "Checking Redis container..."
          CONTAINER_NAME_REDIS="redis"
          if docker ps --format '{{.Names}}' | grep -w "^${CONTAINER_NAME_REDIS}$"; then
            echo "✅ Redis is already running. No changes needed."
          elif docker ps -a --format '{{.Names}}' | grep -w "^${CONTAINER_NAME_REDIS}$"; then
            echo "⏳ Redis exists but is stopped. Restarting it..."
            docker start ${CONTAINER_NAME_REDIS}
          else
            echo "❌ Redis container does not exist. Creating a new one..."
            docker run --name redis -d -v redis-data:/data --network payments-network -p 6379:6379 --restart unless-stopped redis:latest
          fi
          
          echo "Getting the latest image update..."
          docker pull ghcr.io/one-car-now/payments-staging:latest

          # Verificar si el PRIMER contenedor existe antes de intentar detenerlo y eliminarlo
          if docker ps -a --format '{{.Names}}' | grep "^\$CONTAINER_NAME$"; then
            docker stop \$CONTAINER_NAME
            docker rm \$CONTAINER_NAME
            echo "Container \$CONTAINER_NAME stopped and removed."
          else
            echo "Container \$CONTAINER_NAME does not exist."
          fi


          docker run -d -p 4000:4000 \
            --name \$CONTAINER_NAME \
            --env-file ${{ vars.ENV_PATH }} \
            --network payments-network \
            ghcr.io/one-car-now/payments-staging:latest

          # Esperar 1 minuto
          echo "Waiting for 6 seconds to start first container..."
          sleep 20


          # Verificar si el SEGUNDO contenedor existe antes de intentar detenerlo y eliminarlo
          if docker ps -a --format '{{.Names}}' | grep "^\$CONTAINER_NAME_2$"; then
            docker stop \$CONTAINER_NAME_2
            docker rm \$CONTAINER_NAME_2
            echo "Container \$CONTAINER_NAME_2 stopped and removed."
          else
            echo "Container \$CONTAINER_NAME_2 does not exist."
          fi

          docker run -d -p 4001:4001 \
            --name \$CONTAINER_NAME_2 \
            --env-file ${{ vars.ENV_PATH }} \
            -e PORT=4001 \
            --network payments-network \
            ghcr.io/one-car-now/payments-staging:latest

          echo "Clean Docker artifacts..."
          # Eliminar contenedores detenidos
          docker container prune -f

          # Eliminar imágenes sin etiqueta, si las hay
          DOCKER_IMAGES=\$(docker images -q -f "dangling=true")
          if [ -n "\$DOCKER_IMAGES" ]; then
            echo "Eliminando imágenes sin etiqueta..."
            echo "\$DOCKER_IMAGES" | xargs docker rmi -f
          else
            echo "No hay imágenes sin etiqueta para eliminar."
          fi
          EOF