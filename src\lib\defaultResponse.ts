import { HttpException } from '@nestjs/common';
import { Response } from 'express';
// import { HttpException, HttpStatus } from '@nestjs/common';

/** 
 * Wraps the callback in a try-catch block and sends the response to the client.
 * @param res The express response object.
 * @param callback The callback function to be executed.
*/

export async function tryCatchResponse(res: Response, callback: () => Promise<any>) {
  try {
    const response = await callback();
    return res.status(200).send({ statusCode: 200, ...response });
    // return await callback(); // this doesn't work
  } catch (error) {
    if (error instanceof HttpException) {
      // Si el error es del tipo HttpException, lo lanzamos con el mismo status y mensaje
      console.error('error', error);
      return res.status(error.getStatus()).send(error);
    } else {
      // Si es otro tipo de error, lanzamos un 500 Internal Server Error con el mensaje correspondiente
      console.error('error', error);
      return res.status(500).send({ statusCode: 500, message: 'Internal server error', error });
    }

    // return res.status(500).send({ message: 'Internal server error', error });
    // throw new HttpException({
    //   // status: HttpStatus.INTERNAL_SERVER_ERROR,
    //   message: 'Internal server error',
    //   error
    //   // error: 'This is a custom message',
    // }, HttpStatus.INTERNAL_SERVER_ERROR, {
    //   cause: error
    // });
  }
}