import { Modu<PERSON>, OnModuleInit } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';

@Module({
  imports: [
    // TypeOrmModule.forRoot({
    //   type: 'postgres',
    //   host: process.env.DB_HOST,
    //   port: parseInt(process.env.DB_PORT) || 3306,
    //   username: process.env.DB_USER,
    //   password: process.env.DB_PASSWORD,
    //   database: process.env.DB_NAME,
    //   entities: [__dirname + '/../**/*.entity.{js,ts}'],
    //   synchronize: process.env.NODE_ENV !== 'production',
    //   autoLoadEntities: true,

    // }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        return {
          type: 'postgres',
          host: configService.get('database.host'),
          port: configService.get('database.port'),
          username: configService.get('database.username'),
          password: configService.get('database.password'),
          database: configService.get('database.name'),
          entities: [__dirname + '/../**/*.entity.{js,ts}'],
          subscribers: [__dirname + '/../**/*.subscriber.{js,ts}'],
          // subscribers: ['/../**/*.subscriber{.ts,.js}'],
          // subscribers: ['dist/**/*.subscriber{.ts,.js}'],
          synchronize: true,
          retryAttempts: 2,
          autoLoadEntities: true,
        }
      },
    })
  ],
})
export class TypeOrmConfigModule implements OnModuleInit {

  async onModuleInit() {
    setTimeout(() => {
      console.log('Conexión a la base de datos establecida correctamente');
    }, 1000);
  }

}