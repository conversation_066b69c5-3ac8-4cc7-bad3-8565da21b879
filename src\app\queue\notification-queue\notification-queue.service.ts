import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { JOB_SEND_NOTIFICATION, NOTIFICATION_QUEUE } from '@/constants';

@Injectable()
export class NotificationQueueService {
  private readonly logger = new Logger(NotificationQueueService.name);
  constructor(
    @InjectQueue(NOTIFICATION_QUEUE) private notificationQueue: Queue,
  ) {}

  async sendNotification(data: any) {
    this.logger.log({
      message:
        `[NotificationQueueService] - Notificaiton added to queue for sending: ${data}`
    });
    const job = await this.notificationQueue.add(JOB_SEND_NOTIFICATION, data);
    return job.id; // Return the job ID for tracking
  }
}
