import { Injectable, NestMiddleware } from '@nestjs/common';
import { NextFunction, Request } from 'express';
import { ServerResponse } from 'http';
import jwt from 'jsonwebtoken';
@Injectable()
export class BullboardTokenValidatorMiddleware implements NestMiddleware {
  use(req: Request, res: ServerResponse, next: NextFunction) {

    const cookieHeader = req.headers.cookie;
    const cookies =
      cookieHeader?.split(';').reduce(
        (acc, cookie) => {
          const [key, value] = cookie.trim().split('=');
          acc[key] = value;
          return acc;
        },
        {} as Record<string, string>,
      ) || {};
    const token = cookies['bull-board-token'];
    if (!token) {
      res.statusCode = 401;
      res.setHeader('Content-Type', 'application/json');
      res.end(JSON.stringify({ message: 'unauthorized' }));
      return;
    }
    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET_BACKENDV2);
      req['user'] = decoded;
      next();
    } catch (error) {
      res.statusCode = 401;
      res.setHeader('Content-Type', 'application/json');
      res.end(JSON.stringify({ message: 'unauthorized' }));
      return;
    }
  }
}
