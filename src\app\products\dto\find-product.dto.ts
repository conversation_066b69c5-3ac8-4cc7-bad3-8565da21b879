import { CountriesEnum } from '@/app/clients/dto/create-client.dto';
import { RegionsEnum, RegionsType } from '@/types/entities.types';
import { Transform } from 'class-transformer';
import { IsBoolean, IsEnum, IsOptional, IsString } from 'class-validator';



export class FindAllProductsQuery {

  @IsOptional()
  @IsString()
  readonly name?: string;

  @IsOptional()
  @Transform(({ value }) => value === 'true')
  @IsBoolean()
  readonly selectOne?: boolean;

  @IsOptional()
  @IsEnum(RegionsEnum)
  readonly region?: RegionsType;

  @IsOptional()
  @IsEnum(CountriesEnum)
  readonly country?: CountriesEnum;

}