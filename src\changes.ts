import { Between, DataSource, In, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Not } from 'typeorm';
import facturapi from './facturapi';
import { ClientEntity } from './app/clients/entities/client.entity';
import { NestFastifyApplication } from '@nestjs/platform-fastify';
import { ProductEntity } from './app/products/entities/product.entity';
import { SubscriptionProductEntity } from './app/subscription-products/entities/subscription-product.entity';
import { Invoice } from './facturapi/Invoices';
import * as fs from 'fs/promises';
import axios from 'axios';
import { getLocalDate } from './lib/dates';
import { randomUUID } from 'crypto';
import { PaymentsEntity } from './app/payments/entities/payment.entity';
import { sendPaymentWAMessage } from './hilos-whatsapp/payment-message';
import paymentLinkEmail from './providers/nodemailer/templates/paymentLink.template';
import { TaxabilityType } from './facturapi/Products';
import fs2 from 'fs';
import { promisify } from 'util';
import path from 'path';
import e from 'express';
import { CFDI_KEYS_TYPE, G03_LIST } from './sat/cfdi.types';
import { Customer } from './facturapi/Customers';

const writeFile = promisify(fs2.writeFile);
const readFile = promisify(fs2.readFile);

export async function createFacturapiCustomer(app: NestFastifyApplication) {
  try {
    const clientsRepository = app.get(DataSource).getRepository(ClientEntity);

    // const clients = await clientsRepository.find();

    const client = await clientsRepository.findOne({ where: { id: 'beec450b-3c0c-4668-9619-b9eb407baaf4' } });

    /* 
      legal_name: payment.client.legal_name,
      tax_id: payment.client.rfc,
      tax_system: payment.client.tax_system || '625',
      email: payment.client.email,
      phone: payment.client.phone,
      address: {
       zip: payment.client.zip,
      },
    */
    console.log('client', client.id)
    const randomRfc = 'XAXX010101000'

    const createCustomer = await facturapi.customers.create({
      legal_name: client.legal_name,
      tax_id: client.rfc,
      tax_system: client.tax_system || '625',
      email: client.email,
      phone: client.phone,
      address: {
        zip: client.zip,
      }
    });

    console.log('createCustomer', createCustomer);

    await clientsRepository.update(client.id, { facturapiId: createCustomer.id });

    // const isValid = await facturapi.customers.validateTaxInfo('66301b2930148830342a64ec' || '663019cf30148830342a5213');

    // console.log('isValid', isValid.data);
    // const deleteCustomer = await facturapi.customers.delete(client.facturapiId || '663019cf30148830342a5213');
    // console.log('deleteCustomer', deleteCustomer.data);
  } catch (error) {
    console.log('Hubo un error en las peticiones', error)
  }
}

export async function createFacturapiProduct(app: NestFastifyApplication) {

  try {
    const subscriptionProductsRepository = app.get(DataSource).getRepository(SubscriptionProductEntity);

    const customerId = '663117b37798c662560ccf33'

    const subscriptionProducts = await subscriptionProductsRepository.find({ where: { subscription: { id: 'sub-EoNc0Rq0IA' } } });

    // console.log('subscriptionProducts', subscriptionProducts)

    const createInvoice: Invoice = {
      customer: customerId,
      payment_form: '03',
      use: 'G03',
      items: subscriptionProducts.map(subscriptionProduct => {
        return {
          product: {
            description: subscriptionProduct.name,
            product_key: subscriptionProduct.product_key,
            price: subscriptionProduct.price,
            tax_included: true,
            taxability: '02' as any,
            taxes: [{
              rate: 0.16,
              factor: 'Tasa',
              type: 'IVA',
            }],
            unit_key: subscriptionProduct.unit_key,
            unit_name: 'Unidad de servicio',
            sku: subscriptionProduct.id,
          },
          quantity: subscriptionProduct.quantity,
        }
      })
    }


    const invoice = await facturapi.invoices.create(createInvoice);

    console.log('response', invoice);

  } catch (error) {
    console.log('Hubo un error en las peticiones', error)
  }


}

/** 
 * Downloads the invoice from facturapi and saves it in the uploads folder
 * @param id - The invoice id
 * @param type - The type of file to download (pdf or xml)
 * @returns The path and the file name of the downloaded file in the uploads folder
*/

export async function DownloadInvoice(id: string, type: 'pdf' | 'xml' = 'pdf') {
  try {
    const response = await facturapi.invoices.download(id, type);

    const buffer = Buffer.from(response);

    const fileName = type === 'pdf' ? `${id}.pdf` : `${id}.xml`;

    const path = `uploads/${fileName}`;

    await fs.writeFile(path, buffer);

    return {
      path,
      fileName,
    };
  } catch (error) {
    console.log('Hubo un error en las peticiones', error)
  }
}

/** 
 * Downloads the receipt from facturapi and saves it in the uploads folder
 * @param id - The receipt id
 * @param type - The type of file to download (pdf or xml)
 * @returns The path and the file name of the downloaded file in the uploads folder
*/

export async function DownloadReceipt(id: string, type: 'pdf' | 'xml' = 'pdf') {
  try {
    const response = await facturapi.receipts.download(id, type);

    const buffer = Buffer.from(response);

    const fileName = type === 'pdf' ? `${id}.pdf` : `${id}.xml`;

    const path = `uploads/${fileName}`;

    await fs.writeFile(path, buffer);

    return {
      path,
      fileName,
    };
  } catch (error) {
    console.log('Hubo un error en las peticiones', error)
  }
}

export async function validateCustomerTaxInfo(app: NestFastifyApplication) {
  try {
    const clientsRepository = app.get(DataSource).getRepository(ClientEntity);

    // const client = await clientsRepository.findOne({ where: { id: 'beec450b-3c0c-4668-9619-b9eb407baaf4' } });

    // const isValid = await facturapi.customers.validateTaxInfo(client.facturapiId || '663117b37798c662560ccf33');

    // console.log('VALID 1 °°°', isValid.data);

    // const client2 = await clientsRepository.findOne({ where: { id: 'daa65d36-89ec-4dc9-adc4-d0da96f76952' } });

    // const createdClient = await facturapi.customers.create({
    //   legal_name: client2.legal_name,
    //   tax_id: 'MACP021208L82',
    //   tax_system: client2.tax_system,
    //   email: client2.email,
    //   phone: client2.phone,
    //   address: {
    //     zip: '11380',
    //   }
    // });

    // const deleted = await facturapi.invoices.delete('663426327798c662562b41eb', { motive: '02' })

    // console.log('deleted', deleted);

    // const createdClient = await facturapi.customers.delete('663424cf7798c662562b314f');
    // console.log('createdClient', createdClient)
    // const isValid2 = await facturapi.customers.validateTaxInfo('663424cf7798c662562b314f');
    // const isValid2 = await facturapi.customers.validateTaxInfo(client2.facturapiId);

    // console.log('VALID 2 !!!', isValid2);

    // const update = await facturapi.customers.update('663424cf7798c662562b314f', {
    //   email: '<EMAIL>',
    //   // tax_id: 'MACP021208L82',
    //   address: {
    //     zip: '11380'
    //   }
    // });
    // console.log('UPDATED', update);
    // await facturapi.customers.update('663117b37798c662560ccf33', { address: { zip: '11830' } });

    // const createdClient = await facturapi.customers.create({
    //   legal_name: '',
    //   tax_id: '',
    //   tax_system: '',
    //   // email: client2.email,
    //   // phone: client2.phone,
    //   address: {
    //     zip: '',
    //   }
    // });
    // console.log('createdClient', createdClient);


    // const deleteInvoices = [
    //   '6631492b96c31db11a3d8cc8',
    //   '6631491d96c31db11a3d8bed',
    //   '663148b596c31db11a3d8409',
    //   '6631487e7798c66256112cad',
    //   '66312a087798c662560e5003',
    // ]

    // await Promise.all(deleteInvoices.map(async (id) => {
    //   const response = await facturapi.invoices.delete(id, { motive: '02' });

    //   console.log('DELETE INVOICE', response);
    //   console.log('-----------------------------------')
    // }));

    // const response = await facturapi.customers.update('663117b37798c662560ccf33', { tax_system: client.tax_system });

    // console.log('response', response);

  } catch (error) {
    console.log('Hubo un error en las peticiones', error)
  }
}

export async function createAllClientSubscriptions(app: NestFastifyApplication) {
  try {
    const clientsRepository = app.get(DataSource).getRepository(ClientEntity);

    const productsRepository = app.get(DataSource).getRepository(ProductEntity);

    const clients = await clientsRepository.find({
      where: {
        // subscriptions: Not()
        region: 'PBC',
        isActive: true,
      }
    });

    // const clients = await clientsRepository.createQueryBuilder('client')
    //   .leftJoinAndSelect('client.subscriptions', 'subscription')
    //   .where('subscription.id IS NULL') // Esto asegura que los clientes sin suscripción sean seleccionados
    //   .getMany();



    // console.log('clients', clients.length)
    // const products = await productsRepository.find();

    const products2 = [
      { id: 'b06386b7-d5e7-46f3-8097-9edd5f762369' },
      { id: 'fde47b14-2846-44e6-926a-cc457999efe0' }
    ]

    const subscriptionProducts = products2.map(product => {
      return {
        quantity: 1,
        id: product.id,
      }
    });
    console.time('CREATE SUBSCRIPTIONS')
    const result = await Promise.allSettled(clients.map(async (client) => {
      try {
        const body = {
          clientId: client.id,
          products: subscriptionProducts,
          region: 'PBC',
          startDate: getLocalDate(),
          // endDate should be from now to 2 years later
          endDate: new Date(getLocalDate().setFullYear(new Date().getFullYear() + 2)),
          // endDate: new Date(),
        }

        // console.log('body', body)
        const res = await axios.post('http://localhost:4000/subscriptions', body, {
          headers: {
            'Content-Type': 'application/json',
            // Authorization: 'Bearer sk-QtfbMe7LOuiw0xHvIFljKs4D0abM887kQrw4xPBRdX4TFa75pKolt' // prod
            Authorization: 'Bearer sk-IkUqjT3gqX4hV5sbFZWbYhb9289CSZRsMC42QqyutNtgrx2vdWV3A'
          }
        });
        console.log('res', res.data)
      } catch (error: any) {
        console.log('Hubo un error en las peticiones', error.response)
      }

    }));
    console.log('result', result, result.length)
    console.timeEnd('CREATE SUBSCRIPTIONS')
    // for (const client of clients) {

    //   try {
    //     const body = {
    //       clientId: client.id,
    //       products: subscriptionProducts,
    //       region: 'PBC',
    //       startDate: getLocalDate(),
    //       // endDate should be from now to 2 years later
    //       endDate: new Date(getLocalDate().setFullYear(new Date().getFullYear() + 2)),
    //       // endDate: new Date(),
    //     }

    //     // console.log('body', body)
    //     const res = await axios.post('http://localhost:4000/subscriptions', body, {
    //       headers: {
    //         'Content-Type': 'application/json',
    //         // Authorization: 'Bearer sk-QtfbMe7LOuiw0xHvIFljKs4D0abM887kQrw4xPBRdX4TFa75pKolt' // prod
    //         Authorization: 'Bearer sk-IkUqjT3gqX4hV5sbFZWbYhb9289CSZRsMC42QqyutNtgrx2vdWV3A'
    //       }
    //     });
    //     console.log('res', res.data)
    //   } catch (error: any) {
    //     console.log('Hubo un error en las peticiones', error.response)
    //   }

    // }


  } catch (error) {
    console.log('Hubo un error en las peticiones', error)
  }
}

export async function cancelAllReceipts(app: NestFastifyApplication) {

  try {

    const payments = app.get(DataSource).getRepository(PaymentsEntity);

    const limitDate = getLocalDate(new Date('2024-05-28'))


    const allPayments = await payments.find({
      where: {
        // status: 'success',
        isPaid: true,
        receiptId: Not(IsNull()),
        createdAt: LessThanOrEqual(limitDate),
      }
    });

    console.log('allPayments', allPayments.length)


    await Promise.allSettled(allPayments.map(async (payment) => {
      try {
        const response = await facturapi.receipts.cancel(payment.receiptId);

        console.log('response', response);
      } catch (error) {
        console.log('Hubo un error en las peticiones', error)
      }
    }));



  } catch (error) {
    console.log('Hubo un error en las peticiones', error)
  }

}

export async function sendMissingWAMessages(app: NestFastifyApplication) {

  try {

    const paymentsRepository = app.get(DataSource).getRepository(PaymentsEntity);


    const payments = await paymentsRepository.find({
      where: {
        // isPaid: false,
        status: 'pending',
        createdAt: MoreThanOrEqual(getLocalDate(new Date('2024-06-13')))
      },
      relations: ['client', 'subscription']
    })

    const totalToPay = payments.reduce((acc, payment) => acc + payment.total, 0);
    // await sendPaymentWAMessage()

    await Promise.allSettled(payments.map(async (payment) => {
      try {
        // console.log('message', payment.client.name, payment.id, payment.client.phone)
        // await sendPaymentWAMessage(payment.client.name, payment.id, payment.client.phone);
      } catch (error) {
        console.log('Hubo un error en las peticiones', error)
      }
    }));

    console.log('total payments', payments.length, totalToPay)


  } catch (error) {
    console.log('Hubo un error en las peticiones', error)
  }

}

export async function sendAllEmails2(app: NestFastifyApplication) {
  try {
    const paymentsRepository = app.get(DataSource).getRepository(PaymentsEntity);

    const today = getLocalDate(new Date('2024-06-27'), { hours: 23, minutes: 0, seconds: 0 })
    const limit = getLocalDate(undefined, { hours: 23, minutes: 25, seconds: 0 })
    const payments = await paymentsRepository.find({
      where: {
        subscription: Not(IsNull()),
        total: MoreThan(101),
        createdAt: MoreThanOrEqual(today)
      },
      relations: ['client', 'subscription'],
      // take: 1,
    })


    // const first = payments[0];

    // await paymentLinkEmail({
    //   email: '<EMAIL>',
    //   name: 'PEDRO MARTINEZ, TEST',
    //   paymentId: first.id,
    // })
    await Promise.allSettled(payments.map(async (payment, i) => {

      console.log('sending email number', i, '-----------------------------------------------------------')

      try {
        const nameTest = 'PEDRO MARTINEZ, TEST';
        await sendPaymentWAMessage(nameTest, payment.id, '523111656118')
        await paymentLinkEmail({
          email: '<EMAIL>',
          // name: payment.client.name,
          name: nameTest,
          paymentId: payment.id,
        });
        console.log('email number', i, 'sent -----------------------------------------------------------')

      } catch (error) {
        console.log('Hubo un error en las peticiones', error)
      }
    }));

  } catch (error) {
    console.log('Hubo un error en las peticiones', error)
  }
}

export async function sendAllEmails(app: NestFastifyApplication) {
  try {
    const paymentsRepository = app.get(DataSource).getRepository(PaymentsEntity);

    const today = getLocalDate(undefined, { hours: 23, minutes: 0, seconds: 0 });
    const limit = getLocalDate(undefined, { hours: 23, minutes: 25, seconds: 0 });

    const payments = await paymentsRepository.find({
      where: {
        subscription: Not(IsNull()),
        total: MoreThan(101),
        createdAt: MoreThanOrEqual(today),
      },
      relations: ['client', 'subscription'],
      take: 100,
    });

    console.log('Found payments:', payments.length);

    const chunkSize = 10; // Tamaño del lote (chunk size)

    // Función para enviar correos electrónicos en lotes de manera controlada
    async function sendEmailsInChunks(payments: PaymentsEntity[]) {
      const results = [];

      for (let i = 0; i < payments.length; i += chunkSize) {
        const chunk = payments.slice(i, i + chunkSize);
        const chunkPromises = chunk.map(async (payment) => {
          try {
            const nameTest = 'PEDRO MARTINEZ, TEST';
            await sendPaymentWAMessage(nameTest, payment.id, '523111656118')
            await paymentLinkEmail({
              email: '<EMAIL>',
              // name: payment.client.name,
              name: nameTest,
              paymentId: payment.id,
            });
            return { email: '<EMAIL>', status: 'sent' }; // Cambia aquí si tienes el email del cliente
          } catch (error: any) {
            console.log('Error sending email:', error);
            return { email: '<EMAIL>', status: 'failed', error: error.message }; // Cambia aquí si tienes el email del cliente
          }
        });

        const chunkResults = await Promise.allSettled(chunkPromises);
        results.push(...chunkResults);
        await sleep(1000);
      }

      return results;
    }

    // Enviar correos electrónicos en lotes controlados
    const results = await sendEmailsInChunks(payments.slice(200));

    console.log('Email sending results:', results);
  } catch (error) {
    console.error('Error sending emails:', error);
  }
}

function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

export async function invoiceAllMissingReceipts(app: NestFastifyApplication) {

  try {

    const paymentsRepo = app.get(DataSource).getRepository(PaymentsEntity);

    const firstDay = getLocalDate(new Date('2025-04-01'));
    const lastDay = getLocalDate(new Date('2025-05-01'), {
      hours: 11,
      minutes: 59,
      seconds: 59,
    });
    console.log('firstDay', firstDay, 'lastDay', lastDay)

    // i need the payments who are paid and have a receipt id and don't have an invoice id
    const payments = await paymentsRepo.find({
      where: {
        isPaid: true,
        status: 'success',
        // receiptId: '666a41a059a1d64e87d39dbd',
        receiptId: Not(IsNull()),
        invoiceId: IsNull(),
        paidAt: Between(firstDay, lastDay),
        // paidAt: Between('2024-', '')
        // client: {
        //   contractNumber: '5275',
        // }
      },
      relations: ['client'],
      // take: 5,
      order: {
        createdAt: 'ASC'
      }
    });



    payments.forEach(payment => {
      console.log('payment', payment.client.contractNumber, payment.client.id, payment.id, payment.receiptId, payment.invoiceId, payment.paidAt);
    });

    return console.log('payments', payments.length); // Comment this line to run the function

    // group payments by client contract number

    const groupedPayments = payments.reduce((acc, payment) => {
      if (!acc[payment.client.contractNumber]) {
        acc[payment.client.contractNumber] = [];
      }

      const obj = {
        id: payment.id,
        contractNumber: payment.client.contractNumber,
        invoiceId: payment.invoiceId,
        receiptId: payment.receiptId,
        createdAt: payment.createdAt,
      }

      acc[payment.client.contractNumber].push(obj);

      return acc;
    }, {});


    const morethan3 = Object.keys(groupedPayments).filter(key => groupedPayments[key].length > 0);
    // console.log(payments)
    // console.log('morethan3', morethan3, morethan3.length);
    payments.forEach(payment => {
      console.log('payment', payment.client.contractNumber, payment.id, payment.receiptId, payment.invoiceId, payment.paidAt);
    });

    const processedLogs = [];

    await Promise.allSettled(payments.map(async (payment) => {

      try {
        console.log('client', payment.client.contractNumber,);
        console.log('payment', payment.id, payment.receiptId, payment.invoiceId);
        console.log('-----------------------------------');
        // const res = await invoiceOpenReceipt(payment);
        const res = await invoiceOpenReceiptWithValidation(payment, app);
        if (!res) return;
        const invoiceId = res.id;
        console.log('invoiceId', invoiceId);
        await paymentsRepo.update(payment.id, { invoiceId });
        // console.log('invoiceId', invoiceId);
        // await log({
        //   paymentId: payment.id,
        //   contractNumber: payment.client.contractNumber,
        //   invoiceId,
        //   receiptId: payment.receiptId,
        //   separation: '--------------------------',
        //   clientId: payment.client.id,
        //   legal_name: payment.client.legal_name,
        //   paidAt: payment.paidAt,
        //   tax_system: payment.client.tax_system,
        //   tax_id: payment.client.rfc,
        //   is_valid_tax_info: payment.client.is_valid_tax_info,
        //   facturapiId: payment.client.facturapiId,
        // }, firstDay);
        processedLogs.push({
          paymentId: payment.id,
          contractNumber: payment.client.contractNumber,
          invoiceId,
          receiptId: payment.receiptId,
          separation: '--------------------------',
          clientId: payment.client.id,
          legal_name: payment.client.legal_name,
          paidAt: payment.paidAt,
          tax_system: payment.client.tax_system,
          tax_id: payment.client.rfc,
          is_valid_tax_info: payment.client.is_valid_tax_info,
          facturapiId: payment.client.facturapiId,
        });
        console.log('-------------------------------------------------------------------------------')
        console.log('-------------------------------------------------------------------------------')
        console.log('-------------------------------------------------------------------------------')

      } catch (error) {
        console.log('Hubo un error en las peticiones', error)
      }

    }));


    await logArray(processedLogs, firstDay);
    console.log('processedLogs', processedLogs.length);
    console.log('Finished processing payments');

  } catch (error) {

  }


}

async function invoiceOpenReceiptWithValidation(payment: PaymentsEntity, app: NestFastifyApplication) {
  const client = payment.client;

  try {
    let facturapiId = client.facturapiId;
    let isValidTaxInfo = client.is_valid_tax_info;

    // If client doesn't have facturapiId, try to create it
    if (!facturapiId) {
      console.log('Client does not have facturapiId, trying to create one');
      try {
        const customerData = {
          // legal_name: client.legal_name.trim().toUpperCase(),
          legal_name: removeAccentsOnly(client.legal_name.trim().toUpperCase()),
          tax_id: client.rfc,
          tax_system: client.tax_system || '616',
          email: client.email,
          phone: client.phone,
          address: {
            zip: client.zip,
          }
        };
        console.log('Customer data:', customerData);
        const customer = await facturapi.customers.create(customerData);
        console.log('Customer created in Facturapi:', customer.id);

        facturapiId = customer.id;

        // Validate tax info for the newly created customer
        const validation = await facturapi.customers.validateTaxInfo(facturapiId);
        isValidTaxInfo = validation.is_valid;

        // Update client with new facturapiId and validation status
        const clientsRepository = app.get(DataSource).getRepository(ClientEntity);
        await clientsRepository.update(client.id, {
          facturapiId,
          is_valid_tax_info: isValidTaxInfo
        });

        console.log('Client updated with facturapiId and validation status:', isValidTaxInfo);
      } catch (error: any) {
        console.log('Error creating customer in Facturapi:', error.message);
        // Continue with default customer if creation fails
      }
    }
    // validate tax info again before proceeding
    if (facturapiId && isValidTaxInfo) {
      const validation = await facturapi.customers.validateTaxInfo(facturapiId);
      isValidTaxInfo = validation.is_valid;
      console.log('Tax info validation result:', isValidTaxInfo);

      // Check if client isValidTaxInfo is different from the previous validation isValidTaxInfo
      // if it was true and now is false, we update the client
      if (!isValidTaxInfo) {
        const clientsRepository = app.get(DataSource).getRepository(ClientEntity);
        await clientsRepository.update(client.id, {
          is_valid_tax_info: isValidTaxInfo
        });
      }
    }


    // Check if client has facturapiId and valid tax info
    if (facturapiId && isValidTaxInfo) {
      console.log('Using client facturapiId with valid tax info:', facturapiId);

      payment.client.is_valid_tax_info = isValidTaxInfo;
      payment.client.facturapiId = facturapiId;



      const receipt = await facturapi.receipts.retrieve(payment.receiptId);
      console.log('Receipt status WHEN FACTURAPI ID AND VALID TAX INF ARE TRUE:', receipt.status);

      if (receipt.status === 'invoiced_to_customer') {
        console.log('Receipt already invoiced');
        return {
          id: receipt.invoice,
        };
      }

      if (receipt.status === 'open') {
        // Use client's tax system or default to '616'
        const tax_system = client.tax_system || '616';
        let use: CFDI_KEYS_TYPE = 'G03';

        if (!G03_LIST.includes(tax_system)) {
          console.log('REGIMEN NO VALIDO PARA CFDI "G03", CAMBIANDO EL USO DE CFDI A "S01"', tax_system);
          use = "S01";
        }
        try {


          const response = await facturapi.receipts.invoice(payment.receiptId, {
            customer: facturapiId,
            use,
            series: client.region,
          });

          return response;
        } catch (error: any) {
          // Create the invoice with the default customer
          console.log('Error invoicing receipt with client facturapiId:', error.message);
          console.log('Creating invoice with default customer |||||||||||||||||||||||||||||||||||||||||||||||');
          // const customer: Customer = {
          //   legal_name: removeAccentsOnly(client.legal_name.trim().toUpperCase()) || removeAccentsOnly(client.name.trim().toUpperCase()),
          //   tax_id: 'XAXX010101000',
          //   tax_system: '616',
          //   address: {
          //     zip: '11320',
          //   },
          // };
          // const response = await facturapi.receipts.invoice(payment.receiptId, {
          //   customer,
          //   use: 'S01',
          //   series: client.region,
          // });

          // return response;

        }
      }
    } else {
      console.log('Using default customer data (no valid facturapiId or invalid tax info)');
      // Fall back to default customer data
      const customer: Customer = {
        legal_name: removeAccentsOnly(client.legal_name.trim().toUpperCase()) || removeAccentsOnly(client.name.trim().toUpperCase()),
        tax_id: 'XAXX010101000',
        tax_system: '616',
        address: {
          zip: '11320',
        },
      };
      console.log('Customer to invoice:', customer);
      const receipt = await facturapi.receipts.retrieve(payment.receiptId);
      console.log('Receipt status DEFAULT CUSTOMER ----:', receipt.status);

      if (receipt.status === 'invoiced_to_customer') {
        console.log('Receipt already invoiced');
        return {
          id: receipt.invoice,
        };
      }

      if (receipt.status === 'open') {
        const response = await facturapi.receipts.invoice(payment.receiptId, {
          customer,
          use: 'S01',
          series: client.region,
        });

        return response;
      }
    }
  } catch (error: any) {
    // console.log('ERROR CREATE INVOICE', error.message, payment.receiptId);
    console.error('Error creating invoice:', error);
  }
}



async function invoiceOpenReceipt(payment: PaymentsEntity) {

  const client = payment.client;

  try {

    const customer: Customer = {
      legal_name: client.name,
      tax_id: 'XAXX010101000',
      tax_system: '616',
      address: {
        zip: '11320',
      },
    }

    const receipt = await facturapi.receipts.retrieve(payment.receiptId);

    console.log('receipt', receipt.status);

    if (receipt.status === 'invoiced_to_customer') {
      console.log('receipt already invoiced')
      // await facturapi.receipts.cancel(payment.receiptId);
      return {
        id: receipt.invoice,
      }
    }
    if (receipt.status === 'open') {

      const response = await facturapi.receipts.invoice(payment.receiptId, {
        customer,
        use: 'S01',
        series: client.region,
      });
      // console.log('response facturapi invoice receipt', response);
      return response;
    }
  } catch (error: any) {
    console.log('ERROR CREATE INVOICE', error.message, payment.receiptId);
  }

}

const logArray = async (data: any[], date: Date) => {
  const allLogs = [];
  const isoString = date.toISOString();
  const year = isoString.slice(0, 4);
  const month = isoString.slice(5, 7);
  const fileName = `invoices_${year}-${month}.json`;

  try {
    if (fs2.existsSync(fileName)) {
      const data2 = await readFile(fileName, 'utf-8');
      // allLogs = JSON.parse(data2);
      allLogs.push(...JSON.parse(data2));
    }

    allLogs.push(...data);

    await writeFile(fileName, JSON.stringify(allLogs, null, 2), 'utf-8');
    // console.log('Add log', data);
    console.log('Add log', data.length, 'to', fileName);
  } catch (error) {
    console.error('Error logging email:', error);
  }

}


const log = async (data: any, date: Date) => {
// const logFilePath = path.resolve('invoices.json');
  let allLogs = [];
  // Create the file name based on month like this: invoices_YYYY-MM.json
  const isoString = date.toISOString();
  const year = isoString.slice(0, 4);
  const month = isoString.slice(5, 7);
  const fileName = `invoices_${year}-${month}.json`;

  try {
    // if (fs2.existsSync(logFilePath)) {
    if (fs2.existsSync(fileName)) {
      // If the file exists, read it
      // const data2 = await readFile(logFilePath, 'utf-8');
      const data2 = await readFile(fileName, 'utf-8');
    // Parse the data and assign it to allLogs to avoid overwriting it
      allLogs = JSON.parse(data2);
    }

    // emailLog.push({ email, date: new Date().toISOString() });

    allLogs.push(data);
    // await writeFile(logFilePath, JSON.stringify(allLogs, null, 2), 'utf-8');
    await writeFile(fileName, JSON.stringify(allLogs, null, 2), 'utf-8');
    console.log('Add log', data);
  } catch (error) {
    console.error('Error logging email:', error);
  }
};


export async function cancelAllReceiptsFromLastMonth(app: NestFastifyApplication) {

  try {

    const paymentsRepo = app.get(DataSource).getRepository(PaymentsEntity);

    const firstDay = getLocalDate(new Date('2024-06-29'));
    const lastDay = getLocalDate(new Date('2024-06-30'), {
      hours: 23,
      minutes: 59,
      seconds: 59,
    });

    console.log('firstDay', firstDay, 'lastDay', lastDay)

    const payments = await paymentsRepo.find({
      where: {
        isPaid: true,
        status: 'success',
        receiptId: Not(IsNull()),
        // createdAt: Between(firstDay, lastDay),
        paidAt: Between(firstDay, lastDay),
        client: {
          contractNumber: '1250',
        }
      },
      relations: ['client'],
    });

    // return
    await Promise.allSettled(payments.map(async (payment) => {

      try {
        console.log('client and payment', payment.client.contractNumber, payment.id, payment.receiptId, payment.invoiceId);
        console.log('-----------------------------------');

        const receipt = await facturapi.receipts.retrieve(payment.receiptId);

        if (receipt.status === 'open') {
          console.log('receipt already invoiced', receipt.id)
          return await facturapi.receipts.cancel(payment.receiptId);
        }

      } catch (error) {
        console.log('Hubo un error en las peticiones', error)
      }

    }));


  } catch (error) {

  }

}

// export async function createAllMissingReceipts(app: NestFastifyApplication) {
// Change the name to something more descriptive, like createAllMissingReceiptsAndInvoices
export async function createAllMissingReceiptsAndInvoices(app: NestFastifyApplication) {

    console.log('starting createAllMissingReceipts')
    const paymentsRepo = app.get(DataSource).getRepository(PaymentsEntity);
    const clientsRepo = app.get(DataSource).getRepository(ClientEntity);

    const firstDay = getLocalDate(new Date('2024-06-29'));

    const payments = await paymentsRepo.find({
      where: {
        isPaid: true,
        status: 'success',
        receiptId: IsNull(),
        invoiceId: IsNull(),
        total: MoreThan(99),
        // paidAt: MoreThanOrEqual(firstDay),
        client: {
          contractNumber: In(['1274'] /* ["1412"] */)
        }
      },
      relations: ['client', 'subscription', 'subscription.products'],
      take: 1,
      order: {
        createdAt: 'ASC'
      }
    });

    
    // const counter = payments.reduce((acc, payment) => {
    //   if (!acc[payment.client.contractNumber]) {
    //     acc[payment.client.contractNumber] = 0;
    //   }
    //   acc[payment.client.contractNumber]++;
    //   return acc;
    // }, {});
    // console.log('counter', counter);
    
    payments.forEach(payment => {
      console.log('payment', payment.client.contractNumber, payment.id, payment.receiptId, payment.invoiceId, payment.paidAt);
    }); 
    // return ;
    await Promise.allSettled(payments.map(async (payment) => {
          console.log('CREATING INVOICE OR RECEIPT FOR PAYMENT', payment.id, payment.client.contractNumber);

          try {
            let facturapiId = payment.client.facturapiId;
            if (!facturapiId) {
              console.log('facturapi id does not exist', 'creating new customer')
              try {
                console.log('client', payment.client)
                const customer = await facturapi.customers.create({
                  legal_name: payment.client.legal_name,
                  tax_id: payment.client.rfc,
                  tax_system: payment.client.tax_system || '616',
                  email: payment.client.email,
                  phone: payment.client.phone,
                  address: {
                    zip: payment.client.zip,
                  }
                });
                console.log('customer created', customer);
                facturapiId = customer.id;
                await clientsRepo.update(payment.client.id, { facturapiId, is_valid_tax_info: true, tax_system: customer.tax_system });
                // console.log('facturapi id saved', facturapiId)
              } catch (error: any) {
                console.log('Hubo un error en las peticiones', error.message, payment.client.rfc)
              }
            }
            console.log('facturapiId', facturapiId)
            // if (!facturapiId) return;

            // const { is_valid: isValid } = await facturapi.customers.validateTaxInfo(payment.client.facturapiId);

            const isValid = facturapiId ? (await facturapi.customers.validateTaxInfo(facturapiId)).is_valid : false;

            // console.log('before fix ', payment.client.contractNumber, payment.client.tax_system);
            await fixClientTaxInfo(payment.client, isValid, app);

            // console.log('client changed', payment.client.contractNumber, payment.client.tax_system);

            // return 
            console.log('isValid??? ----', isValid);
            if (isValid){
              const invoice = await createInvoice(payment);

              if (!invoice) return;

              if (invoice.invoiceId) {
                await paymentsRepo.update(payment.id, { invoiceId: invoice.invoiceId });
              } else {
                await paymentsRepo.update(payment.id, { receiptId: invoice.receiptId });
              }

              return;
            }
    
            const receipt = await createReceipt(payment);
          
            if (!receipt) return;

            await paymentsRepo.update(payment.id, { receiptId: receipt.id });
          } catch (error) {
            console.log('Hubo un error en las peticiones', error)
          }

  
      }));

}

async function fixClientTaxInfo(client: ClientEntity, isValid: boolean, app: NestFastifyApplication){
  try {
    
    if (!client.is_valid_tax_info){

      if(isValid){
        const clientsRepo = app.get(DataSource).getRepository(ClientEntity);
        
        const customer = await facturapi.customers.retrieve(client.facturapiId);
        
        const tax_system = customer.tax_system;
        if (tax_system !== client.tax_system){
          await clientsRepo.update(client.id, { tax_system });
          client.tax_system = tax_system;
        }
        await clientsRepo.update(client.id, { is_valid_tax_info: true });
        // await client.update({ is_valid_tax_info: true });
      } 

    }
  } catch (error:any) {
    console.log('Hubo un error en las peticiones', error.message)
  }
}

// async function 

function generateItems (payment: PaymentsEntity) {

  const hasSubscription = Boolean(payment.subscription);

  const items = hasSubscription ? 
    payment.subscription.products.map(product => {
      return {
        product: {
          description: product.name,
          product_key: product.product_key,
          price: product.price,
          tax_included: true,
          taxability: '02' as TaxabilityType,
          taxes: [{
            rate: product.taxRate,
            factor: product.taxFactor,
            type: product.taxType,
          }],
          unit_key: product.unit_key,
          unit_name: product.measurementUnit,
          sku: product.id,
        },
        quantity: product.quantity,
      }
    }) 
    :
    payment.products.map(product => {
      return {
        product: {
          description: product.name,
          product_key: product.product_key,
          price: product.total,
          tax_included: true,
          taxability: '02' as TaxabilityType,
          taxes: [{
            rate: product.taxRate,
            factor: product.taxFactor,
            type: product.taxType,
          }],
          unit_key: product.unit_key,
          unit_name: product.measurementUnit,
          sku: product.id,
        },
        quantity: product.quantity,
      }
    }) 

    return items;

}

async function createReceipt(payment: PaymentsEntity) {
  const client = payment.client;
  try {

    const items = generateItems(payment);

    const receipt = await facturapi.receipts.create({
      customer: client.facturapiId,
      items,
      payment_form: '03',
      date: payment.paidAt,
    });
    console.log('receipt created', receipt.id, receipt.status);
    const res = await facturapi.receipts.sendReceiptByEmail(receipt.id, { email: client.email });

    console.log('receipt email sent', res)

    return receipt;

  } catch (error: any) {
    console.log('ERROR CREATE RECEIPT', error.message, payment.id);
  }

}

async function createInvoice(payment: PaymentsEntity){
  let invoice_created = false;
  try {
    const client = payment.client;

    const items = generateItems(payment);
  
    const tax_system = client.tax_system || '616';
  
    let use: CFDI_KEYS_TYPE = 'G03';
    if (!G03_LIST.includes(tax_system)) {
      console.log('REGIMEN NO VALIDO PARA CFDI "G03", CAMBIANDO EL USO DE CFDI A "S01"', tax_system);
      use = "S01";
    }
  
    const invoice = await facturapi.invoices.create({
      customer: client.facturapiId,
      payment_form: '03',
      use,
      items,
      series: client.region,
    });
    invoice_created = true;
    console.log('invoice created', invoice.id, invoice.status, payment.id, payment.client.contractNumber);
  
    const response = await facturapi.invoices.sendInvoiceByEmail(invoice.id, { email: client.email });
    console.log('invoice email sent', response)
  
    return {
      ...invoice,
      invoiceId: invoice.id,
    };
  } catch (error) {
    console.log('Create invoice failed, trying to create receipt', payment.id, payment.client.contractNumber);
    if (!invoice_created) {
      const receipt = await createReceipt(payment);
      if (!receipt) return;

      return {
        ...receipt,
        receiptId: receipt.id,
      }
    }

  }

}


export function removeAccentsOnly(text: string): string {
  return text
    .normalize('NFD')           // Normalize to decomposed form
    .replace(/[\u0300-\u036f]/g, ''); // Remove only combining diacritical marks
}