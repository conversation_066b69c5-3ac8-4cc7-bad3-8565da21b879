import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { RegionsEnum, RegionsType } from '@/types/entities.types';
import { CountriesShortNamesEnum } from '@/app/clients/dto/create-client.dto';

export enum TaxTypesEnum {
  IVA = "IVA",
  IEPS = "IEPS",
  ISR = "ISR"
}

export type TaxTypesType = keyof typeof TaxTypesEnum;

export enum TaxFactorEnum {
  Tasa = "Tasa",
  Cuota = "Cuota",
  Exento = "Exento"
}

export type TaxFactorType = keyof typeof TaxFactorEnum;

@Entity({ name: 'products' })
export class ProductEntity {

  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({
    type: 'text',
  })
  name: string; // name

  @Column({
    type: 'text',
    nullable: true
  })
  description: string; // description


  @Column({
    type: "float",
  })
  price: number; // price

  @Column({
    type: 'float',
    nullable: true,
  })
  subTotal: number; // subTotal, price without taxes (iva)

  @Column({
    type: 'float',
    nullable: true,
    default: 0,
  })
  tax: number; // tax, iva

  @Column({
    type: "text",
    default: '01010101'
  })
  product_key: string; // product_key

  @Column({
    type: "text",
    default: 'E48'
  })
  unit_key: string; // unit_key

  // unidad de medida
  @Column({
    type: 'text',
    default: 'Unidad de servicio',
    nullable: true
  })
  measurementUnit: string; // unit

  @Column({
    type: 'float',
    default: .16,
  })
  taxRate: number; // base, default: 1, 


  @Column({
    type: 'text',
    enum: TaxFactorEnum,
    default: TaxFactorEnum.Tasa,
    nullable: true
  })
  taxFactor: TaxFactorType; // factor tax, Enum: "Tasa" "Cuota" "Exento"

  @Column({
    type: 'enum',
    enum: TaxTypesEnum,
    default: TaxTypesEnum.IVA,
    nullable: true
  })
    /**
     * type tax, Enum: "IVA" "IEPS" "ISR"
     */
  taxType: TaxTypesType; // type tax, Enum: "IVA" "IEPS" "ISR"

  @Column({
    type: 'text',
    nullable: true,
    default: false
  })
  withHolding: string; // withholdingTax (Retenido por el cliente)

  // iva incluido o excluido
  @Column({
    type: 'boolean',
    default: true
  })
  ivaIncluded: boolean; // ivaIncluded

  @Column({
    type: 'enum',
    enum: RegionsEnum,
  })
  region: RegionsType

  @Column({
    type: 'enum',
    default: CountriesShortNamesEnum.Mexico,
    nullable: true,
    enum: CountriesShortNamesEnum
  })
  country: CountriesShortNamesEnum; // country

  @Column({
    type: 'text',
    nullable: true
  })
  state: string; // For USA and other countries


  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date; // createdAt
}

