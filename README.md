
# Payments Service

A NestJS-based payment service that handles payment link generation and management. The service generates secure payment links for drivers, manages payment processing, and handles payment notifications.

## System Overview

### Core Features
- Payment link generation for scheduled payments
- Secure payment processing
- Payment status tracking and management

### Payment Flow
1. Payment is scheduled for a driver
2. <PERSON> generates a unique, secure payment link
3. Link is sent to driver via email/phone
4. Driver accesses payment portal through the link
5. Payment is processed and status is updated

## Prerequisites

Before you begin, ensure you have met the following requirements:

### Node.js and NPM
- Install Node.js (v18.x or higher) and NPM from the [official Node.js website](https://nodejs.org/)
- Verify installation by running:
  ```bash
  node --version
  npm --version
  ```

### PostgreSQL
- Install PostgreSQL from the [official PostgreSQL website](https://www.postgresql.org/download/)
- Create a database for the service

## Getting Started

Follow these steps to get your development environment running:

1. **Clone the repository**
   ```bash
   git clone https://github.com/username/payments.git
   cd payments
   ```

2. **Environment Setup**
   ```bash
   # Copy the sample environment file
   cp env.sample .env

   # Open .env and update the variables according to your setup
   # Required variables:
   # DATABASE_URL=postgresql://user:password@localhost:5432/payment_db
   # PAYMENT_GATEWAY_API_KEY=your-api-key
   # JWT_SECRET=your-secret-key
   # SMTP_CONFIG=your-smtp-settings
   # FRONTEND_URL=http://localhost:3000
   ```

3. **Install Dependencies**
   ```bash
   # Install all dependencies
   npm install

   # Install development dependencies
   npm install --save-dev
   ```

4. **Database Setup**
   ```bash
   # Run database migrations
   npm run migration:postgres
   ```

5. **Start Development Server**
   ```bash
   npm run start:dev
   ```
   The service will start on http://localhost:4000

## Available Scripts

- `npm run start:dev` - Start development server with hot-reload
- `npm run build` - Build for production
- `npm run start:prod` - Start production server
- `npm run lint` - Run ESLint
- `npm run migration:postgres` - Postgres migrations
- `npm run migration:mongo` - MongoDB migrations

## Development Guidelines

### Code Style
- Follow NestJS best practices and conventions
- Use TypeScript decorators and types

### Error Handling
- Implement proper logging

## Contributing

1. Clone the repository
2. Create your feature branch from `main` (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Merge and test with `develop`
6. Open a Pull Request for `master`

## Troubleshooting

Common issues and their solutions:

1. **Database Connection Issues**
   - Verify database credentials in .env
   - Ensure PostgreSQL service is running
   - Check network connectivity

2. **Payment Gateway Issues**
   - Validate API keys

## Support

For support, please:
1. Check the API documentation
2. Review error logs
3. Contact the development team