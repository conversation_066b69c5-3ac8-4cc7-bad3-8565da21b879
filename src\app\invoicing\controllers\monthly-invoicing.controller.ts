import { Controller, Post, Body, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { MonthlyInvoicingService } from '../services/monthly-invoicing.service';

@ApiTags('invoicing')
@Controller('invoicing')
export class MonthlyInvoicingController {
  private readonly logger = new Logger(MonthlyInvoicingController.name);

  constructor(private readonly monthlyInvoicingService: MonthlyInvoicingService) { }

  @Post('process-monthly')
  @ApiOperation({ summary: 'Procesar facturación mensual manualmente' })
  @ApiResponse({ status: 200, description: 'Proceso iniciado correctamente' })
  async processMonthlyInvoicing(@Body() body: { isTest?: boolean }) {
    this.logger.log('Iniciando proceso manual de facturación mensual');
    return await this.monthlyInvoicingService.processMonthlyInvoicing(body.isTest);
  }
}
