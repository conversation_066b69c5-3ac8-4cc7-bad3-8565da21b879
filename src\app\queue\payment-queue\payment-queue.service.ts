import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { JOB_CREATE_PAYMENT, PAYMENT_QUEUE } from '@/constants';

@Injectable()
export class PaymentQueueService {
  private readonly logger = new Logger(PaymentQueueService.name);
  constructor(@InjectQueue(PAYMENT_QUEUE) private paymentQueue: Queue) {}

  async createPayment(data: any) {
    this.logger.log({
      message:
        `[PaymentQueueService] - Subscription added to queue for payment creation: ${data}`
    });
    const job = await this.paymentQueue.add(JOB_CREATE_PAYMENT, data);
    return job.id; // Return the job ID for tracking
  }
}
