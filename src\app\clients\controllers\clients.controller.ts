import { Controller, Get, Post, Body, Patch, Param, Delete, Req, Res, Query, HttpException } from '@nestjs/common';
import { ClientsService } from '../services/clients.service';
import { CreateClientDto } from '../dto/create-client.dto';
import { UpdateClientDto } from '../dto/update-client.dto';
import { Response } from 'express';
import { ApiTags } from '@nestjs/swagger';
import { tryCatchResponse } from '@/lib/defaultResponse';
import { FindClient, FindClientsByCountry } from '../dto/query-find-clients.dto';
import { MessageReadDto } from '../dto/message-read.dto';

@ApiTags('clients')
@Controller('clients')
export class ClientsController {
  constructor(private readonly clientsService: ClientsService) { }

  @Post()
  create(@Body() createClientDto: CreateClientDto) {
    return this.clientsService.create(createClientDto);
  }

  @Get()
  async findAll(@Query() query: FindClientsByCountry, @Res() res: Response) {
    return await tryCatchResponse(res, () => this.clientsService.findAll({...query}))
  }

  @Get('q')
  async findIdBySearch(@Query() query: FindClient, @Res() res: Response) {
    return await tryCatchResponse(res, () => this.clientsService.findIdBySearch(query.search));
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.clientsService.findOne(id);
  }

  @Get(':id/validate-tax-information')
  validateTaxInformation(@Param('id') id: string, @Query('facturapiId') facturapiId?: string) {
    return this.clientsService.validateTaxInformation({
      id,
      facturapiId
    });
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateClientDto: UpdateClientDto) {
    return this.clientsService.update(id, updateClientDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.clientsService.remove(id);
  }

  @Post('message-read')
  async messageRead(@Body() data: MessageReadDto, @Res() res: Response) {
    return await tryCatchResponse(res, () => this.clientsService.messageRead(data));
  }
  @Get('/associateId/:associateId')
  getClientByAssociateId(@Param('associateId') associateId: string) {
    return this.clientsService.getClientByAssociateId(associateId);
  }

}
