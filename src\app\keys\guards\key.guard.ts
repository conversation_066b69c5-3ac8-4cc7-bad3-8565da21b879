import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Request } from 'express';
import { KeysService } from '../services/keys.service';
import { IS_PUBLIC_KEY } from './public.decorator';


@Injectable()
export class KeyGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private readonly keysService: KeysService,
  ) { }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const isPublic = this.reflector.get<boolean>(IS_PUBLIC_KEY, context.getHandler());

    if (isPublic) return true;

    const request = context.switchToHttp().getRequest();

    /**
     * add this to bypass the keyguard for stripe paymentIntent webhook,
     * because stripe doesn't send the custom header with it's webhooks
     */
    if(request.url.includes('/stripe-payment/webhook/paymentIntent')) {
      return true;
    }
    const token = this.extractTokenFromHeader(request);

    if (!token) {
      throw new UnauthorizedException();
    }

    const key = await this.keysService.validateKey(token);
    if (!key) {
      throw new UnauthorizedException();
    }

    // const isOkey = await this.keysService.validateKey(token);
    const isOkey = true;

    return isOkey;
  }

  private extractTokenFromHeader(request: Request): string | undefined {
    // console.log('REQUEST', request.headers)
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }
}
