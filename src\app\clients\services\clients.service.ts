import { HttpException, HttpStatus, Injectable, Logger, Inject } from '@nestjs/common';
import { ILike, Not, Repository, FindOptionsSelect, In } from "typeorm";
import { InjectRepository } from '@nestjs/typeorm';
import { ClientEntity } from '../entities/client.entity';
import { CountriesEnum, CountriesShortNames, CreateClientDto } from '../dto/create-client.dto';
import { UpdateClientDto } from '../dto/update-client.dto';
import { MessageReadDto } from '../dto/message-read.dto';
import { StripePaymentService } from '@/app/stripe-payment/stripe-payment.service';
import stripeAccountLinkEmail from '@/providers/nodemailer/templates/paymentLinkStripe.template';
import facturapi from '@/facturapi';
import { Customer } from '@/facturapi/Customers';
import { SubscriptionEntity } from '@/app/subscriptions/entities/subscription.entity';
import { PaymentsEntity } from '@/app/payments/entities/payment.entity';
import { Db, ObjectId } from 'mongodb';
import { slackApi } from '@/logger.config';

interface AdditionalData {
  [key: string]: any;
}

function returnResponse<T, U extends AdditionalData = {}>(message: string, data: T, additionalData?: U): { message: string; data: T; } & U {
  return {
    message,
    data,
    ...(additionalData && additionalData) // Agregar additionalData solo si está presente
  } as { message: string; data: T; } & U;
}

@Injectable()
export class ClientsService {
  private readonly logger = new Logger(ClientsService.name);
  constructor(
    @Inject('MONGODB_CONNECTION')
    private db: Db,
    @InjectRepository(ClientEntity) private readonly clientsRepository: Repository<ClientEntity>,
    private readonly stripePaymentService: StripePaymentService,
    @InjectRepository(SubscriptionEntity) private readonly subscriptionRepository: Repository<SubscriptionEntity>,
    @InjectRepository(PaymentsEntity) private readonly paymentsRepository: Repository<PaymentsEntity>,
  ) { }

  async create(createClientDto :CreateClientDto) {

    const legal_name = createClientDto.legal_name || (createClientDto.name + ' ' + createClientDto.lastName);

    createClientDto = {
      ...createClientDto,
      legal_name
    };

    if (createClientDto.country === CountriesEnum['United States']) {
      createClientDto.country =
        CountriesShortNames[CountriesEnum['United States']];
    }else{
      createClientDto.country =
        CountriesShortNames[CountriesEnum.Mexico]
    }

    const client = this.clientsRepository.create(createClientDto);
    const saved = await this.clientsRepository.save(client);

    if (createClientDto.country === CountriesShortNames[CountriesEnum['United States']]) {
      this.logger.log('USA client registeration on Stripe');
      await this.stripePaymentService.createStripeCustomer(client);

      try {
        await stripeAccountLinkEmail({
          email: client.email,
          clientId: client.id,
          name: client.name,
        });
      } catch (err) {
        this.logger.error(
          'Error occurred while sending email to client for Stripe Account Link',
          err,
        );
      }
    }
    

    return {
      message: 'Client created',
      data: saved
    };
  }

  async findAll(filters: Record<string, any>) {

    const where = {};
    if(filters.country){
      where['country'] = In([CountriesShortNames[filters.country],filters.country]);
    }
    const clients = await this.clientsRepository.find({
      where: where,
      relations: ['stripeCustomer'],
      select: {
        stripeCustomer: {
          id: true,
          isCustomerBankAccountLinked: true
        }
      }
    });
    return {
      message: 'Clients found',
      total: clients.length,
      data: clients,
    }
  }

  async findIdBySearch(search: string) {

    const where = []

    if (search) {
      where.push({ legal_name: ILike(`%${search}%`) });
      where.push({ contractNumber: ILike(`%${search}%`) });
      if (search.includes('@')) where.push({ email: ILike(`%${search}%`) });
    }

    const clients = await this.clientsRepository.find({
      // where,
      where,
      select: ['id', 'legal_name', 'contractNumber', 'email'],
    });

    return {
      message: 'Client found',
      total: clients.length,
      data: clients,
    }

  }

  async findIdBySearchV2(search: string) {
    if (!search) {
      return {
        message: 'No search term provided',
        total: 0,
        data: [],
      };
    }

    const queryBuilder = this.clientsRepository.createQueryBuilder('client');

    queryBuilder.where('(client.legal_name ILIKE :search AND client.legal_name IS NOT NULL AND client.legal_name != \'\')', { search: `%${search}%` })
      .orWhere('(client.contractNumber ILIKE :search AND client.contractNumber IS NOT NULL AND client.contractNumber != \'\')', { search: `%${search}%` });

    if (search.includes('@')) {
      queryBuilder.orWhere('(client.email ILIKE :search AND client.email IS NOT NULL AND client.email != \'\')', { search: `%${search}%` });
    }

    const clients = await queryBuilder.select(['client.id', 'client.legal_name', 'client.contractNumber', 'client.email']).getMany();

    return {
      message: 'Client found',
      total: clients.length,
      data: clients,
    };
  }

  async findOne(id: string) {
    const client = await this.clientsRepository.findOne({
      where: {
        id,
      },
      relations: ['stripeCustomer'],
      select: {
        stripeCustomer: {
          id: true,
          isCustomerBankAccountLinked: true,
        },
      }
    });

    if (!client) throw new HttpException('Client not found', 404);

    let facturapiClient = null;

    if (client.facturapiId) {
      try {
        facturapiClient = await facturapi.customers.retrieve(client.facturapiId);
      } catch (error) {
      }
    }


    if (facturapiClient) {
      // client.tax_system = facturapiClient.tax_system;

      if (client.legal_name !== facturapiClient.legal_name) {
        await this.clientsRepository.update(id, {
          legal_name: facturapiClient.legal_name,
          name: capitalizeFirstLetterOfEachWord(facturapiClient.legal_name),
        });
      }

      if (facturapiClient.email && client.email !== facturapiClient.email) {
        await this.clientsRepository.update(id, {
          email: facturapiClient.email,
        });
      }

      if (client.rfc !== facturapiClient.tax_id) {
        await this.clientsRepository.update(id, {
          rfc: facturapiClient.tax_id,
        });
      }

      if (client.zip !== facturapiClient.address.zip) {
        await this.clientsRepository.update(id, {
          zip: facturapiClient.address.zip,
        });
      }

      if (client.tax_system !== facturapiClient.tax_system) {
        await this.clientsRepository.update(id, {
          tax_system: facturapiClient.tax_system,
        });
      }

      client.tax_system = facturapiClient.tax_system;
      client.legal_name = facturapiClient.legal_name;
      client.email = facturapiClient.email;
      client.rfc = facturapiClient.tax_id;
      client.zip = facturapiClient.address.zip;
      client.name = capitalizeFirstLetterOfEachWord(facturapiClient.legal_name.trim());

    }

    return returnResponse('Client found', client);
  }

  async findByAssociateId(associateId: string) {
    const client = await this.clientsRepository.findOne({
      where: {
        associateId,
      }
    });
    if (!client) {
      throw new HttpException('Client not found', 404);
    }
    return returnResponse('Client found', client);
  }

  async update(id: string, updateClientDto: UpdateClientDto) {

    const client = await this.clientsRepository.findOne({
      where: {
        id,
      }
    });

    if (!client) {
      throw new HttpException('Client not found', 404);
    }

    if (updateClientDto?.country === CountriesEnum['United States']) {
      updateClientDto.country =
        CountriesShortNames[CountriesEnum['United States']];
    } else {
      updateClientDto.country =
        CountriesShortNames[CountriesEnum.Mexico]
    }

    if (updateClientDto.metadata) {
      if (updateClientDto.overWriteMetadata) {
        updateClientDto.metadata = Object.assign({}, updateClientDto.metadata);
      } else {
        updateClientDto.metadata = Object.assign({}, client.metadata, updateClientDto.metadata);
      }
    }

    const facturapiUpdateObj = {
      tax_system: updateClientDto.tax_system || client.tax_system,
      legal_name: updateClientDto.legal_name || client.legal_name,
      email: updateClientDto.email || client.email,
      tax_id: updateClientDto.rfc || client.rfc,
      address: {
        zip: updateClientDto.zip || client.zip,
      }
    }

    if (!client.facturapiId) {
      try {
        const res = await facturapi.customers.create(facturapiUpdateObj);
        updateClientDto['facturapiId'] = res.id;
      } catch (error: any) {
        // throw new HttpException(error.message || 'Error creating client in facturapi', 400);
      }
    } else {
      try {

        await facturapi.customers.update(client.facturapiId, facturapiUpdateObj);

      } catch (error: any) {
        // throw new HttpException(error.message || 'Error updating client in facturapi', 400);
      }
    }


    const nameCap = updateClientDto.legal_name ? capitalizeFirstLetterOfEachWord(updateClientDto.legal_name.trim()) : client.name;
    updateClientDto.name = nameCap;

    if ('stripeCustomer' in updateClientDto) {
      delete updateClientDto.stripeCustomer;
    }

    await this.clientsRepository.update(id, updateClientDto);

    if ((updateClientDto.zip && client.zip !== updateClientDto.zip) || (updateClientDto.rfc && client.rfc !== updateClientDto.rfc)) {
      try {
        await this.db.collection('associates').updateOne({ _id: new ObjectId(client.associateId) }, {
          $set: {
            postalCode: updateClientDto.zip,
            rfc: updateClientDto.rfc
          }
        });
      } catch (error: any) {
        this.logger.error('Error updating associate postalCode', error);
        await slackApi.chat.postMessage({
          text: `Error updating associate postalCode: ${error.message}`,
          channel: process.env.AUTO_INVOICE_CHANNEL!,
        })

      }
    }

    return {
      message: 'Client updated',
    };
  }

  async remove(id: string) {

    type some = FindOptionsSelect<ClientEntity>
    const client = await this.clientsRepository.findOne({
      where: {
        id,
      },
      relations: ['subscriptions', 'subscriptions.products', 'subscriptions.payments'],
      select: {
        id: true,
        subscriptions: {
          id: true,
          products: {
            id: true,
          },
          payments: {
            id: true,
          }
        }
      }
    });

    if (!client) {
      throw new HttpException('Client not found', 404);
    }

    await this.beforeSoftRemove(client);

    await this.clientsRepository.softDelete(id);

    return {
      message: 'Client removed',
    };
  }

  async beforeSoftRemove(client: ClientEntity) {

    const subscription = client.subscriptions;

    if (subscription) {

      const payments = subscription.payments;

      if (payments.length > 0) {

        const hasOnePaid = payments.some(payment => payment.isPaid && payment.status === 'success');

        if (hasOnePaid) throw new HttpException('No se puede eliminar debido a que el cliente tiene pagos asociados.', 400);

        await Promise.allSettled(payments.map(async payment => await this.paymentsRepository.delete(payment.id)));

      } else {
        await this.subscriptionRepository.delete(subscription.id);
      }
    }

    const isolatedPayments = await this.paymentsRepository.find({
      where: {
        client: {
          id: client.id,
        },
        type: "isolated",
      }
    });

    if (isolatedPayments.length > 0) {

      const hasOnePaid = isolatedPayments.some(payment => payment.isPaid && payment.status === 'success');

      if (hasOnePaid) throw new HttpException('No se puede eliminar debido a que el cliente tiene pagos asociados.', 400);
      await Promise.allSettled(isolatedPayments.map(async payment => await this.paymentsRepository.delete(payment.id)));
    }

    // delete subscriptions

    await this.subscriptionRepository.delete({
      client: {
        id: client.id,
      }
    }/* , {
      isActive: false,
    } */);


  }


  async messageRead(data: MessageReadDto) {

    const client = await this.clientsRepository.findOne({
      where: {
        monexClabe: data.monexClabe,
      },
      select: {
        id: true,
        metadata: true,
        monexClabe: true,
        email: true,
      }
    });

    if (!client) {
      throw new HttpException('Client not found', 404);
    }

    if (!client.metadata) {
      client.metadata = {};
    }

    // client.metadata[data.messageId] = true;
    const count = client.metadata?.new_account_show_count
    if (count || count === 0) {
      client.metadata.new_account_show_count = count + 1;
    }

    await this.clientsRepository.update(client.id, { metadata: client.metadata });

    return {
      message: 'Message read',
      count: client.metadata.new_account_show_count
    };

  }

  async getClientByAssociateId(associateId: string) {
    try {
      const client = await this.clientsRepository.findOne({
        where: {
          associateId: associateId,
        }
      });
      if (!client) {
        throw new HttpException("Client not found", HttpStatus.NOT_FOUND);
      }
      return client;
    } catch (error) {
      this.logger.log(error);
      throw error;
    }

  }

  async validateTaxInformation({ id, facturapiId }: { id: string; facturapiId?: string | null }) {

    if (facturapiId) {

      const { is_valid, errors } = await facturapi.customers.validateTaxInfo(facturapiId);
      if (!is_valid) {
        // throw new HttpException('La información fiscal no es correcta', 400);

        return {
          message: 'La información fiscal no es correcta',
          is_valid,
          errors,
        };

      }

      await this.clientsRepository.update(id, {
        is_valid_tax_info: is_valid,
      });

      return {
        message: 'La información fiscal es correcta',
        is_valid,
      };
    } else {

      const client = await this.clientsRepository.findOne({
        where: {
          id,
        }
      });


      if (!client) {
        throw new HttpException('Client not found', 404);

      }

      const createCustomer: Customer = {
        legal_name: client.legal_name,
        email: client.email,
        tax_id: client.rfc,
        tax_system: client.tax_system,
        address: {
          zip: client.zip,
        }
      }

      const facturapiCustomer = await facturapi.customers.create(createCustomer);

      // await this.clientsRepository.update(id, {
      //   facturapiId: facturapiCustomer.id,
      // });


      const { is_valid, errors } = await facturapi.customers.validateTaxInfo(facturapiCustomer.id);

      if (!is_valid) {
        await this.clientsRepository.update(id, {
          facturapiId: facturapiCustomer.id,
          is_valid_tax_info: is_valid,
        });

        return {
          message: 'La información fiscal no es correcta',
          is_valid,
          errors,
        };

      }

      return {
        message: 'Tax info validated',
        is_valid,
      };


    }

  }

}

function capitalizeFirstLetterOfEachWord(name: string): string {
  return name
    .toLowerCase() // Convertimos todo a minúsculas primero
    .split(' ') // Dividimos por espacios para separar las palabras
    .map(word => word.charAt(0).toUpperCase() + word.slice(1)) // Capitalizamos la primera letra de cada palabra
    .join(' '); // Unimos las palabras de nuevo
}