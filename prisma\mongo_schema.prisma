
generator client {
  provider = "prisma-client-js"
  output = "../node_modules/@prisma/mongo"
  binaryTargets = env("PRISMA_BINARY_TARGETS")
}

datasource db {
  provider = "mongodb"
  url      = env("MONGO_URI")
}

model TestModel {
  id String @id @default(auto()) @map("_id") @db.ObjectId
  model String
  createdAt DateTime @default(now())
}

model weeklydates {
  id String @id @default(auto()) @map("_id") @db.ObjectId
  week String
  endDate DateTime
  createdAt DateTime @default(now())
}