import { HttpException, Injectable } from '@nestjs/common';
import { AddTemporalSubscriptionProducts } from '../dto/update-subscription.dto';
import { Repository } from 'typeorm';
import { SubscriptionEntity } from '../entities/subscription.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { SubscriptionProductEntity } from '@/app/subscription-products/entities/subscription-product.entity';
import { isUUID } from 'class-validator';

@Injectable()
export class SubscriptionsUpdateService {

  constructor(
    @InjectRepository(SubscriptionEntity) private readonly subscriptionRepository: Repository<SubscriptionEntity>,
    @InjectRepository(SubscriptionProductEntity) private readonly subscriptionProductsRepository: Repository<SubscriptionProductEntity>,
  ) { }


  async addTemporalSubscriptionProducts(id: string, body: AddTemporalSubscriptionProducts) {

    const where = isUUID(id) ? { client: { id } } : { id };

    const subscription = await this.subscriptionRepository.findOne({
      where,
      relations: ['products', 'client'],
      select: {
        id: true,
        products: true,
        client: {
          id: true,
          region: true,
        }
      }
    });

    if (!subscription) {
      throw new HttpException('Subscription not found', 404);
    }

    for (const product of body.temporalProducts) {


      // const total = product.price * product.quantity;
      // const tax = total * product.taxRate;
      // const subTotal = total - tax;

      // make the same as previous but round to 2 decimals
      
      const total = +(product.price * product.quantity).toFixed(2);

      const taxRate = product.taxRate || 0.16;
      const tax = +(total * (1 - (taxRate === 1 ? 0 : taxRate))).toFixed(2);
      const subTotal = +(total - tax).toFixed(2);

      const createProduct = {
        ...product,
        total,
        tax,
        subTotal,
        region: subscription.client.region,
      }

      const productEntity = this.subscriptionProductsRepository.create({
        ...createProduct,
        subscription,
      });

      await this.subscriptionProductsRepository.save(productEntity);

      subscription.products.push(productEntity);
    }

    const amounts = subscription.products.reduce((acc, product) => {
      acc.total += product.total;
      acc.subTotal += product.subTotal;
      acc.tax += product.tax;
      return acc;
    }, { total: 0, subTotal: 0, tax: 0 });

    await this.subscriptionRepository.save({ ...subscription, ...amounts });

    return {
      message: 'Subscription updated successfully with temporal products',
    }
  }


  async removeTemporalSubscriptionProducts(id: string) {
    const where = isUUID(id) ? { client: { id } } : { id };

    const subscription = await this.subscriptionRepository.findOne({
      where,
      relations: ['products'],
      select: {
        id: true,
        products: true,
        use_cfdi: true,
        use_cfdi_description: true,
      }
    });


    if (!subscription) {
      throw new HttpException('Subscription not found', 404);
    }

    const temporalProducts = subscription.products.filter(product => product.isTemporal);

    for (const product of temporalProducts) {
      await this.subscriptionProductsRepository.delete(product.id);
    }

    subscription.products = subscription.products.filter(product => !product.isTemporal);


    const { total, subTotal, tax } = subscription.products.reduce((acc, product) => {
      console.log('product', product)
      const quantity = product.quantity || 1;

      const total = product.price * quantity;
      const subTotal = total - (total * product.taxRate);
      acc.total += total;
      acc.subTotal += subTotal;
      acc.tax += total - subTotal;
      return acc;

    }, { total: 0, subTotal: 0, tax: 0 });

    console.log('total', total, subTotal, tax)
    // await this.subscriptionRepository.update(subscription.id, { total, subTotal, tax });

    subscription.total = total;
    subscription.subTotal = subTotal;
    subscription.tax = tax;
    console.log('subscription', subscription);
    const updatedSubscription = await this.subscriptionRepository.save(subscription);


    // await this.subscriptionRepository.update(subscription.id, { total, subTotal, tax });

    return {
      message: 'Subscription updated successfully with temporal products removed',
      data: updatedSubscription,
    }

  }


}
