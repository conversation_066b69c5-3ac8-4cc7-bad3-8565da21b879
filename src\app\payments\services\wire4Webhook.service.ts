import { HttpException, Injectable, Logger } from '@nestjs/common';
import { PaymentsEntity } from '@app/payments/entities/payment.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PAYMENTS_ERROR_CHANNEL, wire4Data } from '@/constants';
import { ClientEntity } from '@/app/clients/entities/client.entity';
import facturapi from '@facturapi';
import { TaxabilityType } from '@/facturapi/Products';
import { ItemProps, PaymentFormType } from '@/facturapi/Invoices';
import { PaymentTransactionService } from './payment-transactions.service';
import { Wire4WebhookDataEntity } from '../entities/wire4-webhook.entity';
import { PaymentTransactionsEntity, PaymentTransactionStatusEnum } from '../entities/payment-transactions.entity';
import { SubscriptionEntity } from '@/app/subscriptions/entities/subscription.entity';
import { getLocalDate } from '@/lib/dates';
import { subDays } from 'date-fns';
import { CFDI_KEYS_TYPE, G03_LIST } from '@/sat/cfdi.types';
import { Wire4FilterDto } from '../dto/wire4-filter.dto';
import { slackApi } from '@/logger.config';

type PaymentData = {
  payment: PaymentsEntity;
  transactionTotal: number;
  remaining: number;
}

@Injectable()
export class Wire4WebHookService {
  private readonly logger = new Logger(Wire4WebHookService.name);
  constructor(
    @InjectRepository(PaymentsEntity) private readonly paymentRepository: Repository<PaymentsEntity>,
    @InjectRepository(ClientEntity) private readonly clientRepository: Repository<ClientEntity>,
    @InjectRepository(Wire4WebhookDataEntity) private readonly wire4TransactionsRepository: Repository<Wire4WebhookDataEntity>,
    @InjectRepository(SubscriptionEntity) private readonly subscriptionRepository: Repository<SubscriptionEntity>,
    @InjectRepository(PaymentTransactionsEntity) private readonly paymentTransactionRepository: Repository<PaymentTransactionsEntity>,
    private readonly paymentTransactionService: PaymentTransactionService,
    // @InjectRepository(PaymentTransactionsEntity) private readonly paymentTransactionsRepository: Repository<PaymentTransactionsEntity>,
  ) { }

  async webhook(body: any) {
    let wire4data = null;
    try {
      const { object, data, id } = body;
      this.logger.log(`[Wire4WebHookService] - Webhook received -> Data:${JSON.stringify(data)}`);
      wire4data = data;
      // The webhook always has to return a 200 status code, that's why we return ok and an empty object even if the process fails
      const existingTransactionRecord = await this.findWebhookDataByIdField(id);
      //check if wire4 transactions has been received earlier if yes then return
      if(existingTransactionRecord){
        this.logger.log(`[Wire4WebHookService] - Duplicate Webhook received -> Data:${JSON.stringify(data)}`);
        return {};
      }
      await this.wire4TransactionsRepository.save({ body });

      if (object === wire4Data.transfers.response.spei.incomming) {
        await this.processIncomingPayment(data);
      }

      return {};
    } catch (error) {
      this.logger.error(
        `[Wire4WebHookService] Failed to process payment. ${JSON.stringify(error)}`,
      );
      //send slack message
      try {
        await slackApi.chat.postMessage({
          text: `Error while processing this payment: ${JSON.stringify(wire4data)}`,
          channel: PAYMENTS_ERROR_CHANNEL!,
        });
      } catch (error) {
        this.logger.error(
          `[Wire4WebHookService] Error sending slack message ${JSON.stringify(error)}`,
        );
        return {};
      }
      return {};
    }
  }

  async findWebhookDataByIdField(
    eventId: string
  ): Promise<Wire4WebhookDataEntity | undefined> {
    const webhookData = await this.wire4TransactionsRepository.createQueryBuilder('webhook')
      .where("webhook.body ->> 'id' = :eventId", { eventId })
      .select(['webhook.id', 'webhook.body', 'webhook.createdAt', 'webhook.updatedAt'])
      .getOne();
  
    return webhookData;
  }  

  async processIncomingPayment(data: any) {
    const clabe = data.depositant_clabe || data.sender_account;
    if (!clabe) return;
    const monex_description = data.monex_description;
    const conceptPaymentString = monex_description
      .split('|')
      .slice(-1)[0]
      .trim();
    const concept = conceptPaymentString.split(':')[1].trim();
    this.logger.log(`[Wire4WebHookService] concept: ${concept}`);
    this.logger.log(`[Wire4WebHookService] clabe: ${clabe}`);
    let payments: PaymentsEntity[] = null;
    payments = await this.findPaymentsByConcept(concept, clabe);
    this.logger.log(
      `[Wire4WebHookService] findPaymentsByConcept payments:${payments.length}`,
    );
    if (!payments || payments.length === 0) {
      payments = await this.findClientPayments(clabe);
      this.logger.log(
        `[Wire4WebHookService] findClientPayments payments:${payments.length}`,
      );
    }
    if (payments && payments.length > 0) {
      // Process payments if found
      const paymentDataArr:PaymentData[] = [];
      // Case 1: Incoming payment completes the payment total
      for (const payment of payments) {
        this.logger.log(`[Wire4WebHookService] payment.id:${payment.id}`);
        const paymentTransactions = await this.findPaymentTransactions(payment);
        this.logger.log(
          `[Wire4WebHookService] - paymentTransactions: ${paymentTransactions.length}`,
        );
        const transactionTotal =
          paymentTransactions?.reduce(
            (total, transaction) => total + transaction.amount,
            0,
          ) || 0;
        this.logger.log(
          `[Wire4WebHookService] transactionTotal ${transactionTotal}`,
        );
        paymentDataArr.push({
          payment,
          transactionTotal,
          remaining: payment.total - transactionTotal
        })
        // Also handles case where there are no previous payment transactions
        if (
          this.isPaymentAmountValid(
            payment.total - transactionTotal,
            data.amount,
          )
        ) {
          this.logger.log(`[Wire4WebHookService] valid payment`);
          await this.handleValidPayment(payment, data);
          return; // Exit early as payment is resolved
        }
      }

      // Case 2: Incoming payment can be used for a partial payment
      for (const paymentData of paymentDataArr) {
        
        if (paymentData.payment.total > paymentData.transactionTotal + data.amount) {
          this.logger.log(`[Wire4WebHookService] partial payment`);
          await this.handlePartialPayment(paymentData.payment, data);
          return; // Exit early as payment is resolved
        }
      }

      // function has still not returned means we need to check for over payment
      // Sort payments by remaining amount in descending order
      if (paymentDataArr && paymentDataArr.length > 0) {
        paymentDataArr.sort((a, b) => b.remaining - a.remaining); // Largest total first
      }
      if (data.amount > paymentDataArr[0].remaining) {
        this.logger.log(
          `[Wire4WebHookService] valid payment for incoming payment:${data.amount} bigger than payment total ${JSON.stringify(paymentDataArr[0].payment)}`,
        );
        await this.handleValidPayment(paymentDataArr[0].payment, data);
        return;
      }
    }
    // If no payment was resolved, flag for finance department review
    await this.flagForFinanceReview(data);
  }

  async flagForFinanceReview(data: any){
    this.logger.log(`[Wire4WebHookService] flaged for FinanceReview: ${JSON.stringify(data)}`);
    //send slack message
    try{
      await slackApi.chat.postMessage({
        text: `This Payment could not be processed: ${JSON.stringify(data)}`,
        channel: PAYMENTS_ERROR_CHANNEL!,
      });
    } catch (error){
      this.logger.error(`[Wire4WebHookService] Error sending slack message ${JSON.stringify(error)}`);
    }
    //send email
  }

  async findPaymentTransactions(payment: PaymentsEntity){
    const results = await this.paymentTransactionRepository.find({
      where: {
        payment: {
          id: payment.id
        }
      },
      order: {
        createdAt: 'DESC'
      }
    });
    if(results &&  results.length > 0){
      results.map((result)=>{
        this.logger.log(`[Wire4WebHookService] - findPaymentTransactions result:${result.id}`);
      });
    }
    return results;
  }

  getFromLastWeekToToday() {
    const today = getLocalDate();
    const lastWeek = subDays(today, 7);
    return lastWeek;
  }

  async findPaymentsByConcept(concept: string, clabe: string){
    return await this.paymentRepository.find({
      where: {
        concept,
        isPaid: false,
        client: {
          monexClabe: clabe,
        },
        // createdAt: MoreThanOrEqual(this.getFromLastWeekToToday()),
      },
      relations: ['client', 'subscription.products'],
      order: {
        createdAt: 'DESC'
      }
    });
  }

  async findPaymentByConcept(concept: string, clabe: string) {
    return await this.paymentRepository.findOne({
      where: {
        concept,
        isPaid: false,
        client: {
          monexClabe: clabe,
        },
        // createdAt: MoreThanOrEqual(this.getFromLastWeekToToday()),
      },
      relations: ['client', 'subscription.products'],
      order: {
        createdAt: 'DESC'
      }
    });
  }

  async findClientPayments(clabe: string) {
    return await this.paymentRepository.find({
      where: {
        isPaid: false,
        client: [
          { monexClabe: clabe },
          { contractNumber: clabe }
        ],
        // createdAt: MoreThanOrEqual(this.getFromLastWeekToToday()),
      },
      relations: ['client', 'subscription.products'],
      order: {
        createdAt: 'DESC'
      }
    });
  }

  isPaymentAmountValid(paymentAmount, incomingPaymentAmount): boolean {
    const permitCents = 0.5;
    // Check if the payment is within permitCents
    return Math.abs(paymentAmount - incomingPaymentAmount) <= permitCents;
  }

  /* IF ITS CORRECT, WE MAKE THE PROCESS */
  async handleValidPayment(payment: PaymentsEntity, data?: any, hasSubscription: boolean = true) {
    let facturapiId = payment.client.facturapiId;
    if (!facturapiId) {
      facturapiId = await this.createFacturapiCustomer(payment.client);
    }
    this.logger.log(`[Wire4WebHookService] - handleValidPayment -> facturapiId:${facturapiId}`);
    const isValid = facturapiId ? await this.validateTaxInfo(facturapiId) : false;
    this.logger.log(`[Wire4WebHookService] - handleValidPayment -> isValid:${isValid}`);
    await this.fixClientTaxInfo(payment.client, isValid);
    this.logger.log(`[Wire4WebHookService] - fixClientTaxInfo`);
    const items = this.createItemsForPayment(payment);
    if (isValid) {
      await this.createInvoiceAndSend(payment, facturapiId, items);
    } else {
      await this.createReceiptAndSend(payment, items);
    }
    /* SAVE THE TRANSACTION IN DB */
    if (data) {
      await this.paymentTransactionService.create({
        payment: payment,
        monexData: data,
        description: 'Pago realizado correctamente',
        amount: data.amount,
        status: PaymentTransactionStatusEnum.success,
      });
      this.logger.log(`[Wire4WebHookService] - Payment transaction saved`);
    }
  }

  async fixClientTaxInfo(client: ClientEntity, isValid: boolean) {
    try {
      if (!client.is_valid_tax_info){ // if the client tax info is not valid or is null, we update it

        if(isValid){ // if the tax info is valid, we update the client with the tax system
          const customer = await facturapi.customers.retrieve(client.facturapiId);
          const tax_system = customer.tax_system;
          client.tax_system = tax_system;
          await this.clientRepository.update(client.id, {
            is_valid_tax_info: true,
            tax_system,
          });
        } else {
          await this.clientRepository.update(client.id, {
            is_valid_tax_info: false,
          });
        }

      }
    } catch (error) {
      this.logger.log(`[Wire4WebHookService] - fixClientTaxInfo error:${JSON.stringify(error)}`);
    }
  }

  createItemsOnSubscription(payment: PaymentsEntity) {
    const items = payment.subscription.products.map(product => {
      return {
        product: {
          description: product.name,
          product_key: product.product_key,
          price: product.price,
          tax_included: true,
          taxability: '02' as TaxabilityType,
          taxes: [{
            rate: product.taxRate,
            factor: product.taxFactor,
            type: product.taxType,
          }],
          unit_key: product.unit_key,
          unit_name: product.measurementUnit,
          sku: product.id,
        },
        quantity: product.quantity,
      }
    })
    return items;
  }

  createItemsForPayment(payment: PaymentsEntity) {

      // Fix items creation for payments
    if (!payment.products[0].product_key || !payment.products[0].unit_key || !payment.products[0].taxType) {
      return this.createItemsOnSubscription(payment);
    }
    else {
      const items = payment.products.map(product => {
        return {
          product: {
            description: product.name,
            product_key: product.product_key,
            price: product.price ? product.price : product.total / product.quantity,
            tax_included: true,
            taxability: '02' as TaxabilityType,
            taxes: [{
              rate: product.taxRate,
              factor: product.taxFactor,
              type: product.taxType,
            }],
            unit_key: product.unit_key,
            unit_name: product.measurementUnit,
            sku: product.id,
          },
          quantity: product.quantity,
        }
      })
      return items;
    }

  }

  createItemsForNonSubscription(payment: PaymentsEntity) {

    const items = payment.products.map(product => {
      return {
        product: {
          description: product.name,
          product_key: product.product_key,
          price: product.total,
          tax_included: true,
          taxability: '02' as TaxabilityType,
          taxes: [{
            rate: product.taxRate,
            factor: product.taxFactor,
            type: product.taxType,
          }],
          unit_key: product.unit_key,
          unit_name: product.measurementUnit,
          sku: product.id,
        },
        quantity: product.quantity,
      }
    })
    return items;

  }

  /* CREATE A CUSTOMER/CLIENT IN FACTURAPI DB */
  async createFacturapiCustomer(client: PaymentsEntity['client']) {
    try {
      const objectCustomer = {
        legal_name: client.legal_name,
        tax_id: client.rfc,
        tax_system: client.tax_system || '616',
        address: { zip: client.zip },
        email: client.email,
        phone: client.phone,
      }
      this.logger.log(`[Wire4WebHookService] - createFacturapiCustomer objectCustomer:${JSON.stringify(objectCustomer)}`);
      const customer = await facturapi.customers.create(objectCustomer);
      this.logger.log(`[Wire4WebHookService] - createFacturapiCustomer customer:${JSON.stringify(customer)}`);
      await this.clientRepository.update(client.id, {
        facturapiId: customer.id,
        tax_system: customer.tax_system,
        is_valid_tax_info: true,
      });
      this.logger.log(`[Wire4WebHookService] - createFacturapiCustomer client updated client.id:${client.id}`);
      return customer.id;
    } catch (error) {
      this.logger.log(`[Wire4WebHookService] - createFacturapiCustomer error:${JSON.stringify(error)}`);
      return null;
    }
  }

  /* VALIDATE THE TAX INFO ITS CORRECT TO CREATE A INVOICE, IF NOT, CREATE A RECEIPT */
  async validateTaxInfo(facturapiId: string) {
    try {
      const response = await facturapi.customers.validateTaxInfo(facturapiId);
      this.logger.log(`[Wire4WebHookService] - validateTaxInfo response:${JSON.stringify(response)}`);
      return response.is_valid;
    } catch (error) {
      this.logger.log(`[Wire4WebHookService] - validateTaxInfo error:${JSON.stringify(error)}`);
      return null;
    }
  }

  /* CREATE THE INVOICE AND SEND IT */
  async createInvoiceAndSend(payment: PaymentsEntity, facturapiId: string, items: ItemProps[]) {
    const payment_form = (payment.subscription?.payment_form || payment.payment_form || '03') as PaymentFormType;
    this.logger.log(`[Wire4WebHookService] - createInvoiceAndSend payment_form:${payment_form}`);
    const regimen = (payment.client.tax_system || '625');
    this.logger.log(`[Wire4WebHookService] - createInvoiceAndSend regimen:${regimen}`);
    let use: CFDI_KEYS_TYPE = 'G03';

    if (!G03_LIST.includes(regimen)) {
      this.logger.log(`[Wire4WebHookService] - createInvoiceAndSend regimen is invalid:${regimen} chaning use to S01`);
      use = "S01";
    }
    this.logger.log(`[Wire4WebHookService] - createInvoiceAndSend regimen:${regimen} use:${use}`);
    try {
      const createInvoice = await facturapi.invoices.create({
        customer: facturapiId,
        payment_form,
        use,
        items,
        series: payment.client.region,
      });
      this.logger.log(`[Wire4WebHookService] - createInvoiceAndSend createInvoice.id:${createInvoice.id}`);
      try {
        const emailSent = await facturapi.invoices.sendInvoiceByEmail(createInvoice.id, { email: payment.client.email });
        this.logger.log(`[Wire4WebHookService] - createInvoiceAndSend sendInvoiceByEmail:${JSON.stringify(emailSent)}`);
      } catch (error) {
        this.logger.log(`[Wire4WebHookService] - createInvoiceAndSend sendInvoiceByEmail error:${JSON.stringify(error)}`);
      }
      await this.updatePayment(payment, createInvoice.id);
      this.logger.log(`[Wire4WebHookService] - createInvoiceAndSend updatePayment success`);
    } catch (error) {
      this.logger.log(`[Wire4WebHookService] - createInvoiceAndSend catch error:${JSON.stringify(error)}`);
      await this.createReceiptAndSend(payment, items);
      this.logger.log(`[Wire4WebHookService] - createInvoiceAndSend catch createReceiptAndSend`);
    }
  }

  /* CREATE THE RECEIPT AND SEND IT */
  async createReceiptAndSend(payment: PaymentsEntity, items: ItemProps[]) {
    console.log('CREATING RECEIPT TO', payment.client.contractNumber);
    const isASoldPaymetn = payment.products.find(product => product.product_key === 'sold');
    if (isASoldPaymetn) {
      console.log('PAGO DE VENTA');
      await this.updatePayment(payment);
      return;
    }

    try {
      const receipt = await facturapi.receipts.create({
        items,
        payment_form: payment.subscription?.payment_form || payment.payment_form || '03',
      });
      this.logger.log(`[Wire4WebHookService] - createReceiptAndSend receipt: ${JSON.stringify(receipt)}`)
      await this.updatePayment(payment, null, receipt.id);
      this.logger.log(`[Wire4WebHookService] - createReceiptAndSend updatePayment payment updated}`)
      const sendReceipt = await facturapi.receipts.sendReceiptByEmail(receipt.id, { email: payment.client.email });
      this.logger.log(`[Wire4WebHookService] - createReceiptAndSend sendReceiptByEmail sendReceipt:${JSON.stringify(sendReceipt)}}`)
    } catch (error) {
      this.logger.log(`[Wire4WebHookService] - createReceiptAndSend catch error:${JSON.stringify(error)}`);
      await this.updatePayment(payment);
      this.logger.log(`[Wire4WebHookService] - createReceiptAndSend catch updatePayment payment updated}`)
    }
  }

  async handlePartialPayment(payment: PaymentsEntity, data: any) {
    await this.paymentTransactionService.create({
      payment: payment,
      monexData: data,
      description: 'El monto recibido es menor al total del pago',
      amount: data.amount,
      status: PaymentTransactionStatusEnum.failed,
    });
    this.logger.log(`[Wire4WebHookService] Partial payment made paymentId:${payment.id} for amount ${data.amount}`);
  }

  async updatePayment(payment: PaymentsEntity, invoice?: string, receipt?: string) {
    if (invoice) payment.invoiceId = invoice;
    if (receipt) payment.receiptId = receipt;
    const today = getLocalDate();
    payment.isPaid = true;
    payment.status = 'success';
    payment.paidAt = today;

    await this.paymentRepository.save(payment);
    this.logger.log('[Wire4WebHookService] - updatePayment payment marked as paid');
    if(payment.subscription){
      await this.subscriptionRepository.update(payment.subscription.id, {
        paymentNumber: payment.subscription.paymentNumber + 1,
      });
      this.logger.log('[Wire4WebHookService] - updatePayment subscription paymentNumber incremented');
    }
  }

  async getFilteredWire4WebhookData(filterDto: Wire4FilterDto) {

    const payment = await this.paymentRepository.findOne({where:{id: filterDto.paymentId}});
    if(!payment){
      throw new HttpException('Payment not found', 404);
    }
    const query = this.wire4TransactionsRepository.createQueryBuilder('wire4_webhook_data');
    query.andWhere('wire4_webhook_data.createdAt > :paymentCreatedAt', { paymentCreatedAt: payment.createdAt.toUTCString() });
    // Apply filters
    if (filterDto.depositant) {
      query.andWhere("wire4_webhook_data.body->'data'->>'depositant' ILIKE :depositant", {
        depositant: `%${filterDto.depositant}%`,
      });
    }
    if (filterDto.description) {
      query.andWhere("wire4_webhook_data.body->'data'->>'description' ILIKE :description", {
        description: `%${filterDto.description}%`,
      });
    }
    if (filterDto.amount) {
      query.andWhere("wire4_webhook_data.body->'data'->>'amount' = :amount", {
        amount: filterDto.amount,
      });
    }
    if (filterDto.senderRfc) {
      query.andWhere("wire4_webhook_data.body->'data'->>'sender_rfc' = :senderRfc", {
        senderRfc: filterDto.senderRfc,
      });
    }
    if (filterDto.claveRastreo) {
      query.andWhere("wire4_webhook_data.body->'data'->>'clave_rastreo' = :claveRastreo", {
        claveRastreo: filterDto.claveRastreo,
      });
    }
    if (filterDto.depositDate) {
      query.andWhere("wire4_webhook_data.body->'data'->>'deposit_date' = :depositDate", {
        depositDate: filterDto.depositDate,
      });
    }
    if (filterDto.depositantClabe) {
      query.andWhere("wire4_webhook_data.body->'data'->>'depositant_clabe' = :depositantClabe", {
        depositantClabe: filterDto.depositantClabe,
      });
    }

    // Pagination
    const page = filterDto.page || 1;
    const limit = filterDto.limit || 10;
    const skip = (page - 1) * limit;
    query.skip(skip).take(limit);

    // Execute query
    const [results, total] = await query.getManyAndCount();

    return {
      data: results,
      meta: {
        total,
        page,
        lastPage: Math.ceil(total / limit),
      },
    };
  }
}