
import { RegionsEnum, RegionsType } from '@/types/entities.types';
import { Transform } from 'class-transformer';
import { IsEnum, IsOptional, IsString } from "class-validator";

export class FindAllSubscriptionProductsQueryDto {
  @IsOptional()
  @Transform(({ value }) => value === 'true', { toClassOnly: true }) // Transforma a booleano
  readonly withClient?: boolean;

  @IsOptional()
  // @IsDefined({ message: 'Region Query is required' })
  @IsString()
  @IsEnum(RegionsEnum)
  readonly region?: RegionsType;

  @Transform(({ value }) => Boolean(value))
  @IsOptional()
  readonly withSubscription?: boolean;

  @IsOptional()
  @IsString()
  subscriptionId?: string;

}