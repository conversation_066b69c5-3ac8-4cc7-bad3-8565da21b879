import { PartialType } from '@nestjs/mapped-types';
import { CreateSubscriptionDto } from './create-subscription.dto';
import { ArrayMinSize, ArrayNotEmpty, IsArray, IsBoolean, IsDefined, IsEnum, IsNumber, IsOptional, IsString, Max, Min, ValidateNested } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { TaxFactorEnum, TaxFactorType, TaxTypesEnum, TaxTypesType } from '@/app/products/entities/product.entity';
import { RegionsEnum, RegionsType } from '@/types/entities.types';

export class UpdateSubscriptionDto extends PartialType(CreateSubscriptionDto) { 

  // @IsOptional()
  // @IsNumber()
  // total: number;

  // @IsOptional()
  // @IsNumber()
  // subTotal: number;

  // @IsOptional()
  // @IsNumber()
  // tax: number;

}

export class UpdateSubscriptionStatusDto {

  @IsDefined()
  @IsBoolean()
  status: boolean;
}

export class AddTemporalSubscriptionProducts {

  @IsDefined()
  @IsArray()
  @ArrayNotEmpty({ message: 'At least one product is required' })
  @ArrayMinSize(1, { message: 'At least one product is required' })
  @ValidateNested()
  @Type(() => CreateSubsProductDto)
  temporalProducts: CreateSubsProductDto[];

}


export class CreateSubsProductDto {

  @IsDefined()
  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  description: string;

  @IsDefined()
  @IsNumber()
  @Min(1)
  @Transform(({ value }) => parseFloat(parseFloat(value).toFixed(2)))
  price: number;


  @IsOptional()
  @IsNumber()
  @Min(1)
  quantity: number = 1;


  @IsOptional()
  @IsString()
  product_key: string;

  @IsOptional()
  @IsString()
  unit_key: string;

  @IsOptional()
  @IsString()
  measurementUnit: string;

  @IsOptional()
  @IsBoolean()
  isTemporal: boolean = true;

  @IsOptional()
  @IsNumber()
  discount: number = 0;

  @IsOptional()
  @IsNumber()
  @Min(0.01)
  @Max(1)
  taxRate: number = .16;

  @IsOptional()
  @IsEnum(TaxFactorEnum)
  taxFactor: TaxFactorType = TaxFactorEnum.Tasa;

  @IsOptional()
  @IsEnum(TaxTypesEnum)
  taxType: TaxTypesType = TaxTypesEnum.IVA;


  // @IsDefined()
  // @IsEnum(RegionsEnum)
  // region: RegionsType;

}