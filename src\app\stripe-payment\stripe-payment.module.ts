import { Module } from '@nestjs/common';
import { StripePaymentService } from './stripe-payment.service';
import { StripePaymentController } from './stripe-payment.controller';
import { ClientsModule } from '../clients/clients.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { StripeCustomerInfoEntity } from './entities/stripe-customer.entity';
import { StripeSetupIntentEntity } from './entities/stripe-setup-intent.entity';
import { StripePaymentTransactionEntity } from './entities/stripe-payment-transaction.entity';
import { PaymentsEntity } from '../payments/entities/payment.entity';

@Module({
  imports: [ 
    TypeOrmModule.forFeature([StripeCustomerInfoEntity, StripeSetupIntentEntity,  StripePaymentTransactionEntity,
      PaymentsEntity
    ])
  ],
  controllers: [StripePaymentController],
  providers: [StripePaymentService],
  exports: [StripePaymentService],
})
export class StripePaymentModule {}
