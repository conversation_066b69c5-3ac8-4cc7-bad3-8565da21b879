import { OnWorkerEvent, Processor, WorkerHost } from '@nestjs/bullmq';
import { Injectable, Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PaymentsEntity } from '@/app/payments/entities/payment.entity';
import facturapi from '@/facturapi';
import { InvoiceEmailQueueService } from '../invoice-email-queue/invoice-email-queue.service';
import { Customer } from '@/facturapi/Customers';
import { CFDI_KEYS_TYPE, G03_LIST } from '@/sat/cfdi.types';
import { PaymentFormType } from '@/facturapi/Invoices';
import { INVOICE_QUEUE } from '@/constants/queues';

@Processor({
  name: INVOICE_QUEUE,
})
export class InvoiceProcessor extends WorkerHost {
  private readonly logger = new Logger(InvoiceProcessor.name);

  constructor(
    @InjectRepository(PaymentsEntity)
    private readonly paymentsRepository: Repository<PaymentsEntity>,
    private readonly invoiceEmailQueueService: InvoiceEmailQueueService,
  ) {
    super();
  }

  async process(job: Job<PaymentsEntity, any, string>): Promise<any> {
    const payment = job.data;
    this.logger.log({
      message: `[InvoiceProcessor] - Procesando facturación para pago: ${payment.id}`,
    });

    try {
      let invoiceId;

      // Verificar si el cliente tiene RFC y datos fiscales
      if (payment.client.rfc && payment.client.rfc.length > 10) {
        // Facturar con datos fiscales del cliente
        const customer: Customer = {
          legal_name: payment.client.legal_name || payment.client.name,
          tax_id: payment.client.rfc,
          tax_system: payment.client.tax_system || '601',
          address: {
            zip: payment.client.zip || '11320',
          },
        };

        const response = await facturapi.receipts.invoice(payment.receiptId, {
          customer,
          use: G03_LIST.includes(payment.client.use_cfdi) ? payment.client.use_cfdi : 'G03',
          series: payment.client.region,
        });

        invoiceId = response.id;
      } else {
        // Facturar con RFC genérico (público en general)
        const customer: Customer = {
          legal_name: payment.client.legal_name || payment.client.name,
          tax_id: 'XAXX010101000', // RFC genérico
          tax_system: '616',
          address: {
            zip: payment.client.zip || '11320',
          },
        };

        const response = await facturapi.receipts.invoice(payment.receiptId, {
          customer,
          use: 'S01',
          series: payment.client.region,
        });

        invoiceId = response.id;
      }

      // Actualizar el pago con el ID de la factura
      await this.paymentsRepository.update(payment.id, { invoiceId });

      // Enviar correo electrónico con la factura
      await this.invoiceEmailQueueService.sendInvoiceEmail({
        invoiceId,
        paymentId: payment.id,
        clientEmail: payment.client.email,
        clientName: payment.client.name,
      });

      return { success: true, invoiceId };
    } catch (error: any) {
      this.logger.error(`Error al procesar factura para pago ${payment.id}: ${error.message}`, error.stack);
      return { success: false, error: error.message };
    }
  }

  @OnWorkerEvent('active')
  onActive(job: Job) {
    this.logger.log(`[InvoiceProcessor] - Activo ${job.id}`);
  }

  @OnWorkerEvent('completed')
  onCompleted(job: Job) {
    this.logger.log(`[InvoiceProcessor] - Completado ${job.id}`);
  }

  @OnWorkerEvent('failed')
  onFailed(job: Job, error: Error) {
    this.logger.error(`[InvoiceProcessor] - Fallido ${job.id}: ${error.message}`, error.stack);
  }
}
