import { ClientEntity } from '@/app/clients/entities/client.entity';
import { generateRandomId } from '@/lib/randomId';
import { BeforeInsert, BeforeUpdate, Column, Entity, JoinColumn, ManyToOne, OneToMany, OneToOne, PrimaryColumn } from 'typeorm';
import { SubscriptionProductEntity } from "./../../subscription-products/entities/subscription-product.entity";
import { RegionsEnum, RegionsType } from '@/types/entities.types';
import { PaymentsEntity } from '@/app/payments/entities/payment.entity';
import { CFDI_KEYS_TYPE, CFDI_DATA, CFDI_KEYS_ENUM } from '@/sat/cfdi.types';
import { PaymentFormType } from '@/facturapi/Invoices';

@Entity({ name: 'subscriptions' })
export class SubscriptionEntity {

  @PrimaryColumn()
  id: string;

  @OneToOne(() => ClientEntity, client => client.subscriptions)
  @JoinColumn()
  client: ClientEntity; // Client

  @Column({
    type: 'int',
    default: 1,
  })
  paymentNumber: number; // paymentNumber

  @Column({
    type: 'boolean',
    default: true,
  })
  isActive: boolean; // isActive

  @Column({
    type: 'float',
    nullable: true,
  })
  total: number; // total

  @Column({ type: 'float', nullable: true })
  subTotal: number; // subTotal

  @Column({ type: 'float', nullable: true })
  tax: number; // tax

  @OneToMany(() => SubscriptionProductEntity, product => product.subscription, {
    cascade: ["remove"],
  })
  products: SubscriptionProductEntity[]; // products

  @OneToMany(() => PaymentsEntity, payment => payment.subscription, {
    cascade: ["remove"],
  })
  payments: PaymentsEntity[];

  @Column({ type: 'timestamp', nullable: true })
  startDate: Date; // startDate

  @Column({ type: 'timestamp', nullable: true })
  endDate: Date; // endDate

  @Column({ type: 'enum', enum: RegionsEnum })
  region: RegionsType; // region

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  updatedAt: Date;

  @Column({ type: 'enum', enum: CFDI_KEYS_ENUM, default: CFDI_KEYS_ENUM.G03 })
  use_cfdi: CFDI_KEYS_TYPE;

  @Column({ type: 'text', default: CFDI_DATA.G03.description })
  use_cfdi_description: string;

  @Column({ type: 'text', default: '03' })
  payment_form: PaymentFormType;

  @BeforeInsert()
  generateId() {
    const id = generateRandomId(10);
    this.id = 'sub-' + id;
    this.setCfdiDescription();
  }

  setCfdiDescription() {
    const use_cfdi = this.use_cfdi ? this.use_cfdi : CFDI_KEYS_ENUM.G03;
    this.use_cfdi_description = CFDI_DATA[use_cfdi].description;
  }

  @BeforeUpdate()
  updateTimestamp() {
    this.updatedAt = new Date();

    const current_desc = CFDI_DATA[this.use_cfdi].description;

    if (this.use_cfdi_description !== current_desc) {
      this.setCfdiDescription();
    }

  }

}
