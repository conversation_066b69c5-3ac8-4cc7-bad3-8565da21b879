
/**
 * Generate a random ID with the specified length.
 * @param length The length of the random ID. Default is 8.
 * @returns A random ID.
 */
export function generateRandomId(length = 8) {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let randomId = '';
  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * characters.length);
    randomId += characters[randomIndex];
  }
  return randomId;
}


/**
  * Generate an API key with the specified length and prefix.
  * @param length The length of the API key. Default is 56.
  * @param prefix The prefix of the API key. Default is 'sk-'.
  * @returns An API key.
*/
export function generateApiKey(length?: number, prefix: string = 'sk-') {

  length = length || 56;

  const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let api<PERSON><PERSON> = prefix;
  for (let i = 0; i < length - prefix.length; i++) {
    apiKey += charset.charAt(Math.floor(Math.random() * charset.length));
  }
  return apiKey;
}