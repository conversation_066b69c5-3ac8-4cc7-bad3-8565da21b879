import { generateRandomId } from '@/lib/randomId';
import { BeforeInsert, Column, Entity, PrimaryColumn } from 'typeorm';

@Entity({ name: 'key' })
export class KeyEntity {

  @PrimaryColumn()
  id: string;

  @Column({ type: 'text' })
  key: string;

  @Column({ type: 'text' })
  userId: string;

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @Column({ type: 'timestamp', nullable: true, onUpdate: 'CURRENT_TIMESTAMP', default: () => 'CURRENT_TIMESTAMP' })
  updatedAt: Date;

  @BeforeInsert()
  async generateId() {
    this.id = generateRandomId(12);

  }

}