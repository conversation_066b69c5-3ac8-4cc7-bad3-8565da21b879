import { HttpException, Inject, Injectable } from "@nestjs/common";
import { Db, ObjectId } from "mongodb";

@Injectable()
export class StockVehicleService {
  constructor(
    @Inject('MONGODB_CONNECTION')
    private db: Db,
  ) { }
  async getStockVehicle(filter) {
    const stockVehicle = await this.db.collection('stockvehicles').findOne(filter);
    if (!stockVehicle) throw new HttpException('Stock vehicle not found', 404);
    const associate = await this.db.collection('associates').findOne({ _id: new ObjectId(stockVehicle.drivers[stockVehicle.drivers.length - 1]._id) });
    return { data: stockVehicle, associate, clientId: associate.clientId };
  }

  async getStockVehicleByAssociateId(associateId: string) {
    try {
      // Find the associate by clientId
      const associate = await this.db
        .collection('associates')
        .findOne({ _id: new ObjectId(associateId) });

      if (!associate) {
        return {
          message: `Associate ${associateId} not found`,
          status: false,
        };
      }

      // Find the stock vehicle where this associate is the last driver in the drivers array
      const stockVehicle = await this.db.collection('stockvehicles').findOne({
        'drivers._id': new ObjectId(associate._id), // Match associate by _id
        drivers: { $elemMatch: { _id: new ObjectId(associate._id) } }, // Ensure it's in the driver list
      });

      if (!stockVehicle) {
        return {
          message: `Stock vehicle not found or the associate ${associateId} is not the current driver`,
          status: false,
        };
      }

      // Get the last driver and ensure it's the associate we just found
      const lastDriver = stockVehicle.drivers[stockVehicle.drivers.length - 1];

      if (lastDriver._id.toString() !== associate._id.toString()) {
        return {
          message: `Associate ${associateId} is not the current driver`,
          status: false,
        };
      }

      // Return the stock vehicle and the associated driver (associate)
      return {
        message: `Associate ${associateId} is not the current driver`,
        status: true,
        data: stockVehicle,
        associate,
      };
    } catch (error: any) {
      console.log('getStockVehicleByAssociateId', error);
      return {
        message: `Associate ${associateId} error`,
        status: false,
      };
    }
  }
}