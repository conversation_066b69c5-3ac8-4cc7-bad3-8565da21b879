import { TaxSystem } from '@/sat/tax_system.types';
import { CustomAxiosInstance } from './Wrapper';

export interface Customer {
  legal_name: string;
  tax_id: string;
  tax_system: TaxSystem
  address: {
    zip: string;
    street?: string;
    interior?: string;
    exterior?: string;
    neighborhood?: string;
    city?: string;
    state?: string;
    country?: string;
    municipality?: string;
  };
  email?: string | undefined;
  phone?: string | undefined;
}

export interface IsValid {
  is_valid: boolean;
  errors: Record<string, any>[];
}

export class Customers {

  private readonly client: CustomAxiosInstance;

  constructor(client: CustomAxiosInstance) {
    this.client = client;
  }

  /**
 * Creates a new customer in your organization
 * @param {String} data.legal_name - required
 * @param {String} data.tax_id - required
 * @param {String} data.tax_system - required
 * @param {String} data.address.zip - required
 * @param {String} data.address.street - not required
 * @param {String} data.address.interior - not required
 * @param {String} data.address.exterior - not required
 * @param {String} data.address.neighborhood - not required
 * @param {String} data.address.city - not required
 * @param {String} data.address.state - not required
 * @param {String} data.address.country - not required
 * @param {String} data.address.municipality - not required
 * @param {String} data.email - not required
 * @param {String} data.phone - not required
 */
  async create(data: Customer) {
    const response = await this.client.post('/customers', data);
    return response;
  }

  /**
   * Retrieves a customer by its ID
   * @param {String} id
   */


  async retrieve(id: string): Promise<Customer> {
    return await this.client.get(`/customers/${id}`);
  }

  /**
   * Retrieves a list of customers
   * @param {Object} params
   */
  async list(params: any) {
    return await this.client.get('/customers', { params });
  }

  /**
   * Updates a customer by its ID
   * @param {String} id
   * @param {Object} data
   */
  async update(id: string, data: any) {
    return await this.client.put(`/customers/${id}`, data);
  }

  /**
   * Deletes a customer by its ID
   * @param {String} id
   */
  async delete(id: string) {
    return await this.client.delete(`/customers/${id}`);
  }


  /**
   * Validate customer with SAT validation.
   * @param {string} id
   */
  async validateTaxInfo(id: string): Promise<IsValid> {
    return await this.client.get('/customers/' + id + '/tax-info-validation');
  }

}