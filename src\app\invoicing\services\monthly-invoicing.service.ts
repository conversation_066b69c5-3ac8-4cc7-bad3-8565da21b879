import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, IsNull, Not } from 'typeorm';
import { Cron, CronExpression } from '@nestjs/schedule';
import { PaymentsEntity } from '@/app/payments/entities/payment.entity';
import { ClientEntity } from '@/app/clients/entities/client.entity';
import { getFirstDayOfCurrentMonth, getLocalDate, getTimezoneDate } from '@/lib/dates';
import { InvoiceQueueService } from '@/app/queue/invoice-queue/invoice-queue.service';
import { NODE_ENV } from '@/constants';

@Injectable()
export class MonthlyInvoicingService {
  private readonly logger = new Logger(MonthlyInvoicingService.name);

  constructor(
    @InjectRepository(PaymentsEntity)
    private readonly paymentsRepository: Repository<PaymentsEntity>,
    @InjectRepository(ClientEntity)
    private readonly clientRepository: Repository<ClientEntity>,
    private readonly invoiceQueueService: InvoiceQueueService,
  ) { }

  // Cron job para ejecutarse el último día del mes a las 5pm (hora de Ciudad de México)
  @Cron('0 17 28-31 * *', {
    timeZone: NODE_ENV !== 'production' ? 'Asia/Karachi' : 'America/Mexico_City',
  })
  async handleMonthlyInvoicing() {
    // Verificar si es el último día del mes
    const today = new Date();
    const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

    if (today.getDate() !== lastDayOfMonth.getDate()) {
      this.logger.log('No es el último día del mes, saltando ejecución del cron job');
      return;
    }

    this.logger.log('Iniciando proceso de facturación mensual de recibos');
    await this.processMonthlyInvoicing();
  }

  async processMonthlyInvoicing(isTest?: boolean) {
    try {
      // Obtener el primer día del mes actual
      const firstDayOfMonth = getFirstDayOfCurrentMonth();

      // Obtener el último día del mes (hoy) a las 23:59
      const lastDayOfMonth = getTimezoneDate({
        timezone: 'America/Mexico_City',
        hour: 23,
        minute: 59,
      });

      this.logger.log(`Procesando facturas para recibos desde ${firstDayOfMonth} hasta ${lastDayOfMonth}`);

      // Buscar pagos con recibos sin facturar
      const payments = await this.paymentsRepository.find({
        where: {
          isPaid: true,
          status: 'success',
          receiptId: Not(IsNull()),
          invoiceId: IsNull(),
          createdAt: Between(firstDayOfMonth, lastDayOfMonth),
        },
        relations: ['client'],
      });

      this.logger.log(`Se encontraron ${payments.length} recibos pendientes de facturar`);

      if (isTest) {
        return {
          message: 'Simulación de facturación mensual',
          count: payments.length,
        };
      }

      // Procesar cada pago a través de la cola
      let count = 0;
      for (const payment of payments) {
        await this.invoiceQueueService.processInvoice(payment);
        count++;
      }

      return {
        message: `Se han enviado ${count} recibos a la cola de facturación`,
        count,
      };
    } catch (error: any) {
      this.logger.error('Error al procesar la facturación mensual', error.stack);
      throw error;
    }
  }
}

