import { Controller, Get, Post, Body, Patch, Param, Delete, Res, Query, Headers } from '@nestjs/common';
import { tryCatchResponse } from '@/lib/defaultResponse';
import { Response } from 'express';
import { Wire4WebHookService } from '../services/wire4Webhook.service';
import { PublicRoute } from '@/app/keys/guards/public.decorator';
import { PaymentsLoanTapeService } from '../services/paymentsLoanTapeService';

@Controller('paymentsLoanTape')
export class PaymentsLoanTapeController {
  constructor(private readonly paymentsLoanTapeService: PaymentsLoanTapeService,
    private readonly wire4Webhook: Wire4WebHookService
  ) { }
  
  @PublicRoute()
  @Get('associateId/:associateId')
  async findByAssociateId(@Param('associateId') associateId: string, @Res() res: Response, @Headers('x-origin-url') origin: string) {
    console.log("findByAssociateId");
    return await tryCatchResponse(res, async () => this.paymentsLoanTapeService.findByAssociateId(associateId));
  }
}
