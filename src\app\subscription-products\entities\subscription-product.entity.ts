import { ProductEntity } from '@/app/products/entities/product.entity';
import { SubscriptionEntity } from '@/app/subscriptions/entities/subscription.entity';
import { Column, Entity, ManyToOne } from 'typeorm';

@Entity({ name: 'subscription_products' })
export class SubscriptionProductEntity extends ProductEntity {

  @Column({
    type: 'int',
    default: 1,
  })
  quantity: number; // quantity

  @Column({
    type: 'boolean',
    default: false,
  })
  isTemporal: boolean; // isTemporal

  @Column({
    type: 'float',
    default: 0,
  })
  discount: number; // discount

  @Column({
    type: 'float',
  })
  total: number; // total

  @ManyToOne(() => SubscriptionEntity, suscription => suscription.products, {
    onDelete: 'CASCADE',
  })
  subscription: SubscriptionEntity; // suscriptionId

}

