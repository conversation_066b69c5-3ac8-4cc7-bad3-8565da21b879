import { IsOptional, IsString, ValidateIf } from "class-validator";


type StringOrNull = string | null;

export class FindOneSubscriptionQueryDto {
  @IsOptional()
  @IsString()
  // @ValidateIf(o => o.clientId !== null)
  readonly clientId?: string | null;

  @IsOptional()
  @IsString()
  // @ValidateIf(o => o.region !== null)
  readonly region?: string | null | undefined;

  @IsOptional()
  @IsString()
  // @ValidateIf(o => o.subProductId !== null)
  readonly subProductId: string | null;

}