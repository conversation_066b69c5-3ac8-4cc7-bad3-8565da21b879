import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, OneToMany, CreateDateColumn } from 'typeorm';
import { WeeklyRecord } from './weekly-record.entity';

@Entity({ name: 'weekly_tracking' })
export class WeeklyTracking {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: "int" })
  year: number;

  @Column({ type: "int" })
  weekNumber: number;

  @CreateDateColumn({ type: "timestamp" })
  executionDate: Date;

  @Column({ default: 0, type: "int" })
  totalCustomers: number;

  @Column({ default: 0, type: "int" })
  successCount: number;

  @Column({ default: 0, type: "int" })
  failureCount: number;

  @Column({ default: 'pending', type: "text" })
  status: string;

  @OneToMany(() => WeeklyRecord, record => record.tracking)
  records: WeeklyRecord[];

  @Column({type: "timestamp", nullable: true })
  startDateTime: Date;

  @Column({type: "timestamp", nullable: true })
  endDateTime: Date;
}
