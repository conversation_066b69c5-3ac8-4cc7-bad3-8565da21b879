import { UnlockPaymentDto, UnlockPaymentPlateDto, Validate2LastPaymentsDto, SoldPaymentDto } from "./../dto/create-payment.dto";
import { HttpException, Injectable, Logger } from '@nestjs/common';
import { CreateIsolatedPayment, CreatePaymentDto } from '../dto/create-payment.dto';
import { UpdatePaymentDto } from '../dto/update-payment.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { PaymentProduct, PaymentStatusEnum, PaymentsEntity } from '../entities/payment.entity';
import { Between, FindOptionsSelect, MoreThanOrEqual, Repository, MoreThan} from 'typeorm';
import { FindQueryPaymentDto, PeriodicityEnum, PeriodicityType } from '../dto/find-query-payment.dto';
import { getLastDay, getLastMonth, getLastThursday, getLastYear, getLocalDate } from '@/lib/dates';
import { ProductEntity, TaxFactorType, TaxTypesType } from '@/app/products/entities/product.entity';
import { ClientsService } from '@/app/clients/services/clients.service';
import { sendPaymentWAMessage, sendUnlockPaymentWAMessage, sendSoldPaymentWAMessage } from '@/hilos-whatsapp/payment-message';
import paymentLinkEmail, { soldPaymentEmail } from '@/providers/nodemailer/templates/paymentLink.template';
import { TaxabilityType } from '@/facturapi/Products';
import facturapi from '@/facturapi';
import { CFDI_KEYS_TYPE, G03_LIST } from '@/sat/cfdi.types';
import { ItemProps, PaymentFormType } from '@/facturapi/Invoices';
import { CountriesShortNames } from "@/app/clients/dto/create-client.dto";
import { StockVehicleService } from './stock.service';
import { UNLOCK_PAYMENT_TEMPLATE_ID, PAYMENT_FRONT_URL } from "@/constants";
import { NotificationService } from "@/app/notification/services/notification.service"

@Injectable()
export class PaymentsService {
  private readonly logger = new Logger(PaymentsService.name);
  constructor(
    @InjectRepository(PaymentsEntity) private readonly paymentRepository: Repository<PaymentsEntity>,
    @InjectRepository(ProductEntity) private readonly productRepository: Repository<ProductEntity>,
    private readonly clientService: ClientsService,
    private readonly stockVehicleService: StockVehicleService,
    private readonly notificationService: NotificationService,
  ) { }

  async create(createPaymentDto: CreatePaymentDto) {

    // const messageLogger = `PAYMENT TO CREATE: ${JSON.stringify(createPaymentDto)}`;
    // this.logger.log(messageLogger);
    const newPaymentInstance = this.paymentRepository.create(createPaymentDto);

    const newPayment = await this.paymentRepository.save(newPaymentInstance);
    return {
      message: 'Payment created successfully',
      data: newPayment,
    };

  }

  /**
    * Create a payment without a subscription
    * @param data
   */
  async createIsolatedPayment(data: CreateIsolatedPayment) {

    const { clientId, products, payment_form } = data;

    const { data: client } = await this.clientService.findOne(clientId);

    // const { data: product } = await this.productsService.findOne(productId);

    const paymentProducts: PaymentProduct[] = [];
    let totalPaymentAmount = 0;
    let subTotalPaymentAmount = 0;
    let taxPaymentAmountr = 0;

    for (const p of products) {
      // const { data: product } = await this.productsService.findOne(p.id);

      const product = await this.productRepository.findOne({
        where: {
          id: p.id,
        },
      });

      const total = product.price * p.quantity;
      const taxRate = product.taxRate;
      const subTotal = total * (1 - (taxRate === 1 ? 0 : taxRate));
      const tax = total - subTotal;

      totalPaymentAmount += total;
      subTotalPaymentAmount += subTotal;
      taxPaymentAmountr += tax;

      const newProduct = {
        id: product.id,
        name: product.name,
        price: product.price,
        total: total,
        subTotal,
        quantity: p.quantity,
        tax,
        taxRate,
        taxFactor: product.taxFactor,
        taxType: product.taxType,
        product_key: product.product_key,
        unit_key: product.unit_key,
        measurementUnit: product.measurementUnit,
      }

      paymentProducts.push(newProduct);
    }

    const newPaymentInstance = this.paymentRepository.create({
      total: totalPaymentAmount,
      subTotal: subTotalPaymentAmount,
      tax: taxPaymentAmountr,
      client,
      products: paymentProducts,
      payment_form,
      type: 'isolated',
    });

    const newPayment = await this.paymentRepository.save(newPaymentInstance);
    await sendPaymentWAMessage(
      client.name,
      newPayment.id,
      client.phone,
      newPayment.total,
    ); // adding total amount to sendPaymentWAMessage

    await paymentLinkEmail({
      email: client.email,
      paymentId: newPayment.id,
      name: client.name,
      amount: newPayment.total,
    });

    this.logger.log(
      `[createIsolatedPayment] - sending push notification to client ${client.name}`,
    );
    await this.notificationService.sendPush(
      client,
      `!Hola ${client.name}`,
      'Adjuntamos el enlace de pago correspondiente a el pago de la renta semanal de la siguiente semana.\nRecuerda que debes realizado el día lunes antes de las 5pm.\nSaludos!',
      {
        type: JSON.stringify({
          operation: 'payment',
          action: 'paymentCreated',
        }),
        link: 'https://pagos.onecarnow.com/pago/' + newPayment.id,
      },
    );
    this.logger.log(
      `[createIsolatedPayment] - notification sent to client ${client.name}`,
    );

    return {
      message: 'Payment created successfully',
      data: newPayment,
    };

  }

  async findAll(query?: FindQueryPaymentDto, internal = false) {

    const { total, status, clientId, email, startDate, periodicity, page = 1, limit, region, totalMoreThan, includeFines, country } = query;
    const where = {};

    if (country) {
      where['client'] = { country: CountriesShortNames[country] }
    }
    if (total) where['total'] = total;
    if (status) where['status'] = status;
    if (clientId) where['client.id'] = clientId;
    if (email) where['client'] = { email };
    if (region) where['subscription'] = { region };
    if (totalMoreThan) where['total'] = MoreThanOrEqual(totalMoreThan);
    if (startDate) {
      const initialDate = getLocalDate(new Date(startDate));
      let endDate = query.endDate as unknown as Date;
      if (endDate) where['createdAt'] = Between(initialDate, endDate);
      else where['createdAt'] = MoreThan(initialDate);

    };
    if (periodicity /* && periodicity !== PeriodicityEnum.FROM_START */) {
      const { initialDate, endDate } = this.getByPeriodicity(periodicity, where);
      where['createdAt'] = Between(initialDate, endDate);
    }
    if(includeFines !== undefined){
      if(!includeFines){
        //this is a temporary hack to not return payments of 100 or less which will be considered as fine.
        //proper solution would be to maintain a column in database of isFine to check if this payment is done on behalf of a fine
        where['total'] = MoreThan(1000)
      }
    }

    const skip = ((page - 1) * limit) || undefined;
    const take = limit || undefined;

    const internalSelect: FindOptionsSelect<PaymentsEntity> = internal ? {} : {
      id: true,
      total: true,
      type: true,
      subTotal: true,
      tax: true,
      status: true,
      isPaid: true,
      invoiceId: true,
      receiptId: true,
      // client: {
      //   id: true,
      //   name: true,
      //   email: true,
      //   monexClabe: true,
      //   contractNumber: true,
      // },
      client: {
        id: true,
        contractNumber: true,
        name: true,
        lastName: true,
        legal_name: true,
        phone: true,
        email: true,
        monexClabe: true,
        rfc: true,
        street: true,
        country: true,
        tax_system: true,
        zip: true,
        isActive: true,
        facturapiId: true,
        region: true,
        associateId: true,
        createdAt: true,
      },
      subscription: {
        id: true,
        total: true,
        isActive: true,
        subTotal: true,
        tax: true,
      },
      paidAt: true,
      createdAt: true,
      updatedAt: true,
      dateLimit: true,
    }

    const payments = await this.paymentRepository.find({
      where,
      relations: ['client', 'subscription.client'],
      select: internalSelect,
      order: {
        createdAt: 'DESC',
      },
      skip,
      take,
    });


    const totalQuery = await this.paymentRepository.count({
      where,
    });

    const allRecords = await this.paymentRepository.count();

    return {
      message: 'Payments fetched successfully',
      allRecords,
      totalQuery,
      total: payments.length,
      data: payments,
    };

  }

  async findOne(id: string, internal = false) {
    const select = internal ? undefined : {
      id: true,
      total: true,
      subTotal: true,
      type: true,
      tax: true,
      paidAt: true,
      createdAt: true,
      updatedAt: true,
      isPaid: true,
      status: true,
      concept: true,
      client: {
        id: true,
        contractNumber: true,
        name: true,
        email: true,
        monexClabe: true,
        metadata: true,
        street: true,
        country:true,
        state:true,
        region:true
      },
      products: true,
      subscription: {
        id: true,
        total: true,
        isActive: true,
        subTotal: true,
        tax: true,
        products: {
          name: true,
          price: true,
          quantity: true,
        }
      }
    }

    const relations = internal ? ['client', 'subscription.products'] : ['client'];

    const payment = await this.paymentRepository.findOne({
      where: {
        id,
      },
      relations,
      select,
    });

    if (!payment) {
      throw new HttpException('Payment not found', 404);
    }

    return {
      message: 'Payment fetched successfully',
      data: payment,
    };

  }

  async update(id: string, updatePaymentDto: UpdatePaymentDto) {

    const payment = await this.paymentRepository.findOne({
      where: {
        id,
      }
    });

    if (!payment) {
      throw new HttpException('Payment not found', 404);
    }

    await this.paymentRepository.update(id, updatePaymentDto);

    return {
      message: 'Payment updated successfully',
    };

  }

  async remove(id: string, completeDelete: boolean = false) {

    const payment = await this.paymentRepository.findOne({
      where: {
        id,
      }
    });

    if (!payment) {
      throw new HttpException('Payment not found', 404);
    }

    if (payment.isPaid || payment.status === PaymentStatusEnum.SUCCESS) {
      throw new HttpException('Payment already paid you cannot delete', 400);
    }

    if (completeDelete) {
      await this.paymentRepository.delete(id);
    } else {
      await this.paymentRepository.softDelete(id);
    }

    // await this.paymentRepository.delete(id);

    return {
      message: 'Payment deleted successfully',
    };

  }

  async removeTemporalProducts(id: string) {

    const payment = await this.paymentRepository.findOne({
      where: {
        id,
      }
    });

    if (!payment) {
      throw new HttpException('Payment not found', 404);
    }

    if (!payment.products || payment.products.length === 0) {
      throw new HttpException('No products found in payment', 404);
    }

    if (payment.isPaid || payment.status === PaymentStatusEnum.SUCCESS) {
      throw new HttpException('Payment already paid you cannot remove product', 400);
    }


    if (payment.status === PaymentStatusEnum.CANCELED) {
      throw new HttpException('Payment already canceled', 400);
    }


    const newProducts = payment.products.filter(p => p.name.toLowerCase() !== 'adendum de contrato');

    let total = 0;
    let subTotal = 0;
    let tax = 0;

    newProducts.forEach(p => {
      total += p.total;
      subTotal += p.subTotal;
      tax += p.tax || total - subTotal;
    });


    await this.paymentRepository.update(id, {
      products: newProducts,
      total,
      subTotal,
      tax,
    });

    return {
      message: 'Temporal product removed successfully',
    };

  }


  getByPeriodicity(periodicity: PeriodicityType, where?: any) {

    let initialDate: Date;
    let endDate = getLocalDate(new Date());

    switch (periodicity) {
      case PeriodicityEnum.LAST_DAY:
        initialDate = getLastDay();
        console.log('LAST DAY', initialDate, endDate)
        break;
      case PeriodicityEnum.LAST_WEEK:
        const day = getLastThursday();
        initialDate = day
        break;
      case PeriodicityEnum.LAST_MONTH:
        const { firstDay, lastDay } = getLastMonth();
        initialDate = firstDay;
        endDate = lastDay;
        break;
      case PeriodicityEnum.LAST_YEAR:
        const { firstDay: firstYearDay, lastDay: lastYearDay } = getLastYear();
        initialDate = firstYearDay;
        endDate = lastYearDay;
        break;
      // case PeriodicityEnum.FROM_START:
      //   break;
      default:
        break;
    }

    return {
      initialDate,
      endDate,
    };

  }

  async cancel(id: string, body: any) {

    const payment = await this.paymentRepository.findOne({
      where: {
        id,
      }
    });

    if (!payment) {
      throw new HttpException('Payment not found', 404);
    }

    if (payment.status === PaymentStatusEnum.CANCELED) {
      throw new HttpException('Payment already canceled', 400);
    }

    let obj: any = {
      status: PaymentStatusEnum.CANCELED,
    }

    if (body.reason) {
      // obj['reason'] = body.reason;
      obj.metdata = { reason: body.reason };
    }

    await this.paymentRepository.update(id, {
      status: PaymentStatusEnum.CANCELED,
    });

    return {
      message: 'Payment canceled successfully',
    };

  }

  async findByAssociateId(associateId:string, internal = false){
    const client = await this.clientService.findByAssociateId(associateId);
    if(!client.data){
      throw new HttpException('Client not found', 404);
    }
    const select = internal ? undefined : {
      id: true,
      total: true,
      subTotal: true,
      tax: true,
      paidAt: true,
      createdAt: true,
      updatedAt: true,
      isPaid: true,
      status: true,
      concept: true,
      client: {
        id: true,
        contractNumber: true,
        name: true,
        email: true,
        monexClabe: true,
      },
      products: true,
      subscription: {
        id: true,
        total: true,
        isActive: true,
        subTotal: true,
        tax: true,
        products: {
          name: true,
          price: true,
          quantity: true,
        }
      }
    }

    const relations = internal ? ['client', 'subscription.products'] : ['client'];
    const where = {};
    where['client.id'] = client.data.id;
    const take = 5;

    const payment = await this.paymentRepository.find({
      where,
      relations,
      select,
      order: {
        createdAt: 'DESC',
      },
      take,
    });

    if (!payment || payment.length === 0) {
      throw new HttpException('Payment not found', 404);
    }

    return {
      message: 'Payment fetched successfully',
      data: payment,
    };
  }

  async unlockPayment(unlockPayment: UnlockPaymentDto) {

    const { clientId, total: price, taxRate, quantity } = unlockPayment;

    const client = await this.clientService.findOne(clientId);

    const lastSubscriptionPayment = await this.paymentRepository.findOne({
      where: {
        client: { id: clientId },
        type: 'subscription',
      },
      order: {
        createdAt: 'DESC',
      },
    });

    const isSubcriptionPaymentToIsoPayment = await this.paymentRepository.findOne({
      where: {
        originPaymentId: lastSubscriptionPayment.id,
      },
      order: {
        createdAt: 'DESC',
      },
    });

    if (isSubcriptionPaymentToIsoPayment) {
      throw new HttpException(
        `This subscription payment already has an assigned isolated payment. Isolated payment id: ${isSubcriptionPaymentToIsoPayment.id} cannot be the same as the originPaymentId: ${lastSubscriptionPayment.id} of the subscription payment.`,
        400
      );
    }

    if (!lastSubscriptionPayment) {
      throw new HttpException('Subscription payment not found', 400);
    }

    if (!lastSubscriptionPayment.isPaid && lastSubscriptionPayment.dateLimit > new Date()) {
      throw new HttpException('Payment is pending and the due date is still valid', 400);
    }

    if (lastSubscriptionPayment.isPaid && lastSubscriptionPayment.dateLimit > lastSubscriptionPayment.paidAt) {
      throw new HttpException('Last subscription payment already paid', 400);
    }



    const total = price * quantity;

    const newPayment = this.paymentRepository.create({
      total,
      client: client.data,
      originPaymentId: lastSubscriptionPayment.id,
      type: 'isolated',
    });

    const is1 = taxRate === 1;

    const tax = is1 ? 0 : parseFloat((total * (taxRate)).toFixed(2));

    const subTotal = total - tax;

    const taxProduct = is1 ? 0 : parseFloat((price * taxRate).toFixed(2));
    const subTotalProduct = price - taxProduct;

    const products = [
      {
        id: "1",
        tax: taxProduct,
        name: "Cargo por reactivación",
        total,
        price,
        taxRate,
        "taxType": "IVA" as TaxTypesType,
        quantity,
        subTotal: subTotalProduct,
        "unit_key": "E48",
        "taxFactor": "Tasa" as TaxFactorType,
        "product_key": "32101656",
        "measurementUnit": "Unidad de servicio"
      }
    ]

    newPayment.products = products;
    newPayment.subTotal = subTotal;
    newPayment.tax = tax;
    newPayment.originPaymentId = lastSubscriptionPayment.id;

    const savedPayment = await this.paymentRepository.save(newPayment);

    await sendPaymentWAMessage(client.data.name, savedPayment.id, client.data.phone, total);
    await paymentLinkEmail({ email: client.data.email, paymentId: savedPayment.id, name: client.data.name, amount: savedPayment.total })

    return {
      message: 'Payment created successfully',
      data: {
        id: savedPayment.id,
        total: savedPayment.total,
        status: savedPayment.status,
        client: {
          email: client.data.email,
          id: client.data.id,
          legal_name: client.data.legal_name,
          associateId: client.data.associateId,
        },
      },
    };
  }

  async soldPayment(soldPayment: SoldPaymentDto) {

    const { clientId, total: price, clabe, taxRate } = soldPayment;

    const client = await this.clientService.findOne(clientId);

    if (!client) throw new HttpException('Client not found', 404);

    await this.clientService.update(clientId, { monexClabe: clabe });

    const total = price;

    const newPayment = this.paymentRepository.create({
      total,
      client: client.data,
      type: 'sold',
    });

    const is1 = taxRate === 1;

    const tax = is1 ? 0 : parseFloat((price * (taxRate)).toFixed(2));

    const subTotal = price - tax;

    const taxProduct = is1 ? 0 : parseFloat((price * taxRate).toFixed(2));
    const subTotalProduct = price - taxProduct;

    const products = [
      {
        id: "1",
        tax: taxProduct,
        name: "Pago por compra de auto",
        total,
        price,
        taxRate,
        "taxType": "IVA" as TaxTypesType,
        quantity: 1,
        subTotal: subTotalProduct,
        "unit_key": "E48",
        "taxFactor": "Tasa" as TaxFactorType,
        "product_key": "32101656",
        "measurementUnit": "Unidad de servicio"
      }
    ]

    newPayment.products = products;
    newPayment.subTotal = subTotal;
    newPayment.tax = tax;

    const savedPayment = await this.paymentRepository.save(newPayment);

    await sendSoldPaymentWAMessage(savedPayment.id, client.data.phone);
    const url = `${PAYMENT_FRONT_URL}/pago/${savedPayment.id}`;
    await soldPaymentEmail({ email: client.data.email, paymentId: savedPayment.id, name: client.data.name, url })

    return {
      message: 'Payment created successfully',
      data: {
        id: savedPayment.id,
        total: savedPayment.total,
        status: savedPayment.status,
        type: savedPayment.type,
        client: {
          email: client.data.email,
          id: client.data.id,
          legal_name: client.data.legal_name,
          associateId: client.data.associateId,
        },
      },
    };

  }

  async unlockPaymentByPlates(unlockPayment: UnlockPaymentPlateDto) {

    const { plates } = unlockPayment;
    const { clientId } = await this.stockVehicleService.getStockVehicle({ 'carPlates.plates': plates });

    return await this.createUnlockPaymentByClient(clientId,unlockPayment);
  }

  async createUnlockPaymentByClient(clientId : string, unlockPayment: UnlockPaymentPlateDto ){
    const { plates, total: price, taxRate, quantity, isCron } = unlockPayment;
    const client = await this.clientService.findOne(clientId);

    const lastSubscriptionPayment = await this.paymentRepository.findOne({
      where: {
        client: { id: clientId },
        type: 'subscription',
      },
      order: {
        createdAt: 'DESC',
      },
    });

    if(!lastSubscriptionPayment)
      throw new HttpException(`Last subscription payment for client ${clientId} not found`, 404);

    const isSubcriptionPaymentToIsoPayment = await this.paymentRepository.findOne({
      where: {
        originPaymentId: lastSubscriptionPayment.id,
      },
      order: {
        createdAt: 'DESC',
      },
    });

    if (isSubcriptionPaymentToIsoPayment) {
      return {
        message: `Unlock Payment ${isSubcriptionPaymentToIsoPayment.id} already created.`,
        data: {
          link: `https://pagos.onecarnow.com/pago/${isSubcriptionPaymentToIsoPayment.id}`,
            status: isSubcriptionPaymentToIsoPayment.status,
        },
      }
    }

    if (!lastSubscriptionPayment) {
      throw new HttpException('Subscription payment not found', 200);
    }

    if (!lastSubscriptionPayment.isPaid && lastSubscriptionPayment.dateLimit > new Date()) {

      return { message: `Go to the endpoint to review-unlook-payment and activate the car. Payment ${lastSubscriptionPayment.id} limit ${lastSubscriptionPayment.dateLimit} remaining.` };
    }

    if (lastSubscriptionPayment.isPaid && lastSubscriptionPayment.dateLimit > lastSubscriptionPayment.paidAt) {
      return { message: `Go to the endpoint to review-unlook-payment and activate the car. Paid before date limit.` };
    }


    const total = price * quantity;

    const newPayment = this.paymentRepository.create({
      total,
      client: client.data,
      originPaymentId: lastSubscriptionPayment.id,
      type: 'isolated',
    });

    const is1 = taxRate === 1;

    const tax = is1 ? 0 : parseFloat((total * (taxRate)).toFixed(2));

    const subTotal = total - tax;

    const taxProduct = is1 ? 0 : parseFloat((price * taxRate).toFixed(2));
    const subTotalProduct = price - taxProduct;

    const products = [
      {
        id: "1",
        tax: taxProduct,
        name: "Cargo por reactivación",
        total,
        price,
        taxRate,
        "taxType": "IVA" as TaxTypesType,
        quantity,
        subTotal: subTotalProduct,
        "unit_key": "E48",
        "taxFactor": "Tasa" as TaxFactorType,
        "product_key": "32101656",
        "measurementUnit": "Unidad de servicio"
      }
    ]

    newPayment.products = products;
    newPayment.subTotal = subTotal;
    newPayment.tax = tax;
    newPayment.originPaymentId = lastSubscriptionPayment.id;
    if(isCron)
      newPayment.createdFrom = 'cronjob';
    else
      newPayment.createdFrom = 'open CX API';

    const savedPayment = await this.paymentRepository.save(newPayment);

    if(!isCron){
      await sendPaymentWAMessage(client.data.name, savedPayment.id, client.data.phone, total);
      await paymentLinkEmail({ email: client.data.email, paymentId: savedPayment.id, name: client.data.name, amount: savedPayment.total });
    }
    else{
      await sendUnlockPaymentWAMessage(client.data.name, client.data.phone, lastSubscriptionPayment.id,savedPayment.id,UNLOCK_PAYMENT_TEMPLATE_ID);
    }

    return {
      message: 'Payment created successfully',
      data: {
        link: `https://pagos.onecarnow.com/pago/${savedPayment.id}`,
        subLink : `https://pagos.onecarnow.com/pago/${lastSubscriptionPayment.id}`,
        paymentId: savedPayment.id,
        subPaymentId: lastSubscriptionPayment.id
      }
    };
  }

  async validate2LastPayments(validate: Validate2LastPaymentsDto) {

    // const { clientId } = unlockPayment;

    // const client = await this.clientService.findOne(clientId);
    console.log('body', validate);
    const select = {
      id: true,
      total: true,
      status: true,
      isPaid: true,
      client: {
        email: true,
        id: true,
        legal_name: true,
      },
      createdAt: true,
    }

    const reactivationPayment = await this.paymentRepository.findOne({
      where: {
        id: validate.reactivationPaymentId,
      },
      relations: ['client'],
      select,
    });

    if (!reactivationPayment) {
      throw new HttpException('Reactivation payment not found', 404);
    }

    const weeklyPayment = await this.paymentRepository.findOne({
      where: {
        id: validate.paymentId,
      },
      relations: ['client'],
      select,
    });

    if (!weeklyPayment) {
      throw new HttpException('Payment not found', 404);
    }


    // if (weeklyPayment.status === 'success' && reactivationPayment.status === 'success') {
    if(weeklyPayment.isPaid && reactivationPayment.isPaid){
      return {
        message: 'Validation success',
        is_valid: true,
        isPaid: "true",
        weeklyPayment: {
          total: weeklyPayment.total,
          clientEmail: weeklyPayment.client.email,

          status: weeklyPayment.status,
          createdAt: weeklyPayment.createdAt,
        },
        // reactivationPayment,
        reactivationPayment: {
          total: reactivationPayment.total,
          clientEmail: reactivationPayment.client.email,
          status: reactivationPayment.status,
          createdAt: reactivationPayment.createdAt,
        },
      }
    }

    return {
      message: 'Validation failed',
      is_valid: false,
      isPaid: "false",
    };

  }

  async retryInvoice(id: string) {

    const payment = await this.paymentRepository.findOneOrFail({
      where: {
        id,
      },
      relations: ['client'],
      select: {
        id: true,
        total: true,
        status: true,
        isPaid: true,
        invoiceId: true,
        receiptId: true,
        products: true,
        client: {
          email: true,
          id: true,
          legal_name: true,
          facturapiId: true,
          is_valid_tax_info: true,
          tax_system: true,
          region: true,
        }
      }
    });


    if (!payment.client.is_valid_tax_info) {
      throw new HttpException('El cliente no tiene información fiscal válida', 400);
    }

    if (!payment.client.facturapiId) {
      throw new HttpException('El cliente no tiene facturapiId', 400);
    }

    if (payment.status === PaymentStatusEnum.CANCELED) {
      throw new HttpException('Este pago ha sido cancelado', 400);
    }

    if (payment.status === PaymentStatusEnum.PENDING || !payment.isPaid) {
      throw new HttpException('Este pago no ha sido procesado', 400);
    }


    if (payment.status === PaymentStatusEnum.SUCCESS && payment.isPaid && !payment.invoiceId) {

      const invoice = await this.invoiceReceipt(payment);

      if (!invoice) {
        throw new HttpException('Error al facturar el recibo', 400);
      }

      await this.paymentRepository.update(payment.id, {
        invoiceId: invoice.id,
      });

      return {
        message: 'Invoice retried',
        invoiceId: invoice.id,
      }
    }

    throw new HttpException('Factura ya creada', 400);
  }

  async createInvoiceAndSend(payment: PaymentsEntity, facturapiId: string, items: ItemProps[]) {
    const payment_form = (payment.subscription?.payment_form || payment.payment_form || '03') as PaymentFormType;

    const regimen = (payment.client.tax_system || '625');

    let use: CFDI_KEYS_TYPE = 'G03';

    if (!G03_LIST.includes(regimen)) {
      console.log('REGIMEN NO VALIDO', regimen, 'changing to S01');
      use = "S01";
    }
    console.log('USO DE CFDI', use, 'REGIMEN', regimen);
    try {
      // console.log('items', items);
      const createInvoice = await facturapi.invoices.create({
        customer: facturapiId,
        payment_form,
        use,
        items,
        series: payment.client.region,
      });
      console.log('FACTURA CREADA', createInvoice.id);

      try {
        const emailSent = await facturapi.invoices.sendInvoiceByEmail(createInvoice.id, { email: payment.client.email });
        console.log('EMAIL ENVIADO', emailSent);

      } catch (error) {
        console.log('ERROR AL ENVIAR EMAIL', error);
      }
      // await this.updatePayment(payment, createInvoice.id);
      await this.paymentRepository.update(payment.id, {
        invoiceId: createInvoice.id,
      });
      console.log('PAGO ACTUALIZADO', payment.id);

    } catch (error: any) {
      console.log('ERROR AL CREAR FACTURA', error);
      throw new HttpException(error.message || 'Error al crear factura', 400);
    }
  }

  async invoiceReceipt(payment: PaymentsEntity) {

    try {

      const receipt = await facturapi.receipts.retrieve(payment.receiptId);

      console.log('receipt', receipt.status);

      if (receipt.status === 'invoiced_to_customer') {
        console.log('receipt already invoiced')
        // await facturapi.receipts.cancel(payment.receiptId);
        return {
          id: receipt.invoice,
        }
      }

      // try to invoice with client's facturapiId
      const invoice = await facturapi.receipts.invoice(payment.receiptId, {
        customer: payment.client.facturapiId,
        use: 'S01',
        series: payment.client.region,
      });

      return invoice;

    } catch (error: any) {
      console.log('ERROR AL FACTURAR EL RECIBO', error);
      throw new HttpException(error.message || 'Error al facturar el recibo', 400);
    }

  }
  createItemsForPayment(payment: PaymentsEntity) {

    // Fix items creation for payments
    if (!payment.products[0].product_key || !payment.products[0].unit_key || !payment.products[0].taxType) {
      return this.createItemsOnSubscription(payment);
    }
    else {
      const items = payment.products.map(product => {
        return {
          product: {
            description: product.name,
            product_key: product.product_key,
            price: product.price ? product.price : product.total / product.quantity,
            tax_included: true,
            taxability: '02' as TaxabilityType,
            taxes: [{
              rate: product.taxRate,
              factor: product.taxFactor,
              type: product.taxType,
            }],
            unit_key: product.unit_key,
            unit_name: product.measurementUnit,
            sku: product.id,
          },
          quantity: product.quantity,
        }
      })
      return items;
    }

  }
  createItemsForNonSubscription(payment: PaymentsEntity) {

    const items = payment.products.map(product => {
      return {
        product: {
          description: product.name,
          product_key: product.product_key,
          price: product.total,
          tax_included: true,
          taxability: '02' as TaxabilityType,
          taxes: [{
            rate: product.taxRate,
            factor: product.taxFactor,
            type: product.taxType,
          }],
          unit_key: product.unit_key,
          unit_name: product.measurementUnit,
          sku: product.id,
        },
        quantity: product.quantity,
      }
    })
    return items;

  }

  createItemsOnSubscription(payment: PaymentsEntity) {
    const items = payment.subscription.products.map(product => {
      return {
        product: {
          description: product.name,
          product_key: product.product_key,
          price: product.price,
          tax_included: true,
          taxability: '02' as TaxabilityType,
          taxes: [{
            rate: product.taxRate,
            factor: product.taxFactor,
            type: product.taxType,
          }],
          unit_key: product.unit_key,
          unit_name: product.measurementUnit,
          sku: product.id,
        },
        quantity: product.quantity,
      }
    })
    return items;
  }

}
