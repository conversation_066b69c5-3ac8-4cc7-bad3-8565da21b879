import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne } from 'typeorm';
import { PaymentsEntity } from './payment.entity';

export enum PaymentTransactionStatusEnum {
  success = 'success',
  failed = 'failed',
}

export type PaymentTransactionStatusType = keyof typeof PaymentTransactionStatusEnum;

@Entity({ name: 'payment_transactions' })
export class PaymentTransactionsEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => PaymentsEntity, payment => payment.id)
  payment: PaymentsEntity;

  @Column({ type: 'jsonb' })
  monexData: Record<string, any>;

  @Column({ type: 'text' })
  description: string;

  @Column({ type: 'float' })
  amount: number;

  @Column({ type: 'enum', enum: PaymentTransactionStatusEnum })
  status: PaymentTransactionStatusType;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP', onUpdate: 'CURRENT_TIMESTAMP' })
  updatedAt: Date;


}
