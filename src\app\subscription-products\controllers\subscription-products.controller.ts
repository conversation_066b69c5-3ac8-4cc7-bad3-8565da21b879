import { Controller, Get, Post, Body, Patch, Param, Delete, Res, Query } from '@nestjs/common';
import { SubscriptionProductsService } from '../services/subscription-products.service';
import { CreateSusbcriptionProductDto } from '../dto/create-subscription-product.dto';
import { UpdateSubscriptionProductDto } from '../dto/update-subscription-product.dto';
import { ApiTags } from '@nestjs/swagger';
import { tryCatchResponse } from '@/lib/defaultResponse';
import { Response } from 'express';
import { FindAllSubscriptionProductsQueryDto } from '../dto/find-suscription-products.dto';

@ApiTags('subscription-products')
@Controller('subscription-products')
export class SubscriptionProductsController {
  constructor(private readonly subscriptionProductsService: SubscriptionProductsService) { }

  @Post()
  async create(@Body() createSuscriptionProductDto: CreateSusbcriptionProductDto, @Res() res: Response) {
    return await tryCatchResponse(res, async () => await this.subscriptionProductsService.create(createSuscriptionProductDto));
  }

  @Get()
  async findAll(@Query() query: FindAllSubscriptionProductsQueryDto, @Res() res: Response) {
    return await tryCatchResponse(res, async () => await this.subscriptionProductsService.findAll(query));
  }

  @Get(':id')
  async findOne(@Param('id') id: string, @Res() res: Response, @Query() query: FindAllSubscriptionProductsQueryDto) {

    return await tryCatchResponse(res, async () => await this.subscriptionProductsService.findOne(id, query));

  }

  @Patch(':id')
  async update(@Param('id') id: string, @Body() updateSuscriptionProductDto: UpdateSubscriptionProductDto) {
    return this.subscriptionProductsService.update(id, updateSuscriptionProductDto);
  }

  @Delete(':id')
  async remove(@Param('id') id: string, @Res() res: Response) {
    return await tryCatchResponse(res, async () => await this.subscriptionProductsService.remove(id));
  }
}
