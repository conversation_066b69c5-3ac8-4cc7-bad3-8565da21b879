import { ClientEntity } from '@/app/clients/entities/client.entity';
import { generateRandomId } from '@/lib/randomId';
import { BeforeInsert, BeforeUpdate, Column, DeleteDateColumn, Entity, JoinColumn, ManyToOne, OneToMany, PrimaryColumn } from 'typeorm';
import { SubscriptionEntity } from '@/app/subscriptions/entities/subscription.entity';
import { addDays } from 'date-fns';
import { getLocalDate } from '@/lib/dates';
import { PaymentFormEnum } from '@/facturapi/enums';
import { CFDI_KEYS_ENUM } from '@/sat/cfdi.types';
import { TaxFactorType, TaxTypesType } from '@/app/products/entities/product.entity';
import { PaymentFormType } from '@/facturapi/Invoices';

export enum PaymentStatusEnum {
  PENDING = 'pending',
  SUCCESS = 'success',
  FAILED = 'failed',
  CANCELED = 'canceled',
}

// export type PaymentStatusType = keyof typeof PaymentStatusEnum;
export type PaymentStatusType = 'pending' | 'success' | 'failed' | 'canceled';

export interface PaymentProduct {
  id: string;
  name: string;
  total: number;
  subTotal: number;
  quantity: number;
  price?: number;
  product_key?: string;
  unit_key?: string;
  taxRate?: number;
  taxFactor?: TaxFactorType;
  taxType?: TaxTypesType;
  measurementUnit?: string;
  tax?: number;
}


export enum PaymentTypeEnum {
  isolated = 'isolated',
  subscription = 'subscription',
  sold = 'sold',
}

export type PaymentType = 'isolated' | 'subscription' | 'sold';

@Entity({ name: 'payments' })
export class PaymentsEntity {

  @PrimaryColumn()
  id: string;


  @ManyToOne(() => ClientEntity, client => client.id)
  client: ClientEntity; // Client

  @ManyToOne(() => SubscriptionEntity, subscription => subscription.id, {
    onDelete: 'CASCADE',
    nullable: true,
  })
  subscription: SubscriptionEntity; // Subscription


  @Column({ type: 'enum', enum: PaymentTypeEnum, default: PaymentTypeEnum.subscription })
  type: PaymentType; // type

  @Column({
    type: 'boolean',
    default: false,
  })
  isPaid: boolean; // isPaid

  @Column({ type: 'boolean', nullable: true })
  paidOnTime: boolean; // paidOnTime

  @Column({ type: 'enum', enum: PaymentFormEnum, nullable: true })
  payment_form: PaymentFormType; // payment_form

  // @Column({ type: 'enum', enum: CFDI_KEYS_ENUM, nullable: true })
  // use_cfdi: CFDI_KEYS_ENUM; // cfdi_key

  @Column({
    type: 'enum',
    default: PaymentStatusEnum.PENDING,
    enum: PaymentStatusEnum,
  })
  status: PaymentStatusType; // status

  @Column({ type: 'float' })
  total: number; // total

  @Column({ type: 'float' })
  subTotal: number; // subTotal

  @Column({ type: 'float' })
  tax: number; // tax

  @Column({ type: 'text', nullable: true })
  invoiceId: string; // invoiceId

  @Column({ type: 'text', nullable: true })
  receiptId: string; // receiptId

  @Column({ type: 'jsonb' })
  products: PaymentProduct[] // subscriptionProducts

  @Column({ type: 'text', nullable: true })
  concept: string; // concept

  @Column({ type: 'timestamp', nullable: true })
  dateLimit: Date;

  @Column({ type: 'timestamp', nullable: true })
  paidAt: Date;

  @Column({ type: "text", nullable: true, })
  createdFrom: string;

  @Column({ type: 'text', nullable: true })
  originPaymentId: string;

  @Column({ type: 'timestamp with time zone', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  updatedAt: Date;

  @DeleteDateColumn({ type: 'timestamp', nullable: true })
  deletedAt: Date;

  @Column({ type: 'jsonb', default: {} })
  metadata: any;

  @BeforeInsert()
  generateId() {
    const id = generateRandomId(10);
    this.id = 'payment-' + id;
    const today = getLocalDate();

    const todayTime = today.toISOString().split('T')[1];

    const minutes = todayTime.split(':')[1];
    const seconds = todayTime.split(':')[2].split('.')[0];

    this.createdAt = getLocalDate(undefined, {
      hours: 23,
      minutes: Number(minutes),
      seconds: Number(seconds),
    });
    this.copyProductsFromSubscription();
    this.createRandomConcept();
    this.createDateLimit();
  }

  createDateLimit() {

    const today = getLocalDate(undefined, { hours: 23 });
    // Set the time to 23:00 hours, beacuse the database set the time 6 hours before,
    // and the limit should be 17:00 hours
    const mondayLimit = addDays(today, 4);
    this.dateLimit = mondayLimit;

  }

  createRandomConcept() {
    const randomNumber = Math.floor(100000 + Math.random() * 900000); // Genera un número aleatorio de 6 dígitos
    this.concept = randomNumber.toString();
  }

  copyProductsFromSubscription() {
    // Verificar si la suscripción está definida
    // console.log('this.subscription.products', this.subscription.products)
    if (this.subscription && this.subscription.products) {
      // Copiar los productos de la suscripción al campo products de PaymentsEntity
      const selectedProducts = this.subscription.products.map(product => ({
        // id: product.id,
        // name: product.name,
        // total: product.total,
        // subTotal: product.subTotal,
        // quantity: product.quantity,
        ...product,
      }));

      // Asignar los productos seleccionados al campo products de PaymentsEntity
      this.products = selectedProducts;
    }
    // else if (this.products) {
    //   this.type = PaymentTypeEnum.isolated;
    // }
  }

  @BeforeUpdate()
  updateTimestamp() {
    this.updatedAt = new Date();
  }

}
