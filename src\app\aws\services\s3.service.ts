import { Injectable, Logger } from '@nestjs/common';
import {
  S3Client,
  PutObjectCommand,
  GetObjectCommand,
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import * as fs from 'fs';
import { basename, extname } from 'path';
import * as path from 'path';
import { MIME_TYPES } from '..';


@Injectable()
export class S3Service {
  private readonly client: S3Client;
  private readonly logger = new Logger(S3Service.name);
  private readonly bucket = process.env.AWS_BUCKET_NAME;

  constructor() {

    this.client = new S3Client({
      region: process.env.AWS_BUCKET_REGION,
      credentials: {
        accessKeyId: process.env.AWS_BUCKET_PUBLIC_KEY,
        secretAccessKey: process.env.AWS_BUCKET_SECRET_KEY,
      },
    });

  }

  /**
    * Uploads a file to the specified file path
    * @param key - The file path to upload to the bucket
    * @param filePath - The internal file path inside the uploads project folder 
  */

  async uploadFile(key: string, filePath: string): Promise<void> {
    try {

      const fileExists = fs.existsSync(filePath);

      if (!fileExists) {
        this.unlinkFile(filePath);
        throw new Error('File not found');
      }


      let stream = fs.createReadStream(filePath);

      // console.log('------------------------')
      // console.log('stream', stream)
      const fileExtension = extname(basename(filePath)).toLowerCase();

      const ContentType = MIME_TYPES[fileExtension] || MIME_TYPES.default;

      // if (typeof body === 'string') {
      //   stream = fs.createReadStream(body);
      // }
      // if (body instanceof Blob) {
      //   stream = body.stream();
      // }
      // if (body instanceof Uint8Array) {
      //   stream = body.buffer;
      // }
      // if (body instanceof Buffer) {
      //   stream = body;
      // }

      const input = {
        Bucket: this.bucket,
        Key: key,
        Body: stream,
        ContentType,
      };

      await this.client.send(new PutObjectCommand(input));
      this.unlinkFile(filePath);
      this.logger.log(`File uploaded successfully: ${key}`);
    } catch (error) {
      this.unlinkFile(filePath);
      this.logger.error(`Failed to upload file: ${error}`);
      throw error;
    }
  }

  /**
    * Retrieves a file from the specified file path
    * @param key - The file path in the bucket
  */

  getFile(key: string, expires: number = 3600) {
    const input = {
      Bucket: this.bucket,
      Key: key,
      Expires: expires,
    };
    try {
      const data = new GetObjectCommand(input);
      this.logger.log(`File retrieved successfully: ${key}`);
      return data;
    } catch (error) {
      this.logger.error(`Failed to retrieve file: ${error}`);
      throw error;
    }
  }

  /** 
   * Generates a presigned URL for the specified file path
   * @param key - The file path in the bucket
   * @param expires - (Optional) The expiration time in seconds (default 3600)
  */

  async getUrl(key: string, expires: number = 3600) {

    try {

      const command = this.getFile(key, expires);

      const url = await getSignedUrl(this.client, command);
      return url;
    } catch (error) {
      this.logger.error(`Failed to generate presigned URL: ${error}`);
      throw error;
    }
  }

  /**
    * Deletes a file from the specified file path
    * @param key - The file path in the bucket
  */
  unlinkFile(key: string) {
    fs.unlink(key, (err) => {
      if (err) {
        console.error(err)
      }
    });

  }
}
