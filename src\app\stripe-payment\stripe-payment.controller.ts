import {
  Controller,
  Get,
  Post,
  Body,
  Req,
  RawBodyRequest,
} from '@nestjs/common';
import { StripePaymentService } from './stripe-payment.service';
import { StripeSetupIntentStatusDto } from './dto/create-stripe-payment.dto';
import { UpdateStripePaymentDto } from './dto/update-stripe-payment.dto';
import { Request } from 'express';

@Controller('stripe-payment')
export class StripePaymentController {
  constructor(private readonly stripePaymentService: StripePaymentService) {}

  @Get('/setup-intent')
  async getSetupIntentSecret(@Req() req: Request) {
    const clientId = req.query.clientId;
    const setupIntent = await this.stripePaymentService.getSetupIntentSecret(
      clientId as string,
    );
    return setupIntent;
  }

  @Post('/setup-intent-status')
  async create(@Body() stripeSetupIntentStatusDto: StripeSetupIntentStatusDto) {
    const setupIntent = await this.stripePaymentService.setupIntentStatus(
      stripeSetupIntentStatusDto,
    );
    return setupIntent;
  }

  @Post('/webhook/paymentIntent')
  async createPayment(@Req() req: RawBodyRequest<Request>) {
    const paymentIntent = await this.stripePaymentService.stripePaymentIntentWebhook(
      req
    );
    return paymentIntent;
  }



}
