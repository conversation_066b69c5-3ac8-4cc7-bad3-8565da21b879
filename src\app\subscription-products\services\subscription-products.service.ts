import { HttpException, Inject, Injectable, forwardRef } from '@nestjs/common';
import { CreateSusbcriptionProductDto } from '../dto/create-subscription-product.dto';
import { UpdateSubscriptionProductDto } from '../dto/update-subscription-product.dto';
import { SubscriptionProductEntity } from '../entities/subscription-product.entity';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { FindAllSubscriptionProductsQueryDto } from '../dto/find-suscription-products.dto';
import { SubscriptionsService } from '@/app/subscriptions/services/susbscriptions.service';
import { UpdateSubscriptionDto } from '@/app/subscriptions/dto/update-subscription.dto';
import { SubscriptionEntity } from '@/app/subscriptions/entities/subscription.entity';

@Injectable()
export class SubscriptionProductsService {

  constructor(
    @InjectRepository(SubscriptionProductEntity) private readonly subscriptionProductRepository: Repository<SubscriptionProductEntity>,
    @Inject(forwardRef(() => SubscriptionsService))
    private readonly subscriptionService: SubscriptionsService,
    @InjectRepository(SubscriptionEntity) private readonly subscriptionRepository: Repository<SubscriptionEntity>,
  ) { }

  async create(createSuscriptionProductDto: CreateSusbcriptionProductDto) {

    // const 

    const subscriptionProduct = this.subscriptionProductRepository.create(createSuscriptionProductDto);

    await this.subscriptionProductRepository.save(subscriptionProduct);

    return {
      message: 'Subscription Product created successfully',
      data: subscriptionProduct,
    }
    // return 'This action adds a new suscriptionProduct';
  }

  async findAll(query: FindAllSubscriptionProductsQueryDto) {

    const relations = ['subscription.client'];

    // if (query?.withSubscription) relations.push('subscription');

    // if (query?.withClient) relations.push('subscription.client');

    const where: FindAllSubscriptionProductsQueryDto = {
      region: query.region,
    }

    if (query?.subscriptionId) where.subscriptionId = query.subscriptionId;

    const subscriptionProducts = await this.subscriptionProductRepository.find({
      where,
      relations,
    });

    return {
      message: 'Subscription Products found',
      total: subscriptionProducts.length,
      data: subscriptionProducts,
    }

  }

  async findOne(id: string, query: FindAllSubscriptionProductsQueryDto = { withClient: false, withSubscription: true }) {

    const relations = ['subscription.client'];

    // if (query?.withSubscription) relations.push('subscription');

    // if (query?.withClient) relations.push('subscription.client');

    const subscriptionProduct = await this.subscriptionProductRepository.findOne({
      where: {
        id,
        // region: query.region,
      },
      relations,
    });


    if (!subscriptionProduct) {
      throw new HttpException('Subscription Product not found', 404);
    }

    return {
      message: 'Subscription Product found',
      data: subscriptionProduct,
    }

  }

  async update(id: string, updateSubsPr: UpdateSubscriptionProductDto) {
    const subscriptionProduct = await this.subscriptionProductRepository.findOne({
      where: {
        id,
      },
      relations: ['subscription'],
    });

    if (!subscriptionProduct) {
      throw new HttpException('Subscription Product not found', 404);
    }

    const updatedSubs: SubscriptionProductEntity = {
      ...subscriptionProduct,
      ...updateSubsPr,
    };

    if (
      updateSubsPr.quantity !== subscriptionProduct.quantity,
      updateSubsPr.price !== subscriptionProduct.price,
      updateSubsPr.taxRate !== subscriptionProduct.taxRate
    ) {
      this.calculateTotalSubsProduct(updatedSubs);

      const updatedSubscriptionProduct = await this.subscriptionProductRepository.save(updatedSubs);

      await this.updateSubscriptionBySubsProduct(subscriptionProduct.subscription.id);

      delete updatedSubscriptionProduct.subscription;

      return {
        message: 'Subscription Product updated successfully',
        data: updatedSubscriptionProduct,
      }
    }

    const updatedSubscriptionProduct = await this.subscriptionProductRepository.save(updatedSubs);

    return {
      message: 'Subscription Product updated successfully',
      data: updatedSubscriptionProduct,
    }

  }

  calculateTotalSubsProduct(subscriptionProduct: SubscriptionProductEntity) {


    const quantity = subscriptionProduct.quantity;
    const price = subscriptionProduct.price;
    const taxRate = subscriptionProduct.taxRate;
    const total = quantity * price;
    const subTotal = total * (1 - (taxRate === 1 ? 0 : taxRate));
    const tax = taxRate === 1 ? 0 : total * taxRate;

    subscriptionProduct.total = Number(total.toFixed(2));
    subscriptionProduct.subTotal = Number(subTotal.toFixed(2));
    subscriptionProduct.tax = Number(tax.toFixed(2));

  }

  async updateSubscriptionBySubsProduct(id: string) {

    const { data: subscription } = await this.subscriptionService.findOne(id, { withClient: false });

    const { total: totalAmount, subTotal, tax } = subscription.products.reduce((acc, product) => {
      return {
        total: acc.total + product.total,
        subTotal: acc.subTotal + product.subTotal,
        tax: acc.tax + product.tax,
      }
    }, { total: 0, subTotal: 0, tax: 0 });

    const updatedSubscription = {
      total: totalAmount,
      subTotal,
      tax,
    };

    // console.log('updatedSubscription', {
    //   total: updatedSubscription.total,
    //   subTotal: updatedSubscription.subTotal,
    //   tax: updatedSubscription.tax,
    // })

    const amounts = {
      total: updatedSubscription.total,
      subTotal: updatedSubscription.subTotal,
      tax: updatedSubscription.tax,
    }


    const subscriptionUpdated = await this.subscriptionRepository.update(id, amounts);

    return subscriptionUpdated;
  }

  async remove(id: string) {

    const subscriptionProduct = await this.subscriptionProductRepository.findOne({
      where: {
        id,
      }
    });

    if (!subscriptionProduct) {
      throw new HttpException('Subscription Product not found', 404);
    }

    await this.subscriptionProductRepository.delete({
      id,
    });

    return {
      message: 'Subscription Product deleted successfully',
    }

  }
}
