import { NestFactory } from '@nestjs/core';
import { AppModule } from '@/app/app.module';
import logger from './logger.config';
import { FastifyAdapter, NestFastifyApplication } from '@nestjs/platform-fastify';
import { ConfigService } from '@nestjs/config';
import { setupGlobalPipes, setupSwagger } from './config/setup-app.config';
import { corsConfig } from './config/cors.config';
// import { createAllMissingReceiptsAndInvoices, invoiceAllMissingReceipts } from './changes';

async function bootstrap() {

  try {
    const app = await NestFactory.create<NestFastifyApplication>(AppModule,
      new FastifyAdapter(), {
      logger,
      rawBody:true
    });

    setupGlobalPipes(app);

    const configService = app.get(ConfigService);

    const PORT = configService.get('PORT') || 4000;
    const isProd = configService.get('NODE_ENV') === 'production';

    corsConfig(app, isProd);

    if (!isProd) {
      setupSwagger(app);
    }

    await app.listen(PORT, '0.0.0.0');
    console.log(`Application is running on: http://localhost:${PORT}`);
    // await createAllMissingReceipts(app);
    // await invoiceAllMissingReceipts(app);
  } catch (error) {
    console.log('Error en el servidor', error);
  }
}

bootstrap();