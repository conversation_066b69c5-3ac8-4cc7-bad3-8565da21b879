import { Controller, Get, Post, Body, Patch, Param, Delete, Query } from '@nestjs/common';
import { NotificationService } from '../services/notification.service';
import { RegisterNotificationDto } from '../dto/register-notification.dto';
import { UpdateNotificationDto } from '../dto/update-notification.dto';
import { NotificationPayloadDto } from '../dto/notification-payload.dto';

@Controller('notification')
export class NotificationController {
  constructor(private readonly notificationService: NotificationService) {}

  @Post('register/:id')
  create(@Param('id') id: string, @Body() registerNotificationDto: RegisterNotificationDto) {
    return this.notificationService.registerForPushNotification(id, registerNotificationDto);
  }

  @Post('activate/:id')
  activate(@Param('id') id: string, @Body() updateNotificationDto: UpdateNotificationDto) {
    return this.notificationService.activatePushNotification(id, updateNotificationDto);
  }

  @Post('disable/:id')
  disable(@Param('id') id: string, @Body() updateNotificationDto: UpdateNotificationDto) {
    return this.notificationService.disablePushNotification(id, updateNotificationDto);
  }

  @Post('sendpush')
  send(@Body() notificationPayloadDto:NotificationPayloadDto){
    const {associateId, title, body, data} = notificationPayloadDto;
    return this.notificationService.sendPushViaAssociateId(associateId, title, body, data);
  }
}
