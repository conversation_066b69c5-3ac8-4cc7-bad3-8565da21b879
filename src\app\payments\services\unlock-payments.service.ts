import {
  NODE_ENV,
  UNLOCK_PAYMENT_REMINDER_TUESDAY_TEMPLATE_ID,
  UNLOCK_PAYMENT_REMINDER_WEDNESDAY_TEMPLATE_ID,
} from '@/constants';
import { HttpException, Injectable, Logger } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import { In, MoreThanOrEqual, Not, Repository } from 'typeorm';
import {
  PaymentsEntity,
  PaymentStatusEnum,
  PaymentTypeEnum,
} from '../entities/payment.entity';
import { StockVehicleService } from './stock.service';
import { PaymentsService } from './payments.service';
import { SubscriptionEntity } from '@/app/subscriptions/entities/subscription.entity';
import { UnlockPaymentPlateDto } from '../dto/create-payment.dto';
import {
  sendUnlockPaymentConfirmationWAMessage,
  sendUnlockPaymentWAMessage,
  sendVehicleUnlockConfirmationWAMessage,
} from '@/hilos-whatsapp/payment-message';
import * as fs from 'fs';
import * as path from 'path';
import { Parser } from 'json2csv';
import { sendEmail, EmailAttachment } from '@/providers/nodemailer';
import { GpsService } from './gps.service';
import { slackApi } from '@/logger.config';

const MX_CLIENTS_UNLOCK_PAYMENTS_CRON_EVERY_MONDAY_FIVE_PM =
  NODE_ENV !== 'production' ? '0 17 * * 1' : '0 17 * * 1';
const MX_CLIENTS_UNLOCK_PAYMENTS_CRON_EVERY_MONDAY_SIX_PM =
  NODE_ENV !== 'production' ? '0 18 * * 1' : '0 18 * * 1';
const MX_CLIENTS_UNLOCK_PAYMENTS_CRON_EVERY_TUESDAY_WEDNESDAY_TWELVE_PM =
  NODE_ENV !== 'production' ? '0 12 * * 2,3' : '0 12 * * 2,3';
const timeZone =
  NODE_ENV !== 'production' ? 'Asia/Karachi' : 'America/Mexico_City';

console.log(
  'MX_CLIENTS_UNLOCK_PAYMENTS_CRON_EVERY_MONDAY_SIX_PM',
  MX_CLIENTS_UNLOCK_PAYMENTS_CRON_EVERY_MONDAY_SIX_PM,
);
console.log(
  'MX_CLIENTS_UNLOCK_PAYMENTS_CRON_EVERY_MONDAY_FIVE_PM',
  MX_CLIENTS_UNLOCK_PAYMENTS_CRON_EVERY_MONDAY_FIVE_PM,
);
console.log(
  'MX_CLIENTS_UNLOCK_PAYMENTS_CRON_EVERY_TUESDAY_WEDNESDAY_TWELVE_PM',
  MX_CLIENTS_UNLOCK_PAYMENTS_CRON_EVERY_TUESDAY_WEDNESDAY_TWELVE_PM,
);
console.log('timeZone', timeZone);
const isDev = NODE_ENV !== 'production';

export const PUBLIC_API_URL = process.env.NEXT_PUBLIC_API_URL;

export const SUPPORT_EMAIL = process.env.SUPPORT_EMAIL;

export enum RUNTIMEEnum {
  FIVE_PM = '5pm',
  SIX_PM = '6pm',
}

// Go up from current file to root -> then into uploads/unlock_payments
const unlockPaymentsDir = path.resolve(
  __dirname,
  '../../../../uploads/unlock_payments',
);

// Make sure the folder exists
if (!fs.existsSync(unlockPaymentsDir)) {
  fs.mkdirSync(unlockPaymentsDir, { recursive: true });
}
console.log('unlock payments CSV path', unlockPaymentsDir);

@Injectable()
export class UnlockPaymentsService {
  private readonly logger = new Logger(UnlockPaymentsService.name);
  constructor(
    @InjectRepository(PaymentsEntity)
    private readonly paymentsRepository: Repository<PaymentsEntity>,
    @InjectRepository(SubscriptionEntity)
    private readonly subscriptionRepository: Repository<SubscriptionEntity>,
    private readonly stockVehicleService: StockVehicleService,
    private readonly paymentsService: PaymentsService,
    private readonly gpsService: GpsService,
  ) {}

  /* CRONJOB FOR CREATE Unlock PAYMENTS */
  @Cron(MX_CLIENTS_UNLOCK_PAYMENTS_CRON_EVERY_MONDAY_FIVE_PM, {
    timeZone,
  })
  async handleUnlockPaymentsFivePM() {
    if (process.env.RUN_MX_CRON_JOB === 'true') {
      this.logger.log(
        '[UnlockPaymentsService] handleUnlockPayments - Running MX Clients Unlock Payments Cron Job at 5pm',
      );
      await this.processUnlockPayments(RUNTIMEEnum.FIVE_PM);
    }
  }

  /* CRONJOB FOR CREATE Unlock PAYMENTS */
  @Cron(MX_CLIENTS_UNLOCK_PAYMENTS_CRON_EVERY_MONDAY_SIX_PM, {
    timeZone,
  })
  async handleUnlockPaymentsSixPM() {
    if (process.env.RUN_MX_CRON_JOB === 'true') {
      this.logger.log(
        '[UnlockPaymentsService] handleUnlockPayments - Running MX Clients Unlock Payments Cron Job at 6pm',
      );
      await this.processUnlockPayments(RUNTIMEEnum.SIX_PM);
    }
  }

  async processUnlockPayments(runTime: RUNTIMEEnum) {
    const RunTimeMap = {
      '5pm': { region: Not(In(['TIJ', 'MXLI'])) },
      '6pm': { region: In(['TIJ', 'MXLI']) },
    };

    try {
      this.logger.log(
        `[UnlockPaymentsService] processUnlockPayments ${runTime} - cron job started at ${new Date()}`,
      );

      let subscriptions;

      // Retrieve the region condition based on the type, defaulting to an empty condition if type is invalid
      const regionCondition = RunTimeMap[runTime] || {};
      // Execute the query with the dynamically determined region condition
      subscriptions = await this.subscriptionRepository.find({
        where: {
          isActive: true,
          ...regionCondition,
          client: {
            country: 'MX',
          },
        },
        relations: ['client'],
      });

      if (!subscriptions || subscriptions.length === 0) {
        this.logger.log(
          `[UnlockPaymentsService] processUnlockPayments ${runTime} - no subscriptions found`,
        );
        return;
      }

      this.logger.log(
        `[UnlockPaymentsService] processUnlockPayments ${runTime} - found ${subscriptions.length} subscriptions`,
      );

      const unpaidSubsPayments: PaymentsEntity[] = [];

      await Promise.all(
        subscriptions.map(async (subscription) => {
          const payment = await this.paymentsRepository.findOne({
            where: {
              subscription: {
                id: subscription.id, // Use the subscription relation
              },
            },
            order: {
              createdAt: 'DESC',
            },
            relations: ['client', 'subscription'], // Make sure to load the subscription relation as well
          });

          if (payment && !payment.isPaid && payment.dateLimit < new Date()) {
            const isSubcriptionPaymentToIsoPayment =
              await this.paymentsRepository.findOne({
                where: {
                  originPaymentId: payment.id,
                  type: PaymentTypeEnum.isolated,
                  total: 100,
                },
              });

            if (!isSubcriptionPaymentToIsoPayment) {
              unpaidSubsPayments.push(payment);
            }
          }
        }),
      );

      this.logger.log(
        `[UnlockPaymentsService] processUnlockPayments ${runTime} - found ${unpaidSubsPayments.length} unpaid weekly payments for unlock payment creation`,
      );

      const success: {
        weekly_payment_id: string;
        unlock_payment_id: string;
        contractNumber: string;
        plates: string;
        phone: string;
        country: string;
        region: string;
      }[] = [];
      const fail: { weekly_payment_id: string; reason: string }[] = [];

      if (unpaidSubsPayments && unpaidSubsPayments.length > 0) {
        await Promise.all(
          unpaidSubsPayments.map(async (payment) => {
            const response = await this.createUnlockPayment(payment);

            if (response.status) {
              const unlockPaymentId = response.data.paymentId;
              const stockVehicle =
                await this.stockVehicleService.getStockVehicleByAssociateId(
                  payment.client.associateId,
                );

              if (
                stockVehicle?.data?.carNumber &&
                stockVehicle?.data?.carPlates?.plates
              ) {
                const stockVehicleData = stockVehicle.data;

                success.push({
                  weekly_payment_id: payment.id,
                  unlock_payment_id: response.data.paymentId,
                  contractNumber: stockVehicleData.carNumber,
                  plates: stockVehicleData.carPlates.plates,
                  phone: payment.client.phone,
                  country: payment.client.country,
                  region: payment.client.region,
                });
              } else {
                success.push({
                  weekly_payment_id: payment.id,
                  unlock_payment_id: response.data.paymentId,
                  contractNumber: '',
                  plates: '',
                  phone: payment.client.phone,
                  country: payment.client.country,
                  region: payment.client.region,
                });
              }
            } else {
              fail.push({
                weekly_payment_id: payment.id,
                reason: response.message,
              });
            }
          }),
        );
      }

      this.logger.log(
        `[UnlockPaymentsService] processUnlockPayments ${runTime} - ${success.length} unlock payments created`,
      );
      this.logger.log(
        `[UnlockPaymentsService] processUnlockPayments ${runTime} - fail to create ${fail.length} unlock payments`,
      );

      const successFileName = `unlocked-payments-${Date.now()}.csv`;
      const failFileName = `failed-unlocked-payments-${Date.now()}.csv`;
      const emailAttachments: EmailAttachment[] = [];
      //generate CSV
      if (success.length > 0) {
        const filePath = await this.generateAndSaveCSV(
          success,
          successFileName,
        );

        this.logger.log(
          `[UnlockPaymentsService] processUnlockPayments ${runTime} - CSV ${successFileName} created`,
        );

        if (filePath) {
          emailAttachments.push({
            filename: successFileName,
            path: filePath,
            contentType: 'text/csv',
          });

          this.sendFilesOnSlack(
            'Lista de pagos de desbloqueo creados automáticamente por el sistema / List of unlock payments created automatically by system',
            successFileName,
            filePath,
          );
        }
      }

      if (fail.length > 0) {
        const filePath = await this.generateAndSaveCSV(fail, failFileName);

        this.logger.log(
          `[UnlockPaymentsService] processUnlockPayments ${runTime} - CSV ${failFileName} created`,
        );

        if (filePath) {
          emailAttachments.push({
            filename: failFileName,
            path: filePath,
            contentType: 'text/csv',
          });

          this.sendFilesOnSlack(
            'Lista de pagos semanales para los cuales el sistema no genera el pago de desbloqueo / List of weekly payments against which system fails to generated unlock payment',
            failFileName,
            filePath,
          );
        }
      }

      if (emailAttachments.length > 0) {
        await sendEmail(
          SUPPORT_EMAIL,
          'Informe de desbloqueo de pagos / Unlock Payments Report',
          '<p>Por favor, encuentre el archivo CSV adjunto. / Please find the CSV attached.</p>',
          emailAttachments,
        );

        this.logger.log(
          `[UnlockPaymentsService] processUnlockPayments ${runTime} - files send to support via email ${SUPPORT_EMAIL}`,
        );
      }

      return {
        data: {
          success: success,
          fail: fail,
        },
        status: true,
      };
    } catch (error: any) {
      this.logger.error(
        `[UnlockPaymentsService] processUnlockPayments ${runTime} internal server error`,
        {
          error: error?.message || error,
          stack: error?.stack,
        },
      );
      return {
        message: 'Internal server error',
        status: false,
      };
    }
  }

  async createUnlockPayment(payment: PaymentsEntity) {
    const paymentId = payment.id;
    try {
      //find unlock payment
      const isSubcriptionPaymentToIsoPayment =
        await this.paymentsRepository.findOne({
          where: {
            originPaymentId: payment.id,
          },
        });
      if (isSubcriptionPaymentToIsoPayment) {
        this.logger.log(
          `[UnlockPaymentsService] createUnlockPayment - Unlock payment already created for payment ${paymentId}`,
        );
        return {
          message: `Unlock payment already created for payment ${paymentId}`,
          status: false,
        };
      }

      if (payment.client) {
        const plate = '';
        const unlockPaymentPlateDto = new UnlockPaymentPlateDto(plate, true);

        const response = await this.paymentsService.createUnlockPaymentByClient(
          payment.client.id,
          unlockPaymentPlateDto,
        );
        if (response.message === 'Payment created successfully') {
          return {
            message: `Payment created successfully ${response.data?.paymentId}`,
            status: true,
            data: response.data,
          };
        } else {
          return {
            message: response.message,
            status: false,
          };
        }
      } else {
        this.logger.log(
          `[UnlockPaymentsService] createUnlockPayment - client not found for payment ${paymentId}`,
        );
        return {
          message: `client not found for payment ${paymentId}`,
          status: false,
        };
      }
    } catch (error: any) {
      this.logger.error(
        `[UnlockPaymentsService] createUnlockPayment - internal server error`,
        {
          error: error?.message || error,
          stack: error?.stack,
        },
      );
      return {
        message: `Internal server error ${paymentId} ${error.message}`,
        status: false,
      };
    }
  }

  /* CRONJOB FOR Pending Unlock PAYMENTS reminders  */
  @Cron(
    MX_CLIENTS_UNLOCK_PAYMENTS_CRON_EVERY_TUESDAY_WEDNESDAY_TWELVE_PM,
    {
    timeZone,
  },
)
  async handlePendingUnlockPaymentsReminder() {
    if (process.env.RUN_MX_CRON_JOB === 'true') {
      this.logger.log(
        '[UnlockPaymentsService] handlePendingUnlockPaymentsReminder - Running MX Clients Unlock Payments reminder Cron Job',
      );
      await this.sendPendingUnlockPaymentsReminder();
    }
  }

  async sendPendingUnlockPaymentsReminder() {
    try {
      this.logger.log(
        `[UnlockPaymentsService] sendPendingUnlockPaymentsReminder - cron job started at ${new Date()}`,
      );

      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 4);

      //Fetching unlock payments created in last 4 days
      const unpaidUnlockPayments = await this.paymentsRepository.find({
        where: {
          type: PaymentTypeEnum.isolated,
          total: 100,
          createdAt: MoreThanOrEqual(sevenDaysAgo),
          status: Not(PaymentStatusEnum.CANCELED),
        },
        relations: ['client'],
      });

      this.logger.log(
        `[UnlockPaymentsService] sendPendingUnlockPaymentsReminder - ${unpaidUnlockPayments.length} unpaid unlock payments`,
      );

      let counter = 0;
      for (const unlockPayment of unpaidUnlockPayments) {
        const subPayment = await this.paymentsRepository.findOne({
          where: {
            id: unlockPayment.originPaymentId,
          },
        });

        if (subPayment && (!subPayment.isPaid || !unlockPayment.isPaid)) {
          counter++;
          await this.sendPaymentReminderNotification(subPayment, unlockPayment);
        }
      }

      this.logger.log(
        `[UnlockPaymentsService] sendPendingUnlockPaymentsReminder - ${counter} payment notifications send`,
      );

      const message = `Notificación de recordatorio de pago enviada a ${counter} clientes / Payment reminder notification send to ${counter} clients`;

      await this.sendMessageOnSlack(message);

      this.logger.log(
        `[UnlockPaymentsService] sendPendingUnlockPaymentsReminder - ended`,
      );

      return;
    } catch (error: any) {
      this.logger.error(
        `[UnlockPaymentsService] sendPendingUnlockPaymentsReminder - error`,
        {
          error: error?.message || error,
          stack: error?.stack,
        },
      );
      return;
    }
  }

  async createUnlockPaymentTest(type: string) {
    //this is an internal test function just to test functionality on developement so we dont need to run cron job
    if (type === '0') this.processUnlockPayments(RUNTIMEEnum.FIVE_PM);
    else this.processUnlockPayments(RUNTIMEEnum.SIX_PM);
    return true;
  }

  async generateAndSaveCSV(data: any[], fileName: string) {
    const parser = new Parser();
    const csv = parser.parse(data);

    const filePath = path.join(unlockPaymentsDir, fileName);
    fs.writeFileSync(filePath, csv);

    return filePath;
  }

  async verifyPayment(paymentId: string) {
    try {
      const payment = await this.paymentsRepository.findOne({
        where: {
          id: paymentId,
        },
        relations: ['client'],
      });

      if (!payment) {
        return;
      }

      if (payment.type === PaymentTypeEnum.isolated && payment.total === 100) {
        const subPayment = await this.paymentsRepository.findOne({
          where: {
            id: payment.originPaymentId,
          },
          relations: ['client'],
        });

        if (subPayment?.isPaid) {
          this.logger.log({
            message: `[UnlockPaymentsService] verifyPayment - both unlock payment ${payment.id} and weekly payment ${subPayment.id} are paid add request to queue for vehicle unlock`,
          });

          this.sendPaymentConfirmationNotification(payment);

          this.pushPaymentToUnlock(payment);
        }
      } else if (payment.type === PaymentTypeEnum.subscription) {
        const isoPayment = await this.paymentsRepository.findOne({
          where: {
            originPaymentId: payment.id,
          },
          relations: ['client'],
        });

        if (isoPayment?.isPaid) {
          this.logger.log({
            message: `[UnlockPaymentsService] verifyPayment - both unlock payment ${isoPayment.id} and weekly payment ${payment.id} are paid add request to queue for vehicle unlock`,
          });

          this.sendPaymentConfirmationNotification(isoPayment);
          this.pushPaymentToUnlock(isoPayment);
        }
      }
    } catch (error: any) {
      this.logger.error(`[UnlockPaymentsService] verifyPayment - error`, {
        error: error?.message || error,
        stack: error?.stack,
      });
    }
  }

  async pushPaymentToUnlock(payment: PaymentsEntity) {
    try {
      this.logger.log(
        `[UnlockPaymentsService] pushPaymentToUnlock - payment ${payment.id}`,
      );

      const previousPendingPayments = await this.paymentsRepository.find({
        where: {
          client: { id: payment.client.id },
          isPaid: false,
        },
      });

      if (previousPendingPayments.length > 0) {
        //Some previous payments are pending cant unlock vehicle
        this.logger.log(
          `[UnlockPaymentsService] pushPaymentToUnlock - payment ${payment.id} Can't unlock vehicle - ${previousPendingPayments.length} previous unpaid payments found.`,
        );

        return;
      }

      const maxRetries = 3;
      const delayMs = 5 * 60 * 1000; // 5 minutes

      const stockVehicle =
        await this.stockVehicleService.getStockVehicleByAssociateId(
          payment.client.associateId,
        );

      if (stockVehicle && stockVehicle.status && stockVehicle.data) {
        if (stockVehicle.data.gpsNumber) {
          const gps = stockVehicle.data.gpsNumber;
          for (let attempt = 1; attempt <= maxRetries; attempt++) {
            this.logger.log(
              `[UnlockPaymentsService] pushPaymentToUnlock - payment ${payment.id} Attempt ${attempt}: Sending unlock request for GPS ${gps}`,
            );

            const gpsData = await this.gpsService.gpsStatus(gps);

            if (gpsData) {
              this.logger.log(
                `[UnlockPaymentsService] pushPaymentToUnlock - payment ${payment.id}  GPS: ${gps} gpsStatus response: ${JSON.stringify(gpsData)}`,
              );

              if (gpsData.engineBlock === '0' || gpsData.engineBlock === 0) {
                await this.sendVehicleUnlockNotification(payment);
                return;
              }
            }

            const resp = await this.gpsService.setUnlockEvent(gps);
            if (resp) {
              this.logger.log(
                `[UnlockPaymentsService] pushPaymentToUnlock - payment ${payment.id} GPS: ${gps} setUnlockEvent response: ${JSON.stringify(resp)}`,
              );
            }
            const delay = delayMs * attempt;
            this.logger.log(
              `[UnlockPaymentsService] pushPaymentToUnlock - payment ${payment.id} Waiting for ${delay / 60000} minutes before next retry...`,
            );

            await new Promise((res) => setTimeout(res, delay));

            this.logger.log(
              `[UnlockPaymentsService] pushPaymentToUnlock - payment ${payment.id} Waiting over`,
            );
          }

          const gpsData = await this.gpsService.gpsStatus(gps);

          if (gpsData) {
            this.logger.log(
              `[UnlockPaymentsService] pushPaymentToUnlock - payment ${payment.id}  GPS: ${gps} gpsStatus response: ${JSON.stringify(gpsData)}`,
            );

            if (gpsData.engineBlock === '0' || gpsData.engineBlock === 0) {
              await this.sendVehicleUnlockNotification(payment);
              return;
            }
          }
        } else {
          this.logger.log(
            `[UnlockPaymentsService] pushPaymentToUnlock - payment ${payment.id} gps not found`,
          );
        }
      } else {
        this.logger.log(
          `[UnlockPaymentsService] pushPaymentToUnlock - payment ${payment.id} stockVehicle not found`,
        );
      }
      this.logger.log(
        `[UnlockPaymentsService] pushPaymentToUnlock - payment ${payment.id} unable to unlock vehicle sending email`,
      );
      let plates = 'N/A';
      let contractNumber = 'N/A';
      if (stockVehicle?.status && stockVehicle?.data?.carPlates?.plates)
        plates = stockVehicle.data?.carPlates?.plates;
      if (stockVehicle?.status && stockVehicle?.data?.carNumber)
        contractNumber = stockVehicle.data?.carNumber;
      await sendEmail(
        SUPPORT_EMAIL,
        'No se puede desbloquear automáticamente el vehículo / Unable to automatically unlock vehicle',
        `<p>
              El sistema no puede desbloquear automáticamente el vehículo con la matrícula <strong>${plates}</strong> contra el pago <strong>${payment.id}</strong>.
              / The system is unable to automatically unlock the vehicle with plate <strong>${plates}</strong> against payment <strong>${payment.id}</strong>.
              <br/>
              Por favor revise este caso. / Please look into this.
            </p>`,
      );

      const message = `*⚠️ Unlock Failure Alert*\n\nEl sistema no puede desbloquear automáticamente el vehículo con la matrícula *${plates}*, contrato *${contractNumber}* contra el pago *${payment.id}*.\n/The system is unable to automatically unlock the vehicle with plate *${plates}*, contract *${contractNumber}* against payment *${payment.id}*.\n\nPor favor revise este caso. / Please look into this.`;

      await this.sendMessageOnSlack(message);
    } catch (error: any) {
      this.logger.error(
        `[UnlockPaymentsService] pushPaymentToUnlock - payment ${payment.id} error`,
        {
          error: error?.message || error,
          stack: error?.stack,
        },
      );
    }
  }

  async sendPaymentConfirmationNotification(payment: PaymentsEntity) {
    this.logger.log({
      message: `[UnlockPaymentsService] sendPaymentConfirmationNotification - send unlock Payment confirmation notification for payment ${payment.id}`,
    });

    await sendUnlockPaymentConfirmationWAMessage(
      payment.client.name,
      payment.client.phone,
    );
  }

  async sendVehicleUnlockNotification(payment: PaymentsEntity) {
    this.logger.log({
      message: `[UnlockPaymentsService] sendVehicleUnlockNotification - send unlock vechicle confirmation notification for payment ${payment.id}`,
    });

    await sendVehicleUnlockConfirmationWAMessage(
      payment.client.name,
      payment.client.phone,
    );
  }

  async sendPaymentReminderNotification(
    subPayment: PaymentsEntity,
    unlockPayment: PaymentsEntity,
  ) {
    const dayToTemplateMap = {
      Tuesday: UNLOCK_PAYMENT_REMINDER_TUESDAY_TEMPLATE_ID,
      Wednesday: UNLOCK_PAYMENT_REMINDER_WEDNESDAY_TEMPLATE_ID,
    };

    const currentDay = new Date().toLocaleString('en-US', { weekday: 'long' }); // returns full day name like 'Tuesday'
    const templateId = dayToTemplateMap[currentDay];

    if (!templateId) {
      this.logger.warn({
        message: `[UnlockPaymentsService] sendPaymentReminderNotification - No template configured for ${currentDay}`,
      });
      return;
    }

    if (!unlockPayment.client?.name || !unlockPayment.client?.phone) {
      this.logger.error({
        message: `[UnlockPaymentsService] sendPaymentReminderNotification - Missing client details for unlockPayment ${unlockPayment.id}`,
      });
      return;
    }

    this.logger.log({
      message: `[UnlockPaymentsService] sendPaymentReminderNotification - Sending unlock payment reminder (${currentDay}) for unlockPayment ID: ${unlockPayment.id}`,
    });

    await sendUnlockPaymentWAMessage(
      unlockPayment.client.name,
      unlockPayment.client.phone,
      subPayment.id,
      unlockPayment.id,
      templateId,
    );
  }

  async sendMessageOnSlack(message: string) {
    this.logger.log({
      message: `[UnlockPaymentsService] sendMessageOnSlack`,
    });

    await slackApi.chat.postMessage({
      text: message,
      channel: process.env.ACTIVATION_PAYMENT_CHANNEL!,
    });
  }

  async sendFilesOnSlack(message: string, fileName: string, filePath: string) {
    this.logger.log({
      message: `[UnlockPaymentsService] sendFilesOnSlack`,
    });

    await slackApi.files.uploadV2({
      channel_id: process.env.ACTIVATION_PAYMENT_CHANNEL!,
      initial_comment: message,
      filename: fileName,
      file: fs.createReadStream(filePath),
    });
  }
}