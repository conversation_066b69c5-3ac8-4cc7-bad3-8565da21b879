import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { KeysController } from './controllers/keys.controller';
import { KeysService } from "./services/keys.service";
import { KeyEntity } from './entities/keys.entity';
import { TypeOrmModule } from '@nestjs/typeorm';

@Module({
  imports: [
    TypeOrmModule.forFeature([KeyEntity]),
  ],
  providers: [KeysService],
  controllers: [KeysController],
  exports: [KeysService]
})

export class KeysModule { }
