import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PaymentTransactionStatusType, PaymentTransactionsEntity } from '../entities/payment-transactions.entity';
import { PaymentsEntity } from '../entities/payment.entity';

@Injectable()
export class PaymentTransactionService {

  constructor(
    @InjectRepository(PaymentTransactionsEntity) private paymentTransactionsRepository: Repository<PaymentTransactionsEntity>,
  ) { }

  async create(data: { monexData: any, description: string, amount: number, status: PaymentTransactionStatusType, payment: PaymentsEntity }) {

    const transaction = this.paymentTransactionsRepository.create(data);
    await this.paymentTransactionsRepository.save(transaction);
    return
  }

  async getPaymentTransactionsForPaymentId(paymentId:string){
    const paymentTransactions =  await this.paymentTransactionsRepository.find({
      where : {
        payment: {
          id : paymentId,
        }
      }
    });
    return paymentTransactions;
  }

}
