import { RegionsEnum, RegionsType } from '@/types/entities.types';
import { TaxFactorEnum, TaxFactorType, TaxTypesEnum, TaxTypesType } from "./../entities/product.entity";
import { IsBoolean, IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString, Length, Max, MaxLength, Min, MinLength, ValidateIf } from 'class-validator';

export class CreateProductDto {

  @Length(1)
  @IsString()
  @IsNotEmpty()
  name: string; // name

  @Min(1)
  @IsNumber({
    allowInfinity: false,
    allowNaN: false,
    maxDecimalPlaces: 2,
  })
  @IsNotEmpty()
  price: number; // price

  @IsString()
  @MinLength(1)
  @IsOptional()
  product_key: string; // product_key


  @IsOptional()
  @IsString()
  @MinLength(1)
  @MaxLength(3)
  unit_key: string; // unit_key

  @IsString()
  @IsEnum(RegionsEnum)
  region: RegionsType; // region


  @IsOptional()
  @IsBoolean()
  hasTaxes: boolean; // hasTaxes

  @IsNumber({
    allowInfinity: false,
    allowNaN: false,
    maxDecimalPlaces: 2,
  })
  @Min(0.01)
  @Max(1, {
    message: 'The tax rate must be a value between 0.01 and 1, the percentage of the tax rate in decimal format. Example: 0.16 for 16% tax rate.'
  })
  @ValidateIf((object) => object.hasTaxes === true)
  taxRate?: number; // taxRate

  @IsString()
  @IsEnum(TaxFactorEnum)
  @ValidateIf((object) => object.hasTaxes === true)
  taxFactor?: TaxFactorType; // TaxFactor

  @IsString()
  @IsEnum(TaxTypesEnum)
  @ValidateIf((object) => object.hasTaxes === true)
  taxType?: TaxTypesType; // taxType


  @IsOptional()
  @IsString()
  country?: any; 

  @IsOptional()
  @IsString()
  state?: string; // For USA


}
