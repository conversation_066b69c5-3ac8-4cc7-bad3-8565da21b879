import { HttpException, Inject, Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { PaymentStatusEnum, PaymentsEntity } from '../entities/payment.entity';
import { Between, FindOptionsSelect, MoreThanOrEqual, Repository, MoreThan, And, LessThan, LessThanOrEqual, MongoClient} from 'typeorm';
import { getLastDay, getLastMonth, getLastThursday, getLastYear, getLocalDate } from '@/lib/dates';
import { PaymentTransactionsEntity } from "../entities/payment-transactions.entity";
import { PaymentsService } from "./payments.service";
import { HttpService } from "@nestjs/axios";
import { plainToClass } from 'class-transformer';
import { FindQueryPaymentDto } from '../dto/find-query-payment.dto';
import { ClientsService } from '@/app/clients/services/clients.service';
import { PaymentTransactionService } from './payment-transactions.service';
import { Db, ObjectId } from 'mongodb';


enum PaymentStatus {
    PENDING = 'pending',
    PAID = 'paid',
    OVERDUE = 'overdue',
    PAID_WITH_LATE_FEE = 'paid-with-late-fee',
  };

enum LoanType {
    NCE = 'NCE', // normal contract extension
    ADD = 'ADD', // Addendum
    RES = 'RES',
}  
@Injectable()
export class PaymentsLoanTapeService {
  private readonly logger = new Logger(PaymentsLoanTapeService.name);
  constructor(
    @Inject('MONGODB_CONNECTION')
    private db: Db,
    @InjectRepository(PaymentsEntity) private readonly paymentRepository: Repository<PaymentsEntity>,
    @InjectRepository(PaymentTransactionsEntity) private readonly paymentTransactionRepository: Repository<PaymentTransactionsEntity>,
    private readonly paymentService: PaymentsService,
    private readonly clientService: ClientsService,
    private readonly httpService: HttpService,
    private readonly paymentTransactionService: PaymentTransactionService,
  ) { }

  async findByAssociateId(associateId: string){
    const mainContract = await this.db.collection('maincontractschemas').findOne({associatedId: new ObjectId(associateId)});
    const associatePayments = await this.db.collection('associatepayments').findOne({associateId: new ObjectId(associateId)});
    const clientData = await this.clientService.findByAssociateId(associateId);
    const client = clientData.data;
    let amountOutstanding = mainContract.totalPrice;
    const response = [];
    for(const [index, duration] of mainContract.allPayments.entries()){
            
        let lateFeeApplied = false;
        let lateFeePaid = false;
        let paymentStatus = PaymentStatus.PENDING;
        let payments = [];
        let fee = 0;
        let weeklyPayment = mainContract.weeklyRent;
        let loanType = LoanType.NCE;

        if(associatePayments.adendumGenerated){
            loanType = LoanType.ADD;
        }

        if(associatePayments?.paymentsArray[index] && associatePayments?.paymentsArray[index].weeklyCost !== undefined){
            weeklyPayment = associatePayments?.paymentsArray[index].weeklyCost;
        }  

        if (associatePayments?.paymentsArray[index] && associatePayments?.paymentsArray[index].fee !== undefined) {
            fee = associatePayments?.paymentsArray[index].fee;
        }

        const [day, month, year] = duration.day.split("-");
        // Months in JavaScript Date are zero-indexed, so we subtract 1 from the month
        const startDate = new Date(year, month - 1, day);
        const todayDate = new Date("2024-10-20");

        if(startDate < todayDate){
            // Calculate the previous week's Thursday
            const givenWeekday = startDate.getUTCDay(); // 0 (Sunday) to 6 (Saturday)
            const daysSincePreviousThursday = (givenWeekday + 3) % 7 + 1; // Days to go back to the previous Thursday
            const previousThursday = new Date(startDate);
            previousThursday.setDate(startDate.getDate() - daysSincePreviousThursday);
             
            let paymentsRep = null
            if(index === 0){
                console.log('getting payments for previous week in IF statement', startDate, previousThursday);
                const threeDaysBefore = new Date(startDate);
                threeDaysBefore.setDate(startDate.getDate()-4);
                const nextDay = new Date(startDate);
                nextDay.setDate(startDate.getDate() +3);
                paymentsRep = await this.paymentRepository.find({
                    where: {
                        client : {id : client.id},
                        createdAt : And(MoreThanOrEqual(threeDaysBefore), LessThanOrEqual(nextDay))
                    }
                })
            } else {
                console.log('getting payments for previous week in ELSE statement', startDate, previousThursday);
                paymentsRep = await this.paymentRepository.find({
                    where: {
                        client : {id : client.id},
                        createdAt : And(LessThan(startDate), MoreThanOrEqual(previousThursday))
                    }
                })
            }
            if(paymentsRep && paymentsRep.length > 0){
                // console.log('total payments', paymentsRep.length);
                for(const payment of paymentsRep){
                    const paymentTransactions = await this.paymentTransactionService.getPaymentTransactionsForPaymentId(payment.id);
                    payment['paymentTransactions'] = paymentTransactions;

                    // if(payment.total <= 1000){
                    //     lateFeeApplied = true;
                    //     if(payment.isPaid === true){
                    //         lateFeePaid = true;
                    //     }
                    // }

                    if (associatePayments?.paymentsArray[index] && associatePayments?.paymentsArray[index].fee === undefined) {
                        if (payment.total <= 1000) {
                            lateFeeApplied = true;
                            if (payment.isPaid === true) {
                                lateFeePaid = true;
                            }
                        }
                    }
                    // console.log('paymentTransactions length', paymentTransactions.length);
                    let totalPaymentAmount = 0;
                    paymentTransactions.map((paymentTransaction)=>{
                        totalPaymentAmount += paymentTransaction.amount;
                    })
                    if (paymentTransactions.length === 0 && paymentsRep.length > 0) {
                        console.log('There are no payment transactions for this payment', ' but there are payments done');
                        paymentsRep.map((payment) => {
                            if (payment.isPaid && payment.status === "success") {
                                totalPaymentAmount += payment.total;
                            }
                        })

                    }

                    if (associatePayments?.paymentsArray[index] && associatePayments?.paymentsArray[index].fee !== undefined) {
                        if (totalPaymentAmount >= payment.total + fee) {
                            paymentStatus = PaymentStatus.PAID;
                        }
                    } else {
                        if (totalPaymentAmount >= payment.total) {
                            paymentStatus = PaymentStatus.PAID;
                        }
                    }
                    // console.log('totalPaymentAmount', totalPaymentAmount);

                    payments.push(payment);
                }
            }
            console.log('---------------------------------------------------')

        }
            
        amountOutstanding -= weeklyPayment;
        const dueDate = startDate;
        const termNo = duration.number;
        const streamNo = `${mainContract.contractNumber}-${duration.number}`;
        const contractNumber = mainContract.contractNumber;
        const amount = weeklyPayment;
        const totalContactValue: number = mainContract.totalPrice;
        const assignmentDate = new Date(mainContract.createdAt);
        response.push({ termNo, streamNo, contractNumber, associateId, amount, dueDate, lateFeeApplied, lateFeePaid, paymentStatus, totalContactValue, amountOutstanding, duration, payments, loanType, assignmentDate, fee });
    }
    return {data:response};
  }
  
}

