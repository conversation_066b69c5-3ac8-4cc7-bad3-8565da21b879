import { Module } from '@nestjs/common';
import { PaymentsModule } from '../payments/payments.module';
import { SchedulesController } from './controllers/schedules.controller';
import { SchedulesService } from './services/schedules.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PaymentsEntity } from '../payments/entities/payment.entity';


@Module({
  imports: [
    PaymentsModule,
    TypeOrmModule.forFeature([
      PaymentsEntity,
    ]),
  ],
  controllers: [SchedulesController],
  providers: [SchedulesService],
  exports: [SchedulesService],
})

export class SchedulesModule { }
