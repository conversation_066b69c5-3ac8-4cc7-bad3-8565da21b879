import { ClientEntity } from '@/app/clients/entities/client.entity';
import { Entity, Column, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';

@Entity({ name: 'notification_tokens' })
export class NotificationToken {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @ManyToOne(() => ClientEntity, (client) => client.notificationTokens)
  client: ClientEntity;

  @Column()
  device_type: string;

  @Column()
  notification_token: string;

  @Column({
    default: 'ACTIVE',
  })
  status: string;
}