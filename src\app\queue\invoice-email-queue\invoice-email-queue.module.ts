import { BullModule } from '@nestjs/bullmq';
import { Modu<PERSON> } from '@nestjs/common';
import { InvoiceEmailQueueService } from './invoice-email-queue.service';
import { InvoiceEmailProcessor } from './invoice-email-queue.processor';
import { BullMQAdapter } from '@bull-board/api/bullMQAdapter';
import { BullBoardModule } from '@bull-board/nestjs';

const INVOICE_EMAIL_QUEUE = 'invoice-email-queue';

@Module({
  imports: [
    BullModule.registerQueue({
      name: INVOICE_EMAIL_QUEUE,
    }),
    BullBoardModule.forFeature({
      name: INVOICE_EMAIL_QUEUE,
      adapter: BullMQAdapter,
    }),
  ],
  providers: [InvoiceEmailQueueService, InvoiceEmailProcessor],
  exports: [InvoiceEmailQueueService],
})
export class InvoiceEmailQueueModule {}
