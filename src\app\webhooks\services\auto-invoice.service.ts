import { InjectRepository } from "@nestjs/typeorm";
import { HttpException, Injectable } from '@nestjs/common';
import { PaymentsEntity } from '@/app/payments/entities/payment.entity';
// import { PaymentsService } from '@/app/payments/services/payments.service';
import { AutoInvoiceEventData } from '../types/auto-invoice.types';
import facturapi from '@/facturapi';
import { Repository } from 'typeorm';
import { ClientEntity } from '@/app/clients/entities/client.entity';
import { AutoInvoiceEntity } from '../models/auto-invoice.entity';
import { slackApi } from '@/logger.config';

@Injectable()
export class AutoInvoiceService {

  constructor(
    @InjectRepository(PaymentsEntity) private readonly paymentsRepository: Repository<PaymentsEntity>,
    @InjectRepository(ClientEntity) private readonly clientsRepository: Repository<ClientEntity>,
    @InjectRepository(AutoInvoiceEntity) private readonly autoInvoiceRepository: Repository<AutoInvoiceEntity>,
  ) { }

  async handleAutoInvoiceWebhook(body: AutoInvoiceEventData) {

    console.log('Incoming auto invoice webhook', body);

    // Validación para evitar generación de factura y recibo si el tipo de pago es "sold"
    const isASoldPaymetn = await this.paymentsRepository.findOne({
      where: {
        receiptId: body.data.object.id,
      },
      select: {
        id: true,
        type: true,
      }
    });

    if (isASoldPaymetn?.type === 'sold') {
      return {
        message: 'Process completed',
      }
    }
    let result: AutoInvoiceEntity | null = null;
    try {

      //Fix save
      const objCreate = {
        ...body,
        requestId: body.id,
      }
      if ("id" in objCreate) {
        delete objCreate.id; // delete id if exists, this makes the save to work correctly, because the id is auto generated and should be UUID, and the incoming id is a different string
      }
      const created = this.autoInvoiceRepository.create(objCreate);

      result = await this.autoInvoiceRepository.save(created);

    } catch (error: any) {
      // notifiy the error
      try {
        await slackApi.chat.postMessage({
          text: `Error saving auto invoice webhook: ${error.message}`,
          channel: process.env.AUTO_INVOICE_CHANNEL!,
        })
      } catch (error) {
        // don't do anything
      }
    }

    // console.log('AutoInvoiceWebhookController -> handleWebhook -> body', body)
    /* EXAMPLE BODY REQUEST
      body: {
        livemode: false,
        organization: '627957b8edca2800264090a0',
        type: 'receipt.self_invoice_complete',
        data: {
          type: 'receipt',
          object: {
            api_version: 2,
            livemode: false,
            folio_number: 5,
            branch: '',
            payment_form: '03',
            items: [Array],
            currency: 'MXN',
            exchange: 1,
            total: 3450,
            invoice: '66354a4f694226071c26a224',
            expires_at: '2024-05-11T05:59:59.999Z',
            key: 'E5gLZW3n',
            status: 'invoiced_to_customer',
            created_at: '2024-05-03T17:59:43.655Z',
            date: '2024-05-03T17:59:43.655Z',
            id: '6635260f0c72c0cc61b4970f',
            self_invoice_url: 'https://factura.space/demo/E5gLZW3n'
          }
        },
        created_at: '2024-05-03T20:34:23.929Z',
        id: '66354a4f694226071c26a23c'
      }
    */
    const { data } = body;

    const invoiceId = data.object.invoice;
    const receiptId = data.object.id

    const text = `Se ha generado una nueva factura con el folio ${invoiceId} y recibo ${receiptId}. Puedes ver la factura en ${data.object.self_invoice_url}`

    const text2 = `El registro en la base de datos es: ${result?.id}`
    try {
      await slackApi.chat.postMessage({
        // text,
        blocks: [
          {
            type: "section",
            text: {
              type: "mrkdwn",
              text,
            }
          },
          {
            type: "section",
            text: {
              type: "mrkdwn",
              text: text2,
            }
          }
        ],
        channel: process.env.AUTO_INVOICE_CHANNEL!,
      })
    } catch (error) {
      // don't do anything
    }


    const paymentFound = await this.paymentsRepository.findOne({
      where: {
        receiptId
      },
      relations: ['client'],
      select: {
        id: true,
        client: {
          facturapiId: true,
          id: true,
        }
      }
    });

    if (!paymentFound) {
      // throw new HttpException('Payment not found', 404);
      // Avoid throwing an error, the webhook should not be affected by this
      return {
        message: 'Process completed',
      }

    }
    const invoiceData = await facturapi.invoices.retrieve(invoiceId);

    const customerId = invoiceData.customer.id;

    const isSameId = paymentFound.client.facturapiId === customerId;

    /* const customer = */ await facturapi.customers.retrieve(customerId);

    if (!isSameId) await this.clientsRepository.update(paymentFound.client.id, {
      facturapiId: customerId,
    });

    await this.paymentsRepository.update(paymentFound.id, {
      invoiceId,
    });

    if (result) {
      try {
        await this.autoInvoiceRepository.update(result.id, {
          process_completed: true,
        });
      } catch (error) {
        // don't do anything, avoid crashing the webhook
      }
    }

  }
}
