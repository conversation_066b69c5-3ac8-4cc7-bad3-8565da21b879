import { OnWorkerEvent, Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { sendPaymentWAMessageCronMX } from '@/hilos-whatsapp/payment-message';
import { WHATSAPP_QUEUE } from '@/constants';

@Processor({
  name: WHATSAPP_QUEUE,
})
export class WhatsappProcessor extends WorkerHost {
  private readonly logger = new Logger(WhatsappProcessor.name);

  async process(job: Job<any, any, string>): Promise<any> {
    const payment = job.data;
    // send Whatsapp here
    this.logger.log({
      message: `[WhatsappProcessor] - Whatsapp is being processed: ${job.data}`,
    });
    await sendPaymentWAMessageCronMX(
      payment.client.name,
      payment.id,
      payment.client.phone,
    );
  }

  @OnWorkerEvent('active')
  onActive(job: Job) {
    this.logger.log(`[WhatsappProcessor] - Active ${job.id} ${job.data}`);
  }

  @OnWorkerEvent('completed')
  onCompleted(job: Job) {
    this.logger.log(`[WhatsappProcessor] - Completed ${job.id} ${job.data}`);
  }

  @OnWorkerEvent('failed')
  onFailed(job: Job) {
    this.logger.log(`[WhatsappProcessor] - Failed ${job.id} ${job.data}`);
  }
}
