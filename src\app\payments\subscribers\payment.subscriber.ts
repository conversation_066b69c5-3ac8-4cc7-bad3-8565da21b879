import {
  Connection,
  EntitySubscriberInterface,
  Repository,
  UpdateEvent,
} from 'typeorm';
import { PaymentsEntity } from '../entities/payment.entity';
import { Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { UnlockPaymentsService } from '../services/unlock-payments.service';

export class PaymentSubscriber
  implements EntitySubscriberInterface<PaymentsEntity>
{
  private readonly logger = new Logger(PaymentSubscriber.name);

  constructor(
    @InjectRepository(PaymentsEntity)
    private readonly paymentsRepository: Repository<PaymentsEntity>,
    private readonly unlockPaymentsService: UnlockPaymentsService,
    private readonly connection: Connection,
  ) {}

  onModuleInit() {
    // 👇 manually register subscriber
    if (!this.connection.subscribers.includes(this)) {
      this.connection.subscribers.push(this);
      this.logger.log('[PaymentSubscriber] onModuleInit - registered with TypeORM');
    }
  }

  listenTo() {
    return PaymentsEntity;
  }

  async afterUpdate(event: UpdateEvent<PaymentsEntity>): Promise<void> {
    const prev = event.databaseEntity;
    const curr = event.entity;

    if (!prev || !curr) return;

    if (!prev.isPaid && curr.isPaid) {
      this.logger.log(
        '[PaymentSubscriber] afterUpdate - send payment for verification',
        curr.id,
      );
      this.unlockPaymentsService
        .verifyPayment(curr.id)
        .catch((err) => this.logger.error('[PaymentSubscriber] afterUpdate - error in verifyPayment:', err));
    }
  }
}
