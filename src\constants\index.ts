import { join } from 'path'
// import 'dotenv/config'
// import { config } from 'dotenv'
import * as dotenv from 'dotenv'
dotenv.config()

dotenv.config({ path: join(__dirname, `../../env/.env.${process.env.NODE_ENV || 'development'}`) })

export const PORT = process.env.PORT || 4000
export const NODE_ENV = process.env.NODE_ENV || 'development'

export const HILOS_API = process.env.HILOS_API;
export const HILOS_API_KEY = process.env.HILOS_API_KEY;
export const PAYMENT_TEMPLATE_ID = process.env.PAYMENT_TEMPLATE_ID || process.env.PATMENT_TEMPLATE_ID;
export const CHARGE_100_TEMPLATE_ID = process.env.CHARGE_100_TEMPLATE_ID;
export const UNLOCK_PAYMENT_TEMPLATE_ID = '067f83a1-bdd8-75be-8000-fcd6a952148c';
export const REACTIVATION_PAYMENT_CONFIRMATION_TEMPLATE_ID = '067f83b6-b351-7aa5-8000-5e2a5a819d78';
export const VEHICLE_UNLOCK_CONFIRMATION_TEMPLATE_ID = '067f83c5-68da-7dfe-8000-e0e823b60d97';
export const UNLOCK_PAYMENT_REMINDER_TUESDAY_TEMPLATE_ID = '0681117a-305a-7e91-8000-5e319a1029af';
export const UNLOCK_PAYMENT_REMINDER_WEDNESDAY_TEMPLATE_ID = '068091d2-a659-7298-8000-299d31df738c';

/* redis constants */
export const REDIS_URL = process.env.REDIS_URL;
export const REDIS_HOST = process.env.REDIS_HOST;
export const REDIS_PORT = process.env.REDIS_PORT;

/*  queue constants */
export const QUEUE_REMOVE_ON_COMPLETE=process.env.QUEUE_REMOVE_ON_COMPLETE;
export const QUEUE_REMOVE_ON_FAIL=process.env.QUEUE_REMOVE_ON_FAIL;
export const QUEUE_ATTEMPTS=process.env.QUEUE_ATTEMPTS;
export const QUEUE_BACKOFF_TYPE=process.env.QUEUE_BACKOFF_TYPE;
export const QUEUE_BACKOFF_DELAY=process.env.QUEUE_BACKOFF_DELAY;
export const EMAIL_QUEUE = 'email_queue';
export const JOB_SEND_EMAIL = 'sendEmail';
export const NOTIFICATION_QUEUE = 'notification_queue';
export const JOB_SEND_NOTIFICATION = 'sendNotification';
export const PAYMENT_QUEUE = 'payment_queue';
export const JOB_CREATE_PAYMENT = 'createPayment';
export const WHATSAPP_QUEUE = 'whatsapp_queue';
export const JOB_SEND_WHATSAPP = 'sendWhatsapp';

/* gigstack */
export const TEST_TOKEN = process.env.TEST_TOKEN;
export const CDMX_TOKEN = process.env.CDMX_GIGSTACK_TOKEN;
export const GDL_TOKEN = process.env.GDL_GIGSTACK_TOKEN;
export const QRO_TOKEN = process.env.QRO_GIGSTACK_TOKEN;
export const TIJ_TOKEN = process.env.TIJ_GIGSTACK_TOKEN;
export const MTY_TOKEN = process.env.MTY_GIGSTACK_TOKEN;
export const PBC_TOKEN = process.env.PBC_GIGSTACK_TOKEN;


export const wire4Data = {
  token: {
    scope: 'spei_admin device_local',
    type: 'AccesToken',
  },
  transfers: {
    response: {
      spei: {
        incomming: 'spei_incoming',
      },
    },
  },
  bankAccount: {
    currency_code: 'MXP',
  },
};

/* FACTURAPI */

export const FACTURAPI_URL = 'https://www.facturapi.io/v2'

export const FACTURAPI_SECRET_KEY = process.env.FACTURAPI_SECRET_KEY;


// PAYMENT FRONT URL

export const PAYMENT_FRONT_URL = process.env.PAYMENT_FRONT_URL || 'https://pagos.onecarnow.com';

export const PAYMENTS_CHANNEL = process.env.PAYMENTS_CHANNEL;
export const PAYMENTS_ERROR_CHANNEL = process.env.PAYMENTS_ERROR_CHANNEL;

//GPS Service
export const GPS_API_TOKEN = 'https://ef5zlqp9rl.execute-api.us-east-1.amazonaws.com/prod/getToken';
export const GPS_API_URL = 'https://jjmwrxb6w3.execute-api.us-east-1.amazonaws.com/prod/prod';
export const GPS_API_PASSWORD = '20oneCar24@';
export const GPS_API_USERNAME = 'mcangas';
export const GPS_API_EVENTS = 'https://vmmqnvrmj3.execute-api.us-east-1.amazonaws.com/production/set-events';