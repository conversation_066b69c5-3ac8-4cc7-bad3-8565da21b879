import {
  HttpException,
  HttpStatus,
  Injectable,
  InternalServerErrorException,
  Logger,
  RawBodyRequest,
} from '@nestjs/common';
import Stripe from 'stripe';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { StripeCustomerInfoEntity } from './entities/stripe-customer.entity';
import { Repository } from 'typeorm';
import { ClientEntity } from '../clients/entities/client.entity';
import {
  SetupIntentStatus,
  StripeSetupIntentEntity,
} from './entities/stripe-setup-intent.entity';
import {
  PaymentCurrency,
  PaymentIntentStatus,
  StripePaymentTransactionEntity,
} from './entities/stripe-payment-transaction.entity';
import { Request } from 'express';
import {
  PaymentStatusEnum,
  PaymentStatusType,
  PaymentsEntity,
} from '../payments/entities/payment.entity';
import stripeReceiptLinkAfterPaymentEmail from '@/providers/nodemailer/templates/paymentReceiptLinkStripe.template';

@Injectable()
export class StripePaymentService {
  private readonly stripe: Stripe;
  private readonly logger = new Logger(StripePaymentService.name);

  constructor(
    private readonly configService: ConfigService,

    @InjectRepository(StripeCustomerInfoEntity)
    private readonly stripeCustomerRepository: Repository<StripeCustomerInfoEntity>,

    @InjectRepository(StripeSetupIntentEntity)
    private readonly stripeSetupIntentRepository: Repository<StripeSetupIntentEntity>,

    @InjectRepository(StripePaymentTransactionEntity)
    private readonly stripePaymentTransactionRepository: Repository<StripePaymentTransactionEntity>,

    /**
     * Directly importing entity repository because there is some issue with PaymentModule,
     * it can't be imported in other modules due to some circular dependency issue.
     */
    @InjectRepository(PaymentsEntity)
    private readonly paymentRepository: Repository<PaymentsEntity>,
  ) {
    this.stripe = new Stripe(this.configService.get('STRIPE_SECRET_KEY'));
  }

  async setupIntentStatus(stripeSetupIntentStatusDto: any) {
    stripeSetupIntentStatusDto = JSON.parse(stripeSetupIntentStatusDto);

    const { clientId, setupIntentId, intentStatus } =
      stripeSetupIntentStatusDto;

    try {
      const setupIntent = await this.stripeSetupIntentRepository.findOne({
        where: {
          stripeCustomerInfo: {
            client: {
              id: clientId,
            },
          },
          stripeSetupIntentId: setupIntentId,
        },
        relations: {
          stripeCustomerInfo: true,
        },
        select: {
          stripeCustomerInfo: {
            stripeCustomerId: true,
          },
        },
      });

      if (!setupIntent) {
        return {
          message: 'Invalid client Id or setup intent Id',
          data: null,
        };
      } else if (setupIntent.status === SetupIntentStatus.SUCCEEDED) {
        return {
          message: 'Setup intent already succeeded',
          data: {
            status: setupIntent.status,
            clientId: clientId,
          },
        };
      } else if (setupIntent.status === SetupIntentStatus.FAILED) {
        /**
         * need to handle setup intent status failed case as well
         */
        return {
          message: 'Setup Intent is in failed state, Please contact support',
          data: null,
        };
      }

      await this.stripeSetupIntentRepository.update(
        { id: setupIntent.id },
        { status: SetupIntentStatus.SUCCEEDED },
      );

      await this.stripeCustomerRepository.update(
        { client: { id: clientId } },
        {
          isCustomerBankAccountLinked: true,
        },
      );

      //  setting customer default payment method on stripe
      const stripeCustomerId = setupIntent.stripeCustomerInfo.stripeCustomerId;
      const stripeSetupIntentId = setupIntent.stripeSetupIntentId;
      await this.setDefaultPaymentMethodForStripeCustomer(
        stripeCustomerId,
        stripeSetupIntentId,
      );

      const updatedStripeSetupIntent =
        await this.stripeSetupIntentRepository.findOne({
          where: {
            stripeCustomerInfo: {
              client: {
                id: clientId,
              },
            },
            stripeSetupIntentId: setupIntentId,
          },
        });

      return {
        message: 'Setup intent status updated successfully',
        data: {
          status: updatedStripeSetupIntent.status,
          clientId: clientId,
        },
      };
    } catch (err) {
      this.logger.error(
        `Error occured while updating stripe setup intent status: ${err} for client ${stripeSetupIntentStatusDto.clientId}`,
      );
      throw new InternalServerErrorException(
        `Error occured while updating stripe setup intent status`,
      );
    }
  }

  async getSetupIntentSecret(clientId: string) {
    try {
      const localStripeCustomerInfo =
        await this.stripeCustomerRepository.findOne({
          where: { client: { id: clientId } },
          relations: {
            client: true,
          },
          select: {
            client: {
              email: true,
              name: true,
              lastName: true,
            },
          },
        });
      if (!localStripeCustomerInfo) {
        return {
          message: 'Invalid client id',
          data: null,
        };
      }

      if (localStripeCustomerInfo.isCustomerBankAccountLinked) {
        return {
          message: 'You Bank Account has already been linked with Us.',
          data: {
            isCustomerBankAccountLinked:
              localStripeCustomerInfo.isCustomerBankAccountLinked,
          },
        };
      }

      const setupIntentSecret = await this.createStripeSetupIntent(
        localStripeCustomerInfo.stripeCustomerId,
      );

      this.logger.log(
        `Setup intent secret created successfully with id ${setupIntentSecret.id}`,
      );

      try {
        await this.stripeSetupIntentRepository.insert({
          stripeCustomerInfo: localStripeCustomerInfo,
          stripeSetupIntentObj: setupIntentSecret,
          stripeSetupIntentId: setupIntentSecret.id,
        });
      } catch (err) {
        this.logger.error(
          `Error occured while storing stripe setup intent locally: ${err}`,
        );
        throw err;
      }

      return {
        data: {
          clientSecret: setupIntentSecret.client_secret,
          email: localStripeCustomerInfo.client.email,
          fullName:
            localStripeCustomerInfo.client.name +
            ' ' +
            localStripeCustomerInfo.client.lastName,
        },
        message: 'Successfully generated setup intent',
      };
    } catch (err) {
      this.logger.error(
        `Error occured while creating stripe setup intent: ${err}`,
        err,
      );
      throw new InternalServerErrorException(
        `Error occured while creating stripe setup intent`,
      );
    }
  }

  async createStripeCustomer(client: ClientEntity) {
    try {
      const stripeCustomer = await this.stripe.customers.create({
        email: client.email,
        name: client.name,
        description: 'OCN US Texas Dallas Customer',
      });
      this.logger.log(
        `Successfully registered customer on stripe with id ${stripeCustomer.id}`,
      );

      const _stripeCustomer = await this.stripeCustomerRepository.insert({
        stripeCustomerId: stripeCustomer.id,
        client: client,
      });

      this.logger.log(
        `Successfully created customer locally with id ${_stripeCustomer.identifiers[0].id}`,
      );
    } catch (err) {
      this.logger.error(
        `Error occured while registering customer on stripe or creating stripe customer locally: ${err}`,
      );
      throw new InternalServerErrorException(
        'Error occured while creating stripe customer:',
      );
    }
  }

  async stripePaymentIntentWebhook(req: RawBodyRequest<Request>) {
    // Retrieve the event by verifying the signature using the raw body and secret.
    let event: Stripe.Event;
    try {
      event = this.stripe.webhooks.constructEvent(
        req.rawBody,
        req.headers['stripe-signature'],
        this.configService.get('STRIPE_PAYMENT_WEBHOOK_SECRET'),
      );
      this.logger.log(
        `Stripe Webhook event successfully constructed: ${event}`,
      );
    } catch (err) {
      this.logger.error(`Stripe Webhook signature verification failed.`);
      throw new HttpException(
        'Stripe Webhook signature verification failed.',
        HttpStatus.BAD_REQUEST,
      );
    }

    const eventType = event.type;
    if (eventType === 'payment_intent.succeeded') {
      await this.handleStripePaymentIntentSucceeded(event);
    } else if (eventType === 'payment_intent.payment_failed') {
      await this.handleStripePaymentIntentFailed(event);
    }
    return {
      message: 'Successfully processed Stripe Webhook event',
      data: {},
    };
  }

  private async handleStripePaymentIntentSucceeded(
    paymentIntentSuccessEvent: Stripe.PaymentIntentSucceededEvent,
  ) {
    try {
      const paymentIntent = paymentIntentSuccessEvent.data.object;
      this.logger.log(
        `Stripe PaymentTransaction was successful with id ${paymentIntent.id}`,
      );
      const paymentIntentTransaction = {
        stripePaymentTransactionStatus: PaymentIntentStatus.SUCCEEDED,
        stripePaymentIntentObj: {
          [PaymentIntentStatus.SUCCEEDED]: paymentIntent,
        },
        isPaid: true,
        paymentStatus: PaymentStatusEnum.SUCCESS,
      };

      const stripePaymentTransaction =
        await this.updatePaymentAndStripePaymentTransactionStatus(
          paymentIntent.id,
          paymentIntentTransaction,
        );

      try {
        const payment = await this.paymentRepository.findOne({
          where: {
            id: stripePaymentTransaction.payment.id,
          },
          relations: ['client'],
          select: {
            client: {
              id: true,
              email: true,
              name: true,
            },
          },
        });
        await stripeReceiptLinkAfterPaymentEmail({
          name: payment.client.name,
          email: payment.client.email,
          paymentId: payment.id,
        });
      } catch (err) {
        this.logger.error(
          'Error occurred while sending email to client for stripe payment receipt',
          err,
        );
      }
    } catch (err: any) {
      this.logger.error(
        `Error occured while processing stripe payment intent succeeded: ${err}`,
        err.stack,
      );
    }
  }

  private async handleStripePaymentIntentFailed(
    paymentIntentFailedEvent: Stripe.PaymentIntentPaymentFailedEvent,
  ) {
    const paymentIntent = paymentIntentFailedEvent.data.object;
    try {
      this.logger.log(
        `Stripe PaymentTransaction was failed with id ${paymentIntent.id}`,
      );
      const paymentIntentTransaction = {
        stripePaymentTransactionStatus: PaymentIntentStatus.FAILED,
        stripePaymentIntentObj: {
          [PaymentIntentStatus.FAILED]: paymentIntent,
        },
        isPaid: false,
        paymentStatus: PaymentStatusEnum.FAILED,
      };
      await this.updatePaymentAndStripePaymentTransactionStatus(
        paymentIntent.id,
        paymentIntentTransaction,
      );
    } catch (err: any) {
      this.logger.error(
        `Error occured while processing stripe payment intent failed: ${err}`,
        err.stack,
      );
      throw new InternalServerErrorException(
        `Error occured while processing stripe payment intent failed.`,
      );
    }
  }

  async updatePaymentAndStripePaymentTransactionStatus(
    stripePaymentIntentId: string,
    stripePaymentObj: {
      stripePaymentTransactionStatus: PaymentIntentStatus;
      stripePaymentIntentObj: Record<string, any>;
      isPaid: boolean;
      paymentStatus: PaymentStatusType;
    },
  ) {
    try {
      const {
        stripePaymentTransactionStatus,
        stripePaymentIntentObj,
        paymentStatus,
        isPaid,
      } = stripePaymentObj;

      const paymentIntentTransaction =
        await this.stripePaymentTransactionRepository.findOne({
          where: {
            stripePaymentIntentId: stripePaymentIntentId,
          },
          relations: {
            payment: true,
          },
          select: {
            payment: {
              id: true,
            },
          },
        });

      paymentIntentTransaction.status = stripePaymentTransactionStatus;
      Object.assign(
        paymentIntentTransaction.stripePaymentIntentObj,
        stripePaymentIntentObj,
      );
      await this.stripePaymentTransactionRepository.update(
        { stripePaymentIntentId: stripePaymentIntentId },
        paymentIntentTransaction,
      );

      const isPaymentSuccessfullyDebited =
        paymentStatus === PaymentStatusEnum.SUCCESS;
      await this.updatePayment(paymentIntentTransaction.payment.id, {
        isPaid: isPaid,
        status: paymentStatus,
        paidAt: isPaymentSuccessfullyDebited ? new Date() : null,
      });

      this.logger.log(
        `Stripe payment transaction status updated successfully with id ${stripePaymentIntentId}`,
      );

      return paymentIntentTransaction;
    } catch (err: any) {
      this.logger.error(
        `Error occured while updating stripe payment transaction status: ${err}`,
        err.stack,
      );
      throw new InternalServerErrorException(
        `Error occured while updating stripe payment transaction status.`,
      );
    }
  }

  async setDefaultPaymentMethodForStripeCustomer(
    stripeCustomerId: string,
    setupIntentId: string,
  ) {
    try {
      const setupIntent = await this.stripe.setupIntents.retrieve(
        setupIntentId,
      );
      const paymentMethod = setupIntent.payment_method;
      const customer = await this.stripe.customers.update(stripeCustomerId, {
        invoice_settings: {
          default_payment_method: paymentMethod as string,
        },
      });

      this.logger.log(
        `Stripe customer payment method updated successfully with id ${customer.id}`,
      );
    } catch (err) {
      this.logger.error(
        `Error occured while updating stripe customer payment method: ${err}`,
      );
      throw new InternalServerErrorException(
        `Error occured while updating stripe customer payment method.`,
      );
    }
  }

  async retrieveStripeCustomer(stripeCustomerId: string) {
    try {
      const stripeCustomer = await this.stripe.customers.retrieve(
        stripeCustomerId,
      );
      this.logger.log(
        `Stripe customer retrieved successfully with id ${stripeCustomer.id}`,
      );
      return stripeCustomer;
    } catch (err) {
      this.logger.error(
        `Error occured while retrieveing stripe customer: ${err}`,
      );
      throw new InternalServerErrorException(
        `Error occured while retrieveing stripe customer.`,
      );
    }
  }

  async createStripePaymentTransaction(paymentMetadata: Record<string, any>) {
    try {
      const { clientId, amountInUSDollars, paymentId } = paymentMetadata;

      const stripeCustomer = await this.stripeCustomerRepository.findOne({
        where: {
          client: {
            id: clientId,
          },
        },
      });

      if (!stripeCustomer.isCustomerBankAccountLinked) {
        this.logger.error(
          `Bank account not linked to stripe of client with id ${clientId}`,
        );
        throw new HttpException(
          'Client bank account is not linked to Stripe',
          400,
        );
      }
      this.logger.log(
        `Bank account is linked to stripe of client with id ${clientId}`,
      );
      const stripeCustomerId = stripeCustomer.stripeCustomerId;
      const stripeSetupIntent = await this.stripeSetupIntentRepository.findOne({
        where: {
          stripeCustomerInfo: {
            stripeCustomerId: stripeCustomer.stripeCustomerId,
          },
          status: SetupIntentStatus.SUCCEEDED,
        },
      });

      if (!stripeSetupIntent) {
        this.logger.log(
          `Stripe Setup Intent not found for StripeCustomerId = ${stripeCustomerId}. Please link your bank account first`,
        );
        throw new InternalServerErrorException(
          `Stripe Setup Intent not found for StripeCustomerId = ${stripeCustomerId}. Please link your bank account first`,
        );
      }

      const stripePaymentTransaction =
        await this.stripePaymentTransactionRepository.save({
          payment: paymentId,
          amount: amountInUSDollars,
          currency: PaymentCurrency.USD,
          status: PaymentIntentStatus.PENDING,
        });

      this.logger.log(
        `Stripe payment transaction created successfully with id ${stripePaymentTransaction.id}`,
      );

      const setupIntent = await this.stripe.setupIntents.retrieve(
        stripeSetupIntent.stripeSetupIntentId,
      );
      const paymentIntent = await this.debitClientUsingStripe(
        stripeCustomerId,
        amountInUSDollars,
        setupIntent.payment_method as string,
        PaymentCurrency.USD.toLowerCase(),
      );

      await this.stripePaymentTransactionRepository.update(
        stripePaymentTransaction.id,
        {
          stripePaymentIntentId: paymentIntent.id,
          stripePaymentIntentObj: {
            [PaymentIntentStatus.PENDING]: paymentIntent,
          },
        },
      );
    } catch (err) {
      this.logger.error(
        `Error occured while creating stripe payment transaction: ${err}`,
      );
      throw new InternalServerErrorException(
        `Error occured while creating stripe payment transaction.`,
      );
    }
  }

  /**
   * This method is used to debit the client using stripe
   * @param stripeCustomerId: string
   * @param amount: number (amount in USD Dollars)
   */
  async debitClientUsingStripe(
    stripeCustomerId: string,
    amountInUSDollars: number,
    paymentMethodId: string,
    currency: string = 'usd',
  ) {
    try {
      const amountInCents = amountInUSDollars * 100;
      const paymentIntent = await this.stripe.paymentIntents.create({
        amount: amountInCents, // Amount in cents (e.g., $50.00)
        currency: currency,
        customer: stripeCustomerId,
        payment_method: paymentMethodId,
        payment_method_types: ['us_bank_account'],
        confirm: true, // Automatically confirm the payment
      });

      this.logger.log(
        `Stripe customer debited successfully with id ${paymentIntent.id}`,
      );

      return paymentIntent;
    } catch (err) {
      this.logger.error(`Error occured while debiting stripe customer: ${err}`);
      throw new InternalServerErrorException(
        `Error occured while debiting stripe customer.`,
      );
    }
  }

  private async createStripeSetupIntent(customerId: string) {
    try {
      const stripeSetupIntent = await this.stripe.setupIntents.create({
        customer: customerId,
        payment_method_types: ['us_bank_account'],
        payment_method_options: {
          us_bank_account: {
            financial_connections: {
              permissions: ['payment_method', 'balances'],
            },
          },
        },
      });

      this.logger.log(
        `Stripe setup intent created successfully with id ${stripeSetupIntent.id} for customer ${customerId}`,
      );

      return stripeSetupIntent;
    } catch (err) {
      this.logger.error(
        `Error occured while creating stripe setup intent: ${err}`,
      );
      throw new InternalServerErrorException(
        `Error occured while creating stripe setup intent.`,
      );
    }
  }

  async retrieveStripeSetupIntent(id: string) {
    try {
      const stripeSetupIntent = await this.stripe.setupIntents.retrieve(id);
      this.logger.log(
        `Stripe setup intent retrieved successfully with id ${stripeSetupIntent.id}`,
      );

      return stripeSetupIntent;
    } catch (err) {
      this.logger.error(
        `Error occured while retrieveing stripe setup intent: ${err}`,
      );
      throw new InternalServerErrorException(
        `Error occured while retrieveing stripe setup intent.`,
      );
    }
  }

  private async updatePayment(
    paymentId: string,
    paymentUpdateDto: Partial<PaymentsEntity>,
  ) {
    try {
      await this.paymentRepository.update(paymentId, paymentUpdateDto);
      this.logger.log(`Payment updated successfully with id ${paymentId}`);
    } catch (error: any) {
      this.logger.error(
        'Error occured while updating payment status',
        error.stack,
      );
      throw new InternalServerErrorException(
        'Error occured while updating payment status',
      );
    }
  }
}
