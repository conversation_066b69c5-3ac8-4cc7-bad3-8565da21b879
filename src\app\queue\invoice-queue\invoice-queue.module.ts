import { BullModule } from '@nestjs/bullmq';
import { Module } from '@nestjs/common';
import { InvoiceQueueService } from './invoice-queue.service';
import { InvoiceProcessor } from './invoice-queue.processor';
import { BullMQAdapter } from '@bull-board/api/bullMQAdapter';
import { BullBoardModule } from '@bull-board/nestjs';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PaymentsEntity } from '@/app/payments/entities/payment.entity';
import { InvoiceEmailQueueModule } from '../invoice-email-queue/invoice-email-queue.module';
import { INVOICE_QUEUE } from '@/constants/queues';

@Module({
  imports: [
    BullModule.registerQueue({
      name: INVOICE_QUEUE,
    }),
    BullBoardModule.forFeature({
      name: INVOICE_QUEUE,
      adapter: BullMQAdapter,
    }),
    TypeOrmModule.forFeature([PaymentsEntity]),
    InvoiceEmailQueueModule,
  ],
  providers: [InvoiceQueueService, InvoiceProcessor],
  exports: [InvoiceQueueService],
})
export class InvoiceQueueModule { }

