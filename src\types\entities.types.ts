export enum RegionsEnum {
  CDMX = 'CDMX',
  GDL = 'GDL',
  MTY = 'MTY',
  TIJ = 'TIJ',
  QRO = 'QRO',
  TEST = 'TEST',
  PBC = 'PBC',
  PBE = 'PBE',
  TOL = 'TOL',
  PTV = 'PTV',
  TEP = 'TEP',
  COL = 'COL',
  SAL = 'SAL',
  TORR = 'TORR',
  DUR = 'DUR',
  MXLI = 'MXLI',
  HER = 'HER',
  CHI = 'CHI',
  LEO = 'LEO',
  AGS = 'AGS',
  SLP = 'SLP',
  MER = 'MER',

  // us states
  FLORIDA = 'FLORIDA', // do not use this, use MIAMI or other US states instead
  MIAMI = 'MIA', // do not use this, use below City names, not commenting this because of no db migrations.
  DALLAS = 'DAL',
}



export type RegionsType = keyof typeof RegionsEnum;