import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, ManyToOne } from 'typeorm';
import { WeeklyTracking } from './weekly-tracking.entity';

@Entity({ name: 'weekly_records' })
export class WeeklyRecord {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => WeeklyTracking, tracking => tracking.records)
  tracking: WeeklyTracking;

  @Column({ type: "text", })
  clientId: string;

  @Column({ type: "text", })
  subscriptionId: string;

  @Column({ type: "text", default: 'pending' })  // 'pending', 'created', 'failed'
  status: string;

  @Column({ nullable: true })
  generatedLink: string;

  @Column({ nullable: true, type: "jsonb" })
  errorDetails: object;
}
