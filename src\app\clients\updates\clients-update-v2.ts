// import { Inject, Injectable, forwardRef } from '@nestjs/common';
// import { InjectRepository } from '@nestjs/typeorm';
// import { DataSource, In, IsNull, Raw, Repository } from 'typeorm';
// import { ClientEntity } from '../entities/client.entity';
// import { RegionsType } from '@/types/entities.types';
// import { tokenAssignGigstack } from '@/lib/getTokenByRegion';
// import { TEST_TOKEN } from '@/constants';
// import { ClientAPI } from '../client-types';
// import { CreateClientDto } from '../dto/create-client.dto';
// import { SubscriptionEntity } from '@/app/subscriptions/entities/subscription.entity';
// import { ProductEntity } from '@/app/products/entities/product.entity';
// import { SubscriptionProductsService } from '@/app/subscription-products/services/subscription-products.service';
// import { SubscriptionProductEntity } from '@/app/subscription-products/entities/subscription-product.entity';
// import { invertFormatDate } from '@/lib/formatDate';
// import { getLocalDate } from '@/lib/dates';
// import axios from 'axios';
// import associates from "@/associates_v2.json";
// import missing from "@/missing.json";
// // import associatesCDMX from '@/associatesCDMX.json'


// @Injectable()
// export class ClientsUpdaterV2 {
//   private readonly baseurl = "https://gigstack-cfdi-bjekv7t4.uc.gateway.dev/v1";

//   constructor(
//     @InjectRepository(ClientEntity)
//     private readonly clientRepository: Repository<ClientEntity>,
//     @InjectRepository(SubscriptionEntity) private readonly subscriptionRepository: Repository<SubscriptionEntity>,
//     @InjectRepository(ProductEntity) private readonly productRepository: Repository<ProductEntity>,
//     // private readonly subscriptionProductsService: SubscriptionProductsService,
//     @InjectRepository(SubscriptionProductEntity) private readonly subscriptionProductRepository: Repository<SubscriptionProductEntity>,
//   ) { }


//   async getMongoDBData(region: RegionsType, missingAssociatesFilter?: any[]) {

//     // const totalOfRegion = associates.filter(associate => associate.region.trim() === region).length;

//     // const associatescdmx = associates.filter(associate => associate.region.trim() === region && missing.includes(associate.contractNumber));

//     const associatesFilter = missingAssociatesFilter ? associates.filter(associate => missingAssociatesFilter.includes(associate.contractNumber) && associate.region.trim() === region) : associates.filter((associate) => associate.region.trim() === region);

//     console.log('total of same region:', associatesFilter.length);
//     // return console.log('associates', associatesFilter);
//     let count = 0;

//     let totalCreated = 0;
//     const associatesCreated = [];

//     let totalUpdated = 0;

//     const associatesUpdated = [];

//     await Promise.allSettled(associatesFilter.map(async associate => {
//       count++;

//       // if (associate.region.trim() !== region) return;
//       // if (count > 5) break;

//       if (!associate.gigId) return;
//       const clientExists = await this.clientRepository.findOne({
//         where: [
//           { gigId: associate.gigId },
//           { monexClabe: associate.monexClabe },
//           { email: associate.email },
//           { legal_name: associate.legal_name },
//           { associateId: associate._id },
//         ]
//       });

//       // if (clientExists) {
//       //   console.log('CLIENT EXISTS', clientExists.contractNumber, clientExists.email)
//       //   return;
//       // };

//       const res = await this.getGigstackUser(associate.gigId, associate.region.trim() as RegionsType);
//       const gigUser = res[0];

//       const exists = Boolean(gigUser);



//       const taxSystem = exists ? (typeof gigUser.tax_system === 'string' ? gigUser.tax_system : gigUser.tax_system?.value) : null;

//       const isActive = associate.vehicleStatus === 'active';
//       console.log('GIG USER EXISTS?', gigUser?.company)
//       console.log('client exists? ', clientExists?.contractNumber, gigUser.company);



//       const newClient: CreateClientDto & { gigId: string } = {
//         contractNumber: associate.contractNumber || gigUser.company,
//         legal_name: associate.legal_name || gigUser.legal_name,
//         email: associate.email,
//         country: 'MX',
//         rfc: associate.rfc || gigUser.rfc,
//         tax_system: taxSystem || null,
//         name: associate.name.trim(),
//         lastName: associate.lastName.trim(),
//         monexClabe: associate.monexClabe?.trim() || JSON.parse(gigUser.metadata)?.clabe,
//         region: associate.region.trim() as RegionsType,
//         phone: associate.phone.toString().trim(),
//         zip: associate.zip.toString().trim().padStart(5, '0'),
//         gigId: associate.gigId,
//         use_cfdi: gigUser.use || 'G03',
//         associateId: associate._id,
//       }

//       if (!clientExists) {
//         // console.log('CLIENT TO CREATE', clientExists.contractNumber, clientExists.email)
//         const createClient = this.clientRepository.create({ ...newClient, isActive });
//         console.log('CLIENT TO CREATE', createClient)
//         const created = await this.clientRepository.save(createClient);
//         console.log('created, ', created)
//         totalCreated++;

//         associatesUpdated.push(associate);
//         return
//       }

//       if (clientExists) {
//         //update client
//         // console.log('CLIENT TO UPDATE', clientExists.contractNumber, clientExists.email)
//         await this.clientRepository.update(clientExists.id, { ...newClient, isActive });
//         console.log('CLIENT TO UPDATE', clientExists.contractNumber, clientExists.email);
//         totalUpdated++;
//         associatesCreated.push(associate);
//         // console.log('UPDATE CLIENT', gigUser.company, clientExists.contractNumber)
//       }

//     }));

//     console.log('-------------------------------------')

//     console.log('total created:', totalCreated, 'total updated:', totalUpdated)
//     console.log('total general', totalCreated + totalUpdated)


//     const all = [...associatesCreated, ...associatesUpdated];

//     const missingAssociates = associatesFilter.filter(associate => !all.find(a => a._id === associate._id));

//     console.log('missing associates:', missingAssociates.length, missingAssociates.map(a => a.contractNumber));

//     // console.log('total associates:', associates.length)
//   }

//   async getGigstackUser(id: string, region: RegionsType) {

//     const token = tokenAssignGigstack(region);

//     const res = await fetch(`${this.baseurl}/clients/list?id=${id}`, {
//       method: "GET",
//       headers: {
//         "Content-Type": "application/json",
//         Authorization: `Bearer ${token || TEST_TOKEN}`,
//       },
//     });
//     const data = await res.json();
//     const clients: ClientAPI[] = data.clients;


//     return clients;

//   }


//   async createSubscriptionsForClientsWithoutSubscriptions(region: RegionsType, missing?: any[]) {

//     // const associateIds = associates.map(associate => associate._id);


//     // const regionAssociates = associates.filter(associate => associate.region.trim() === region && missing.includes(associate.contractNumber));

//     const regionAssociates = missing ?
//       associates.filter(associate => missing.includes(associate.contractNumber) && associate.region.trim() === region)
//       :
//       associates.filter(associate => associate.region.trim() === region);

//     console.log('total associates:', regionAssociates.length)
//     // console.log('associate', regionAssociates)
//     const notFound = [];
//     // return;
//     const errors = [];

//     const clientsWithoutSubsription = []
//     let count = 0;
//     await Promise.allSettled(regionAssociates.map(async (associate) => {

//       // if (!associate.gigId) return
//       try {
//         const client = await this.clientRepository.findOne({
//           where: {
//             associateId: associate._id,
//             // subscriptions: IsNull(),
//             region
//           },
//           relations: ['subscriptions'],
//         });

//         // const client = await this.clientRepository.createQueryBuilder('client')
//         //   .leftJoinAndSelect('client.subscriptions', 'subscription')
//         //   .where('client.associateId = :associateId', { associateId: associate._id })
//         //   .andWhere('client.region = :region', { region })
//         //   .andWhere('subscription.id IS NULL')
//         //   .getOne();

//         if (!client) {
//           console.log('client not found', associate._id)
//           notFound.push(associate)
//           return;
//         }
//         if (client.subscriptions) {
//           console.log('client already has subscription', associate._id)
//           return;
//         }
//         clientsWithoutSubsription.push(client);
//         const products = await this.fetchProductsDependingOnDownpayment(region, associate.downPayment > 0, associate);
//         // console.log('total products fetched:', associate.contractNumber, products.length, products);
//         if (products.length < 2) {
//           console.log('THIS IS NOT CORRECT', products.length);
//           return
//         }

//         const subscriptionProducts = await Promise.all(products.map(async (p) => {
//           const productEntity = await this.productRepository.findOne({
//             where: { id: p.id }
//           });

//           if (!productEntity) {
//             console.log('product not found', p.id)
//             // throw new HttpException('Product ${p.name}', 404);
//           }

//           const taxRate = productEntity.taxRate || 0.16;
//           const rate = taxRate === 1 ? 0 : taxRate;
//           const subTotal = productEntity.price * (1 - rate)

//           const tax = taxRate === 1 ? 0 : productEntity.price * rate

//           const productSubscription = {
//             ...productEntity,
//             quantity: 1,
//             hasTaxes: true,
//             subscription: null,
//             total: productEntity.price,
//             discount: 0,
//             subTotal,
//             tax,
//           }

//           delete productSubscription.id;
//           delete productSubscription.createdAt;

//           // const subscriptionProduct = await this.subscriptionProductsService.create(productSubscription);

//           const subscriptionProduct = this.subscriptionProductRepository.create(productSubscription);

//           await this.subscriptionProductRepository.save(subscriptionProduct);

//           return subscriptionProduct;
//         }));

//         const { total, subTotal, tax } = subscriptionProducts.reduce((acc, p) => {
//           acc.total += p.total;
//           acc.subTotal += p.subTotal;
//           acc.tax += p.tax;
//           return acc;
//         }, { total: 0, subTotal: 0, tax: 0 });

//         const subscriptionCreate = this.subscriptionRepository.create({
//           client,
//           region: client.region,
//           products: subscriptionProducts,
//           total,
//           subTotal,
//           isActive: client.isActive,
//           tax,
//           startDate: getLocalDate(),
//           // endDate: new Date(),
//           endDate: invertFormatDate(associate.lastPaymentDate),
//         });

//         const subscription = await this.subscriptionRepository.save(subscriptionCreate);

//         console.log('created subscription for client:', client.id, subscription.id)

//       } catch (error: any) {
//         console.log('error in the process', associate._id, error)
//         errors.push(associate);
//       }

//     }))
//     /* FINISH PROMISE */

//     console.log('total not found:', notFound.length, notFound.map(a => a.contractNumber));

//     console.log('total errors:', errors.length, errors.map(a => a.contractNumber));

//     console.log('total clients without subscription:', clientsWithoutSubsription.length, clientsWithoutSubsription.map(a => a.contractNumber));

//   }

//   async fetchProductsDependingOnDownpayment(region: RegionsType, hasDownPayment: boolean, findAssociate: typeof associates[0]) {

//     // console.log('START FETCHING PRODUCTS', findAssociate.contractNumber, hasDownPayment, findAssociate.downPayment)


//     if (hasDownPayment) {

//       const product1 = await this.productRepository.findOne({
//         where: { region, name: `Renting ${findAssociate.model}` }
//       });

//       const product2 = await this.productRepository.createQueryBuilder('producto')
//         // .select(['producto.id', 'producto.name', 'producto.region', 'producto.price'])
//         .where('producto.name ILIKE :palabra1', { palabra1: `%Asistencia%` })
//         // .andWhere('producto.name ILIKE :palabra2', { palabra2: `%enganche%` })
//         .andWhere('producto.name ILIKE :palabra2', { palabra2: `%${findAssociate.model} enganche%` })
//         .andWhere('producto.region = :region', { region })
//         .getOne();

//       // console.log('PRODUCTS ENGANCHE', product2)


//       const products = [product1, product2];
//       const total = products.reduce((acc, p) => acc + p.price, 0);
//       // console.log('TIENE ENGANCHE', findAssociate.contractNumber, findAssociate.downPayment, products.map(p => p.name), total)

//       // console.log('PRODUCTS ENGANCHE', products.length, products.map(p => p.name))
//       return products

//     }
//     /* DOESN'T HAVE DOWNPAYMENT */

//     const product1 = await this.productRepository.findOne({
//       where: { region, name: `Renting ${findAssociate.model}` },

//     });


//     const restAmount = findAssociate.weeklyRent - product1.price;

//     const product2 = await this.productRepository.createQueryBuilder('producto')
//       .where('producto.name ILIKE :palabra1', { palabra1: `%Asistencia%` })
//       .andWhere('producto.name ILIKE :palabra2', { palabra2: `%${findAssociate.model}%` })
//       .andWhere('producto.region = :region', { region })
//       .andWhere('producto.price = :price', { price: restAmount })
//       .getOne();
//     console.log('FIRST PRODUCT', product1?.name, product1?.price);

//     console.log('has downpayment', findAssociate.contractNumber, hasDownPayment, product2?.price)

//     console.log('rent', findAssociate.weeklyRent, 'rest amount: ', restAmount)
//     console.log('--------------------------------------------------')


//     // console.log('PRODUCTS NO ENGANCHE', product2)

//     const products = [product1, product2];
//     return products;

//   }

//   async test() {

//     const associatesFilterRegion = associates.filter(associate => associate.region.trim() === 'CDMX');

//     console.log('total associates:', associatesFilterRegion.length)

//     let count = 0;

//     const correctAssociates = []

//     await Promise.allSettled(associatesFilterRegion.map(async associate => {

//       const products = await this.fetchProductsDependingOnDownpayment('CDMX', associate.downPayment > 0, associate);
//       count++
//       correctAssociates.push(associate);
//       const total = products.reduce((acc, p) => acc + p.price, 0);

//       console.log('products', associate.contractNumber, products.length, products.map(p => p.name), 'Total: ', total);

//     }));

//     console.log('total associates:', associatesFilterRegion.length, 'count:', count)

//     const incorrectAssociates = associatesFilterRegion.filter(associate => associate._id !== correctAssociates.find(c => c._id === associate._id)?._id);
//     console.log('incorrect associates:', incorrectAssociates.length, incorrectAssociates.map(a => a.contractNumber));

//   }

//   async stopGigstackSubscriptions(region: RegionsType) {
//     const token = tokenAssignGigstack(region);

//     const cdmxAssociates = associates.filter(associate => associate.region.trim() === region);

//     console.log('total associates:', cdmxAssociates.length);

//     await Promise.allSettled(cdmxAssociates.map(async associate => {

//       try {
//         const res = await axios.put(`${this.baseurl}/recurring/status`, {
//           id: associate.gigSubscriptionId,
//           status: 'paused',
//         }, {
//           headers: {
//             Authorization: `Bearer ${token}`,
//           }
//         })

//         console.log('subscription stopped', associate._id, res.data)

//       } catch (error: any) {
//         console.log('error stopping subscription', associate.gigId, error.response?.data)
//       }


//     }))



//   }


// }