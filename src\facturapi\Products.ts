import { TaxFactorType, TaxTypesType } from '@/app/products/entities/product.entity';
import { CustomAxiosInstance } from './Wrapper';


export enum Taxability {
  '01' = '01',
  '02' = '02',
  '03' = '03',
  '04' = '04',
}

export type TaxabilityType = keyof typeof Taxability;

export interface ProductFacturapi {
  description: string;
  product_key: string;
  price: number;
  tax_included?: boolean;
  taxability?: TaxabilityType
  taxes?: {
    rate?: number;
    base?: string;
    type: TaxTypesType;
    factor?: TaxFactorType,
    withholding?: boolean;
  }[];
  unit_key?: string;
  unit_name?: string;
  sku?: string;
}

export class Products {

  private readonly client: CustomAxiosInstance;

  constructor(client: CustomAxiosInstance) {
    this.client = client;
  }

  /**
 * Creates a new Product in facturapi
 * @param {String} description - required
 * @param {String} product_key - required
 * @param {Number} price - required
 * @param {Boolean} tax_included - not required
 * @param {String} taxability - not required
 * @param {Number} taxes.rate - not required
 * @param {String} taxes.base - not required
 * @param {String} taxes.factor - not required
 * @param {Boolean} taxes.withholding - not required
 * @param {String} unit_key - not required
 * @param {String} unit_name - not required
 * @param {String} sku - not required
 */
  async create(data: ProductFacturapi) {
    const response = await this.client.post('/products', data);
    console.log('response', response);
    return response;
  }

  /**
   * Retrieves a customer by its ID
   * @param {String} id
   */


  async retrieve(id: string) {
    return await this.client.get(`/customers/${id}`);
  }

  /**
   * Retrieves a list of customers
   * @param {Object} params
   */
  async list(params: any) {
    return await this.client.get('/customers', { params });
  }

  /**
   * Updates a customer by its ID
   * @param {String} id
   * @param {Object} data
   */
  async update(id: string, data: any) {
    return await this.client.put(`/customers/${id}`, data);
  }

  /**
   * Deletes a customer by its ID
   * @param {String} id
   */
  async delete(id: string) {
    return await this.client.delete(`/customers/${id}`);
  }


  /**
   * Validate customer with SAT validation.
   * @param {string} id
   */
  async validateTaxInfo(id: string) {
    return await this.client.get('/customers/' + id + '/tax-info-validation');
  }

}