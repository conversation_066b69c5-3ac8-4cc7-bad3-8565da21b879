import { OnWorkerEvent, Processor, WorkerHost } from '@nestjs/bullmq';
import { Injectable, Logger, Scope } from '@nestjs/common';
import { Job } from 'bullmq';
import { EmailQueueService } from '../email-queue/email-queue.service';
import { WhatsappQueueService } from '../whatsapp-queue/whatsapp-queue.service';
import { NotificationQueueService } from '../notification-queue/notification-queue.service';
import { PaymentsService } from '../../payments/services/payments.service';
import { PAYMENT_QUEUE } from '@/constants';

@Processor({
  name: PAYMENT_QUEUE,
})
@Injectable()
export class PaymentProcessor extends WorkerHost {
  private readonly logger = new Logger(PaymentProcessor.name);
  constructor(
    private readonly emailQueueService: EmailQueueService,
    private readonly whatsappQueueService: WhatsappQueueService,
    private readonly notificationQueueService: NotificationQueueService,
    private readonly paymentsService: PaymentsService,
  ) {
    super();
  }

  async process(job: Job<any, any, string>): Promise<any> {
    // create payment here
    this.logger.log({
      message: `[PaymentProcessor] - Subscription is being procseed: ${job.data}`
    });
    const payment = await this.paymentsService.create({
      total: job.data.total,
      subTotal: job.data.subTotal,
      tax: job.data.tax,
      client: job.data.client,
      subscription: job.data,
      createdFrom: 'cronjob',
    });
    await this.emailQueueService.sendEmail(payment.data);
    await this.whatsappQueueService.sendWhatsapp(payment.data);
    await this.notificationQueueService.sendNotification(payment.data);
    return payment;
  }

  @OnWorkerEvent('active')
  onActive(job: Job) {
    this.logger.log(`[PaymentProcessor] - Active ${job.id} ${job.data}`);
  }

  @OnWorkerEvent('completed')
  onCompleted(job: Job) {
    this.logger.log(`[PaymentProcessor] - Completed ${job.id} ${job.data}`);
    //add send email, send whatsapp and send push notification to their queues
  }

  @OnWorkerEvent('failed')
  onFailed(job: Job) {
    this.logger.log(`[PaymentProcessor] - Failed ${job.id} ${job.data}`);
  }
}
