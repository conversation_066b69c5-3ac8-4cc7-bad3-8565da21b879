import { Module } from '@nestjs/common';
import { PaymentsService } from './services/payments.service';
import { PaymentsController } from './controllers/payments.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PaymentsEntity } from './entities/payment.entity';
import { Wire4WebHookService } from './services/wire4Webhook.service';
import { ClientEntity } from '../clients/entities/client.entity';
import { PaymentTransactionsEntity } from './entities/payment-transactions.entity';
import { PaymentTransactionService } from './services/payment-transactions.service';
import { Wire4WebhookDataEntity } from './entities/wire4-webhook.entity';
import { ClientsModule } from '../clients/clients.module';
import { ProductsModule } from '../products/products.module';
import { PaymentsControllerSecond } from './controllers/payments-2.controller';
import { UpdatePaymentService } from './services/update-payments.service';
import { SubscriptionEntity } from '../subscriptions/entities/subscription.entity';
import { ProductEntity } from '../products/entities/product.entity';
import { FacturapiPaymentsService } from './services/facturapi-payments.service';
import { S3Service } from '../aws/services/s3.service';
import { Wire4WebhookController } from './controllers/wire4-weebhook.controller';
import { HttpModule } from '@nestjs/axios';
import { PaymentsLoanTapeService } from './services/paymentsLoanTapeService';
import { PaymentsLoanTapeController } from './controllers/paymentsLoanTape.controller';
import { MongoDbModule } from '@/db/mongodb.module';
import { StockVehicleService } from './services/stock.service';
import { UnlockPaymentsService } from './services/unlock-payments.service';
import { BlockPaymentsService } from './services/block-payments.service';
import { PaymentSubscriber } from './subscribers/payment.subscriber';
import { GpsService } from './services/gps.service';
import { NotificationModule } from '../notification/notification.module'

@Module({
  imports: [
    TypeOrmModule.forFeature([
      PaymentsEntity,
      ClientEntity,
      PaymentTransactionsEntity,
      Wire4WebhookDataEntity,
      ProductEntity,
      SubscriptionEntity
    ]),
    ClientsModule,
    ProductsModule,
    HttpModule,
    MongoDbModule,
    NotificationModule,
  ],
  controllers: [PaymentsController, PaymentsControllerSecond, Wire4WebhookController, PaymentsLoanTapeController],
  providers: [
    PaymentsService,
    Wire4WebHookService,
    PaymentTransactionService,
    UpdatePaymentService,
    FacturapiPaymentsService,
    S3Service,
    PaymentsLoanTapeService,
    StockVehicleService,
    UnlockPaymentsService,
    BlockPaymentsService,
    PaymentSubscriber,
    GpsService
  ],
  exports: [PaymentsService, PaymentTransactionService, UpdatePaymentService, Wire4WebHookService, PaymentsLoanTapeService, StockVehicleService, UnlockPaymentsService, GpsService],
})
export class PaymentsModule { }
