import { Injectable, UseGuards, Inject } from "@nestjs/common";
import { InjectRepository } from '@nestjs/typeorm';
import { KeyEntity } from '../entities/keys.entity';
import { Repository } from 'typeorm';
import { generateApiKey } from '@/lib/randomId';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
// import { JwtAuthGuard } from '@/app/jwt/guard/jwt.guard';

@Injectable()
export class KeysService {

  constructor(
    @InjectRepository(KeyEntity) private readonly keysRepository: Repository<KeyEntity>,
    @Inject(CACHE_MANAGER) private cacheManager: Cache
  ) { }

  async createKey(userId: string) {


    await this.keysRepository.update({}, { isActive: false });
    const key = new KeyEntity();
    key.userId = userId;
    key.key = generateApiKey();
    return await this.keysRepository.save(key);
  }


  async getKeys() {
    const result = await this.keysRepository.find({
      where: {
        isActive: true
      }
    })

    return {
      message: 'Keys fetched successfully',
      data: result
    };
  }

  async validateKey(key: string) {
    const cacheKey = `apiKey:${key}`;

    const cached = await this.cacheManager.get<boolean>(cacheKey);

    if (cached) {
      return cached;
    }

    const result = await this.keysRepository.findOne({
      where: {
        key,
        isActive: true
      }
    })
    const isValid = !!result;
    await this.cacheManager.set(cacheKey, isValid, 36000);

    return isValid;
  }


}
