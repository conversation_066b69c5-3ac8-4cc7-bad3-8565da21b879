import { tryCatchResponse } from '@/lib/defaultResponse';
import { Body, Controller, Post, Res } from '@nestjs/common';
import { AutoInvoiceService } from '../services/auto-invoice.service';
import { Response } from 'express';
import { AutoInvoiceEventData } from '../types/auto-invoice.types';
import { PublicRoute } from '@/app/keys/guards/public.decorator';




@Controller('webhooks/auto-invoice')
export class AutoInvoiceWebhookController {

  constructor(
    private readonly autoInvoiceService: AutoInvoiceService,
  ) { }

  @PublicRoute()
  @Post()
  async handleWebhook(@Res() res: Response, @Body() body: AutoInvoiceEventData) {
    // Lógica para manejar el webhook de auto-invoice
    return await tryCatchResponse(res, async () => this.autoInvoiceService.handleAutoInvoiceWebhook(body));
  }
}