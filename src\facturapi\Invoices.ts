import { Customer } from './Customers';
import { ProductFacturapi } from './Products';
import { CFDI_KEYS_TYPE } from '@/sat/cfdi.types';
import { CustomAxiosInstance } from './Wrapper';


export type PaymentFormType = '01' | '02' | '03' | '04' | '05' | '06' | '08';


export interface ItemProps {
  product: ProductFacturapi,
  quantity?: number,
  discount?: number,
};


export interface Invoice {
  // id: string;
  customer: string | Customer;
  items: ItemProps[] /* | string[] */;
  payment_form: PaymentFormType;
  use: CFDI_KEYS_TYPE;
  type?: 'I' | 'E';
  payment_method?: 'PUE';
  currency?: 'MXN' | 'USD';
  exchange?: number;
  series?: string;
}


export interface InvoiceResponse {
  id: string;
  customer: Customer;
  items: ItemProps[];
  payment_form: PaymentFormType;
  use: CFDI_KEYS_TYPE;
  type: 'I' | 'E';
  payment_method: 'PUE';
  currency: 'MXN' | 'USD';
  exchange: number;
  status: 'active' | 'cancelled';
  total: number;
  sub_total: number;
  tax: number;
  created_at: string;
  updated_at: string;

}

type DeleteInvoiceParams = {
  motive: '01' | '02' | '03';
}

export class Invoices {

  private readonly client: CustomAxiosInstance;

  constructor(client: CustomAxiosInstance) {
    this.client = client;
  }

  /**
 * Creates a new Product in facturapi
 * @param {String} description - required
 * @param {String} product_key - required
 * @param {Number} price - required
 * @param {Boolean} tax_included - not required
 * @param {String} taxability - not required
 * @param {Number} taxes.rate - not required
 * @param {String} taxes.base - not required
 * @param {String} taxes.factor - not required
 * @param {Boolean} taxes.withholding - not required
 * @param {String} unit_key - not required
 * @param {String} unit_name - not required
 * @param {String} sku - not required
 */
  // create a new invoice and type the response as InvoiceResponse to get the response data typed
  async create(data: Invoice): Promise<InvoiceResponse> {
    return await this.client.post('/invoices', data);
  }

  /**
   * Retrieves a customer by its ID
   * @param {String} id
   */


  async retrieve(id: string) {
    return await this.client.get(`/invoices/${id}`);
  }

  /**
   * Retrieves a list of customers
   * @param {Object} params
   */
  async list(params?: any) {
    return await this.client.get('/customers', { params });
  }

  /**
   * Updates a customer by its ID
   * @param {String} id
   * @param {Object} data
   */
  async update(id: string, data: any) {
    return await this.client.put(`/customers/${id}`, data);
  }


  /**
   * Deletes a customer by its ID
   * @param {String} id
   */
  async delete(id: string, params?: DeleteInvoiceParams) {
    let url = `/invoices/${id}`;

    if (params) {
      url += `?motive=${params.motive}`;
    }

    return await this.client.delete(`${url}`);
  }


  /** 
   * Downloads the invoice on PDF, XML or both formats in a ZIP file 
   * @param {String} id
   * @param {String} format
  */
  async download(id: string, format: 'pdf' | 'xml' | 'zip') {
    return await this.client.get(`/invoices/${id}/${format}`, {
      responseType: 'arraybuffer',
      responseEncoding: 'binary',
    });
  }


  /**
   * Sends the invoice by email
   * @param {String} id
   * @param {Object} data
  */
  async sendInvoiceByEmail(id: string, data?: { email?: string }) {
    return await this.client.post('/invoices/' + id + '/email', data);
  }


  /**
   * Creates a new receipt
   * @param {Object} data
  */
  async createReceipt(data: any) {
    return await this.client.post('/receipts', data);
  }

  /**
   * Sends the receipt by email
   * @param {String} id
   * @param {Object} data
  */
  async sendReceiptByEmail(id: string, data?: { email?: string }) {
    return await this.client.post('/receipts/' + id + '/email', data);
  }

}