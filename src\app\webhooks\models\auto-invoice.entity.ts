import { Entity } from "typeorm";
import { PrimaryGeneratedColumn, Column } from "typeorm";

@Entity({ name: 'auto_invoice_webhook' })
export class AutoInvoiceEntity {

  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ type: 'text', nullable: true })
  requestId: string;

  @Column({ type: 'text', nullable: true })
  created_at: string;

  @Column({ type: 'text', nullable: true })
  livemode: boolean;

  @Column({ type: 'text', nullable: true })
  organization: string;

  @Column({ type: 'text', nullable: true })
  type: string;

  @Column({ type: 'jsonb', nullable: true })
  data: any;

  @Column({ type: 'boolean', default: false })
  process_completed: boolean;

}