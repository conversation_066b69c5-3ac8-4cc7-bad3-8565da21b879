import { BullModule } from '@nestjs/bullmq';
import { Modu<PERSON> } from '@nestjs/common';
import { EmailQueueService } from './email-queue.service';
import { EmailProcessor } from './email-queue.processor';
import { BullMQAdapter } from '@bull-board/api/bullMQAdapter';
import { BullBoardModule } from '@bull-board/nestjs';
import { EMAIL_QUEUE } from '@/constants';

@Module({
  imports: [
    BullModule.registerQueue({
      name: EMAIL_QUEUE,
    }),
    BullBoardModule.forFeature({
      name: EMAIL_QUEUE, // Register the queue with Bull Board
      adapter: BullMQAdapter,
    }),
  ],
  providers: [EmailQueueService, EmailProcessor],
  exports: [EmailQueueService],
})
export class EmailQueueModule {}
