import { BullModule } from '@nestjs/bullmq';
import { Module } from '@nestjs/common';
import { PaymentQueueService } from './payment-queue.service';
import { PaymentProcessor } from './payment-queue.processor';
import { BullMQAdapter } from '@bull-board/api/bullMQAdapter';
import { BullBoardModule } from '@bull-board/nestjs';
import { EmailQueueModule } from '../email-queue/email-queue.module';
import { WhatsappQueueModule } from '../whatsapp-queue/whatsapp-queue.module';
import { NotificationQueueModule } from '../notification-queue/notification-queue.module';
import { PaymentsModule } from '../../payments/payments.module';
import { PAYMENT_QUEUE } from '@/constants';

@Module({
  imports: [
    BullModule.registerQueue({
      name: PAYMENT_QUEUE,
    }),
    BullBoardModule.forFeature({
      name: PAYMENT_QUEUE, // Register the queue with Bull Board
      adapter: BullMQAdapter,
    }),
    EmailQueueModule,
    WhatsappQueueModule,
    NotificationQueueModule,
    PaymentsModule,
  ],
  providers: [PaymentQueueService, PaymentProcessor],
  exports: [PaymentQueueService],
})
export class PaymentQueueModule {}
