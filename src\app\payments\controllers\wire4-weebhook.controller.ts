import { Controller, Post, Body, Res, Get, UsePipes, ValidationPipe, Query } from '@nestjs/common';
import { tryCatchResponse } from '@/lib/defaultResponse';
import { Response } from 'express';
import { Wire4WebHookService } from '../services/wire4Webhook.service';
import { PublicRoute } from '@/app/keys/guards/public.decorator';
import { Wire4FilterDto } from '../dto/wire4-filter.dto';

@Controller('payments')
export class Wire4WebhookController {
  constructor(private readonly wire4Webhook: Wire4WebHookService) { }

  @PublicRoute()
  @Post('webhook')
  async webhook(@Body() body: any, @Res() res: Response) {
    return await tryCatchResponse(res, async () => this.wire4Webhook.webhook(body));
  }

  @Get('filtered')
  @UsePipes(new ValidationPipe({ transform: true,  transformOptions: {
    enableImplicitConversion: true, // <- This line here
  }, }))
  async getFilteredData(@Query() filterDto: Wire4FilterDto) {
    console.log('filterDto', filterDto);
    return this.wire4Webhook.getFilteredWire4WebhookData(filterDto);
  }

}
