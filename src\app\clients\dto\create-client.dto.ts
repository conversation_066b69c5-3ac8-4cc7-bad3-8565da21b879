import { CFDI_KEYS_ENUM, CFDI_KEYS_TYPE } from '@/sat/cfdi.types';
import { TaxSystem, TaxSystemValues } from '@/sat/tax_system.types';
import { RegionsEnum, RegionsType } from '@/types/entities.types';
import { Type } from 'class-transformer';
import { IsBoolean, IsDefined, IsEnum, IsIn, IsObject, IsOptional, IsString, Matches, MinLength } from 'class-validator';
import { IsOptionalIf } from './custom-validator';

export enum CountriesEnum {
  'United States' = 'United States',
  Mexico = 'Mexico',
}

export const CountriesShortNames = {
  [CountriesEnum['United States']] : 'US',
  [CountriesEnum.Mexico] : 'MX'
}

export enum CountriesShortNamesEnum {
 'United States' = 'US',
  Mexico = 'MX',
}

export class CreateClientDto {

  @IsDefined()
  @IsString()
  contractNumber: string;

  @IsDefined()
  @IsString()
  name: string;

  @IsDefined()
  @IsString()
  lastName: string;

  @IsOptional()
  @IsString()
  legal_name: string;

  @IsDefined()
  @IsString()
  email: string;

  @IsDefined()
  @IsString()
  phone: string;

  @IsOptional()
  @IsString()
  monexClabe: string;

  @IsOptional()
  @IsString()
  country: string = 'MX';

  @IsOptional()
  // @IsString()
  @IsIn(TaxSystemValues)
  tax_system: TaxSystem;

  @IsDefined()
  @IsString()
  @MinLength(5)
  zip: string;

  @IsDefined()
  @IsEnum(RegionsEnum)
  region: RegionsType

  @IsOptional()
  @IsBoolean()
  isActive: boolean;


  @IsString()
  @IsOptionalIf('country', CountriesEnum['United States'] , {  message: 'RFC is invalid, please provide a valid RFC. Example with generic RFC: XAXX010101000' })
  rfc: string;

  @IsOptional()
  @IsEnum(CFDI_KEYS_ENUM)
  use_cfdi: CFDI_KEYS_TYPE;

  @IsDefined()
  @IsString()
  associateId?: string;

  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;

  @IsOptional()
  @IsString()
  city?: string

  @IsOptional()
  @IsString()
  state?: string

  @IsOptional()
  @IsString()
  street?: string;
}
