// src/health/health.controller.ts
import { Controller, Get } from '@nestjs/common';
import { HealthCheckService, HealthCheck, TypeOrmHealthIndicator } from '@nestjs/terminus';
import { RedisHealthIndicatorService } from '../services/redis.health.service';
import { PublicRoute } from '@/app/keys/guards/public.decorator';

@Controller('health')
export class HealthController {
  constructor(
    private health: HealthCheckService,
    private redisHealthIndicatorService: RedisHealthIndicatorService,
  ) {}
  
  @PublicRoute()
  @Get()
  @HealthCheck()
  check() {
    return this.health.check([
      async () => this.redisHealthIndicatorService.isHealthy('redis'),
    ]);
  }
}
