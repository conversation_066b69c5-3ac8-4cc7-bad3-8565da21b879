import { ArrayMinSize, ArrayNotEmpty, IsArray, IsDefined, IsOptional, IsString, IsUUID, Matches, ValidateNested, IsNumber, Min, Max, IsBoolean, IsEnum, ValidateIf, IsDateString } from 'class-validator';
// import { CreateSubscriptionDto, ProductBody } from './create-subscription.dto';
import { Type, Transform } from 'class-transformer';
import { TaxFactorEnum, TaxFactorType, TaxTypesEnum, TaxTypesType } from '@/app/products/entities/product.entity';
import { RegionsEnum, RegionsType } from '@/types/entities.types';

export class ProductBodyWithPrice {
  @IsString()
  @Matches(/^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/, { message: 'Invalid UUID format' })
  id: string;

  @IsOptional()
  @IsNumber()
  @Min(1)
  @Transform(({ value }) => parseFloat(value))
  quantity: number = 1;

  @IsNumber()
  @Min(0.01)
  @Transform(({ value }) => parseFloat(value))
  price: number;

  @IsNumber()
  @IsOptional()
  @Transform(({ value }) => value ? parseFloat(value) : value)
  subTotal: number;

  @IsNumber()
  @IsOptional()
  @Transform(({ value }) => value ? parseFloat(value) : value)
  tax: number;

  @IsNumber()
  @IsOptional()
  @Transform(({ value }) => value ? parseFloat(value) : value)
  discount: number;

  @IsOptional()
  @IsString()
  product_key: string;

  @IsOptional()
  @IsString()
  unit_key: string;

  @IsOptional()
  @IsString()
  measurementUnit: string;

  @IsOptional()
  @IsBoolean()
  hasTaxes: boolean;

  @IsNumber({
    allowInfinity: false,
    allowNaN: false,
    maxDecimalPlaces: 2,
  })
  @Min(0.01)
  @Max(1, {
    message: 'The tax rate must be a value between 0.01 and 1, the percentage of the tax rate in decimal format. Example: 0.16 for 16% tax rate.'
  })
  @Transform(({ value }) => value ? parseFloat(value) : value)
  @ValidateIf((object) => object.hasTaxes === true)
  taxRate?: number;

  @IsString()
  @IsEnum(TaxFactorEnum)
  @ValidateIf((object) => object.hasTaxes === true)
  taxFactor?: TaxFactorType;

  @IsString()
  @IsEnum(TaxTypesEnum)
  @ValidateIf((object) => object.hasTaxes === true)
  taxType?: TaxTypesType;
}

export class CreateSubscriptionPaymentDto {

  // @IsDefined()
  @IsOptional()
  @IsUUID()
  readonly clientId: string;

  @IsOptional()
  @IsString()
  readonly subscriptionId: string;

}

export class InitiatePayFlowDto {
  @IsString()
  @Matches(/^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/, { message: 'Invalid UUID format' })
  clientId: string;

  @IsArray()
  @ArrayNotEmpty({ message: 'At least one product is required' })
  @ArrayMinSize(1, { message: 'At least one product is required' })
  @IsDefined()
  @ValidateNested()
  @Type(() => ProductBodyWithPrice)
  public products: ProductBodyWithPrice[];

  @IsOptional()
  @IsEnum(RegionsEnum)
  region: RegionsType;

  @IsOptional()
  @IsDateString()
  startDate: string;

  @IsDefined()
  @IsDateString()
  endDate: string;

  @IsOptional()
  @Type(() => ProductBodyWithPrice)
  downPaymentProduct: ProductBodyWithPrice;

  @IsOptional()
  @IsString()
  rentingType: 'normal_rent' | 'deposit' | 'down_payment';

  @IsOptional()
  @Type(() => ProductBodyWithPrice)
  depositProduct: ProductBodyWithPrice;
}