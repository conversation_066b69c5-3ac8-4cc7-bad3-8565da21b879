import { ArrayMinSize, ArrayNotEmpty, IsArray, IsDefined, IsOptional, IsString, IsUUID, Matches, ValidateNested } from 'class-validator';
import { CreateSubscriptionDto, ProductBody } from './create-subscription.dto';
import { Type } from 'class-transformer';



export class CreateSubscriptionPaymentDto {

  // @IsDefined()
  @IsOptional()
  @IsUUID()
  readonly clientId: string;

  @IsOptional()
  @IsString()
  readonly subscriptionId: string;

}

export class InitiatePayFlowDto extends CreateSubscriptionDto {

  @IsOptional()
  // @IsArray()
  // @ArrayNotEmpty({ message: 'At least one product is required' })
  // @ArrayMinSize(1, { message: 'At least one product is required' })
  // @ValidateNested()
  @Type(() => ProductBody)
  downPaymentProduct: CreateSubscriptionDto['products'][number];

  @IsOptional()
  @IsString()
  rentingType: 'normal_rent' | 'deposit' | 'down_payment';

  @IsOptional()
  // @IsArray()
  // @ValidateNested()
  @Type(() => ProductBody)
  depositProduct: CreateSubscriptionDto['products'][number];

}