import { Transform, Type } from 'class-transformer';
import { IsBoolean, IsEnum, IsNumber, IsOptional, IsString, ValidateIf } from 'class-validator';
import { PaymentStatusEnum, PaymentStatusType } from '../entities/payment.entity';
import { PaymentTransactionStatusEnum } from '../entities/payment-transactions.entity';
import { RegionsEnum, RegionsType } from '@/types/entities.types';
import { CountriesEnum } from '@/app/clients/dto/create-client.dto';

export enum PeriodicityEnum {
  LAST_DAY = 'LAST_DAY',
  LAST_WEEK = 'LAST_WEEK',
  LAST_MONTH = 'LAST_MONTH',
  LAST_YEAR = 'LAST_YEAR',
  // FROM_START = 'FROM_START',
}

export type PeriodicityType = keyof typeof PeriodicityEnum;

export class FindQueryPaymentDto {

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  readonly total: number;

  @IsOptional()
  @IsEnum(PaymentStatusEnum)
  readonly status: PaymentStatusType;

  @IsOptional()
  @Transform(({ value }) => value === 'true', { toClassOnly: true })
  readonly isPaid: boolean;

  @IsOptional()
  @IsString()
  readonly clientId: string;

  @IsOptional()
  @IsString()
  readonly email: string;

  @IsOptional()
  @IsString()
  readonly startDate: string;

  // @ValidateIf((object) => object.startDate)
  @IsOptional()
  @IsString()
  readonly endDate: string;

  @IsOptional()
  @IsEnum(PeriodicityEnum)
  readonly periodicity: PeriodicityType;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  readonly limit: number;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  readonly page: number;

  @IsOptional()
  @IsEnum(RegionsEnum)
  readonly region: RegionsType;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  readonly totalMoreThan: number;

  @IsOptional()
  @Transform(({ value }) => value === 'true', { toClassOnly: true })
  readonly includeFines: boolean;

  @IsOptional()
  @IsEnum(CountriesEnum)
  readonly country: CountriesEnum;
}
