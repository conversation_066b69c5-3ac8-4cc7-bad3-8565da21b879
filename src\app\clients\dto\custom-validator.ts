import {
  registerDecorator,
  ValidationArguments,
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
} from 'class-validator';

@ValidatorConstraint({ async: false })
export class IsOptionalIfConstraint implements ValidatorConstraintInterface {
  validate(value: any, args: ValidationArguments) {
    const [relatedPropertyName, relatedValue] = args.constraints;
    const relatedPropertyValue = (args.object as any)[relatedPropertyName];

    // If the related property value matches the specified value, the current field can be optional
    if (relatedPropertyValue === relatedValue) {
      return true; // No need to validate the current field (treated as optional)
    }
    const rfcRegex = /^[A-Z]{4}[0-9]{6}[A-Z0-9]{3}$/;

    return (
      value !== undefined &&
      value !== null &&
      value !== '' &&
      rfcRegex.test(value)
    );
  }

  defaultMessage(args: ValidationArguments) {
    const [relatedPropertyName, relatedValue] = args.constraints;
    return `${args.property} is required when ${relatedPropertyName} is not ${relatedValue}.`;
  }
}

export function IsOptionalIf(
  property: string,
  value: any,
  validationOptions?: ValidationOptions,
) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [property, value],
      validator: IsOptionalIfConstraint,
    });
  };
}
