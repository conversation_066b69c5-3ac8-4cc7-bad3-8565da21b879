import { Modu<PERSON> } from '@nestjs/common';
import { NotificationService } from './services/notification.service';
import { NotificationController } from './controllers/notification.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Notifications } from './entities/notification.entity';
import { NotificationToken } from './entities/notification-token.entity';
import { ClientsModule } from '../clients/clients.module';
import { ClientEntity } from '../clients/entities/client.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([Notifications, NotificationToken, ClientEntity]),
  ],
  controllers: [NotificationController],
  providers: [NotificationService],
  exports: [NotificationService]
})

export class NotificationModule {}