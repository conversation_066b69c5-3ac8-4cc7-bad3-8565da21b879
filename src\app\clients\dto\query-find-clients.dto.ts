import { IsDefined, IsOptional, IsString,  } from 'class-validator';
import { CountriesEnum } from './create-client.dto';

export class FindClient {

  @IsDefined()
  @IsString()
  search: string;

  // @IsOptional()
  // @IsString()
  // legal_name: string;


  // @IsOptional()
  // @IsString()
  // contractNumber: string;

  // @IsOptional()
  // @IsString()
  // email: string;

}

export class FindClientsByCountry{
  
  @IsOptional()
  @IsString()
  country: CountriesEnum;
}