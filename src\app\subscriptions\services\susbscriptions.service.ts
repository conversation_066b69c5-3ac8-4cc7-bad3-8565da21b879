import { HttpException, Injectable } from '@nestjs/common';
import { CreateSubscriptionDto } from '../dto/create-subscription.dto';
import { UpdateSubscriptionDto } from '../dto/update-subscription.dto';
import { FindManyOptions, Repository } from 'typeorm';
import { SubscriptionEntity } from '../entities/subscription.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { ProductsService } from '@/app/products/services/products.service';
import { SubscriptionProductsService } from '@/app/subscription-products/services/subscription-products.service';
import { ClientsService } from '@/app/clients/services/clients.service';
import { isUUID } from 'class-validator';
import { ClientEntity } from '@/app/clients/entities/client.entity';

@Injectable()
export class SubscriptionsService {

  constructor(
    @InjectRepository(SubscriptionEntity) private readonly subscriptionRepository: Repository<SubscriptionEntity>,
    @InjectRepository(ClientEntity) private readonly clientRepository: Repository<ClientEntity>,
    private readonly productService: ProductsService,
    private readonly clientService: ClientsService,
    private readonly subscriptionProductsService: SubscriptionProductsService,
  ) { }

  async create(createSuscriptionDto: CreateSubscriptionDto) {

    const { data: client } = await this.clientService.findOne(createSuscriptionDto.clientId);

    if (!client) {
      throw new HttpException('Client not found', 404);
    }

    const products = [];
    await Promise.allSettled(createSuscriptionDto.products.map(async (p) => {
      const res = await this.productService.findOne(p.id);
      const productEntity = res.data;
      if (!productEntity) {
        throw new HttpException('Product ${p.name}', 404);
      }

      const taxRate = productEntity.taxRate || 0.16;
      const rate = taxRate === 1 ? 0 : taxRate;
      const subTotal = productEntity.price * (1 - rate)

      const tax = taxRate === 1 ? 0 : productEntity.price * rate

      const productSubscription = {
        ...productEntity,
        quantity: p.quantity,
        hasTaxes: p.hasTaxes || false,
        subscription: null,
        total: p.quantity * productEntity.price,
        discount: p.discount,
        subTotal,
        tax,
      }

      delete productSubscription.id;
      delete productSubscription.createdAt;

      const subscriptionProduct = await this.subscriptionProductsService.create(productSubscription);
      products.push(subscriptionProduct.data);
      return true;
    }));

    const { total, subTotal, tax } = products.reduce((acc, p) => {
      acc.total += p.total;
      acc.subTotal += p.subTotal;
      acc.tax += p.tax;
      return acc;
    }, { total: 0, subTotal: 0, tax: 0 });


    const subscriptionCreate = this.subscriptionRepository.create({
      client,
      region: client.region,
      products,
      total,
      subTotal,
      tax,
      startDate: createSuscriptionDto.startDate || new Date(),
      endDate: createSuscriptionDto.endDate
    });
    // console.log('products', products)
    console.log('subscriptionCreate', subscriptionCreate, 'CHECKING SUBSCRIPTION CREATE')

    const subscription = await this.subscriptionRepository.save(subscriptionCreate);

    // const prod = await Promise.all(products.map(async (p) => {
    //   console.log('p', p)
    //   const subscriptionProduct = await this.subscriptionProductsService.create(p);
    //   console.log('subscriptionProduct', subscriptionProduct)
    //   return subscriptionProduct.data;
    // }));

    return {
      message: 'Subscription created successfully',
      data: subscription,
    }
  }

  async findAll(query: FindManyOptions<SubscriptionEntity> = {}) {

    const where = query.where || {};
    const relations = query.relations || ['products', 'client'];
    const take = query.take;

    const subscriptions = await this.subscriptionRepository.find({
      where,
      relations,
      take,
    });

    return {
      message: 'Subscriptions found',
      total: subscriptions.length,
      data: subscriptions,
    }

  }

  async findOne(id: string, query?: { withClient?: boolean, withProducts?: boolean }) {

    const defaultQuery = { withClient: true, withProducts: true };
    const mergedQuery = { ...defaultQuery, ...query };

    const relations = [];

    if (mergedQuery?.withProducts) relations.push('products');
    if (mergedQuery?.withClient) relations.push('client');

    const subscription = await this.subscriptionRepository.findOne({
      where: {
        id,
      },
      relations,
    });

    if (!subscription) {
      throw new HttpException('Subscription not found', 404);
    }

    return {
      message: 'Subscription found',
      data: subscription,
    }

  }

  async update(id: string, updateSuscriptionDto: UpdateSubscriptionDto) {
    const where = isUUID(id) ? { client: { id } } : { id };

    const subscription = await this.subscriptionRepository.findOne({
      where,
      relations: ['products']
    });

    if (!subscription) {
      throw new HttpException('Subscription not found', 404);
    }

    const products = updateSuscriptionDto.products
    // console.log('products', products)

    if (products && products.length > 0) {

      for (const p of products) {

        await this.subscriptionProductsService.update(p.id, p)

      }

    }

    delete updateSuscriptionDto.products;
    delete subscription.products;
    delete subscription.total;
    delete subscription.subTotal;
    delete subscription.tax;

    const updatedSubscription = Object.assign({}, subscription, updateSuscriptionDto);

    await this.subscriptionRepository.update(subscription.id, updatedSubscription);

    const updatedSubscriptionData = await this.subscriptionRepository.findOne({
      where: {
        id,
      },
    });

    return {
      message: 'Subscription updated successfully',
      data: updatedSubscriptionData,
    }
  }

  async updateStatus(id: string, status: boolean) {

    const where = isUUID(id) ? { client: { id } } : { id };


    const subscription = await this.subscriptionRepository.findOne({
      where,
      relations: ['client'],
      select: {
        id: true,
        isActive: true,
        client: {
          id: true,
          // email: true,
          contractNumber: true,
        }
      }
    });

    if (!subscription) {
      throw new HttpException('Subscription not found', 404);
    }

    // console.log('subscription', subscription)
    const updatedSubscription = Object.assign({}, subscription, { isActive: status });

    // console.log('updatedSubscription', updatedSubscription)
    await this.clientRepository.update(subscription.client.id, { isActive: status });
    await this.subscriptionRepository.update(subscription.id, updatedSubscription);

    return {
      message: 'Subscription updated successfully',
      status: status,
      id: updatedSubscription.id,
      // data: updatedSubscription,
    }
  }


  async remove(id: string) {
    const subscription = await this.subscriptionRepository.findOne({
      where: {
        id,
      },
    });

    if (!subscription) {
      throw new HttpException('Subscription not found', 404);
    }

    await this.subscriptionRepository.remove(subscription);

    return {
      message: 'Subscription removed successfully',
    }
  }


  async createWithProcessedProducts(createSuscriptionDto: any) {
    const { data: client } = await this.clientService.findOne(createSuscriptionDto.clientId);

    if (!client) {
      throw new HttpException('Client not found', 404);
    }

    // Products are already processed with correct prices from body
    const products = [];
    await Promise.allSettled(createSuscriptionDto.products.map(async (p) => {
      const productSubscription = {
        ...p,
        subscription: null,
      } as any;

      delete productSubscription.id;

      const subscriptionProduct = await this.subscriptionProductsService.create(productSubscription);
      products.push(subscriptionProduct.data);
      return true;
    }));

    const { total, subTotal, tax } = products.reduce((acc, p) => {
      acc.total += p.total;
      acc.subTotal += p.subTotal;
      acc.tax += p.tax;
      return acc;
    }, { total: 0, subTotal: 0, tax: 0 });

    const subscriptionCreate = this.subscriptionRepository.create({
      client,
      region: client.region,
      products,
      total,
      subTotal,
      tax,
      startDate: createSuscriptionDto.startDate || new Date(),
      endDate: createSuscriptionDto.endDate
    });

    console.log('subscriptionCreate with processed products', subscriptionCreate, 'CHECKING SUBSCRIPTION CREATE WITH PROCESSED PRODUCTS')

    const subscription = await this.subscriptionRepository.save(subscriptionCreate);

    return {
      message: 'Subscription created successfully',
      data: subscription,
    }
  }

  async createSubscriptionForUSClient(createSuscriptionDto: CreateSubscriptionDto) {

    const { data: client } = await this.clientService.findOne(createSuscriptionDto.clientId);

    if (!client) {
      throw new HttpException('Client not found', 404);
    }

    const products = [];
    await Promise.allSettled(createSuscriptionDto.products.map(async (p) => {
      const res = await this.productService.findOne(p.id);
      const productEntity = res.data;
      if (!productEntity) {
        throw new HttpException('Product ${p.name}', 404);
      }

      const tax = productEntity.tax
      const subTotal = productEntity.subTotal

      const productSubscription = {
        ...productEntity,
        quantity: p.quantity,
        hasTaxes: p.hasTaxes || false,
        subscription: null,
        total: productEntity.price,
        discount: p.discount,
        subTotal,
        tax,
      } as any;

      delete productSubscription.id;
      delete productSubscription.createdAt;

      const subscriptionProduct = await this.subscriptionProductsService.create(productSubscription);
      products.push(subscriptionProduct.data);
      return true;
    }));

    const { total, subTotal, tax } = products.reduce((acc, p) => {
      acc.total += p.total;
      acc.subTotal += p.subTotal;
      acc.tax += p.tax;
      return acc;
    }, { total: 0, subTotal: 0, tax: 0 });


    const subscriptionCreate = this.subscriptionRepository.create({
      client,
      region: client.region,
      products,
      total,
      subTotal,
      tax,
      startDate: createSuscriptionDto.startDate || new Date(),
      endDate: createSuscriptionDto.endDate
    });
    const subscription = await this.subscriptionRepository.save(subscriptionCreate);
    return {
      message: 'Subscription created successfully',
      data: subscription,
    }
  }


}
