import { NestFastifyApplication } from '@nestjs/platform-fastify';

export function corsConfig(app: NestFastifyApplication, isProd: boolean): void {
  if (isProd) {
    // app.enableCors({
    //   origin: [
    //     'https://pagos.onecarnow.com',
    //     'https://www.pagos.onecarnow.com',
    //     'https://administrador.onecarnow.com/',
    //     'https://www.administrador.onecarnow.com/',
    //     'https://develop.administrador.onecarnow.com/',
    //     'https://www.develop.administrador.onecarnow.com/',
    //     'http://localhost:8080',
    //   ],
    //   methods: '*',
    //   preflightContinue: false,
    //   optionsSuccessStatus: 204,
    //   credentials: true,
    // });

    app.enableCors({
      origin: true,
      methods: ['GET', 'HEAD', 'PUT', 'PATCH', 'POST', 'DELETE', 'OPTIONS'],
    });

  } else {
    app.enableCors({
      origin: true,
      methods: ['GET', 'HEAD', 'PUT', 'PATCH', 'POST', 'DELETE', 'OPTIONS'],
    });
  }

}