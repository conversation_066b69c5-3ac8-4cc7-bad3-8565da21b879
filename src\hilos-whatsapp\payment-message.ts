import { CHARGE_100_TEMPLATE_ID, HILOS_API,
  HILOS_API_KEY, PAYMENT_FRONT_URL, PAYMENT_TEMPLATE_ID,
  REACTIVATION_PAYMENT_CONFIRMATION_TEMPLATE_ID,
  UNLOCK_PAYMENT_TEMPLATE_ID ,
  VEHICLE_UNLOCK_CONFIRMATION_TEMPLATE_ID,
} from '@/constants';
import axios from 'axios';


const imgUrl = 'https://hilos.io/api/file/p/154473c6-5dd9-4bd0-a5df-6772e0b00eb6'

export async function sendPaymentWAMessagePAYFLOW(name: string, payment_id: string, phone: string, amount?: number, template_id: string = PAYMENT_TEMPLATE_ID) {
  try {

    const isUnblock = amount && amount === 100;

    if (isUnblock) template_id = CHARGE_100_TEMPLATE_ID;

    const variables = isUnblock ? [payment_id] : [imgUrl, name, payment_id];

    /* const res =  */ await axios.post(`${HILOS_API}/api/channels/whatsapp/template/${template_id}/send`, {
      variables,
      phone: phone.split('+').join(''),
  }, {
    headers: {
      Authorization: `Token ${HILOS_API_KEY}`
    }
  });
    console.log('Message sent to: ', phone, 'with payment_id: ', payment_id, 'and name: ', name);
    return true;
    // console.log('SENT IT', res.data);
  } catch (error: any) {
    console.log('Error sending message: ', error.response.data);
    console.log('Phone: ', phone, 'payment_id: ', payment_id, 'name: ', name);
  }

}

const change_CLABE_Template_Id = '2ce47da8-ddfa-425c-9b70-db9645ac8f53';

export async function sendPaymentWAMessage(name: string, payment_id: string, phone: string, amount?: number, template_id: string = change_CLABE_Template_Id){

  try {
    const isUnblock = amount && amount === 100;

    if (isUnblock) template_id = CHARGE_100_TEMPLATE_ID;

    const variables = isUnblock ? [payment_id] : [/* imgUrl, */ name, payment_id];
    console.log('url request', `${HILOS_API}/api/channels/whatsapp/template/${template_id}/send`);
    /* const res =  */ await axios.post(`${HILOS_API}/api/channels/whatsapp/template/${template_id}/send`, {
      variables,
      phone: phone.split('+').join(''),
  }, {
    headers: {
      Authorization: `Token ${HILOS_API_KEY}`
    }
  });
    console.log('Message sent to: ', phone, 'with payment_id: ', payment_id, 'and name: ', name);
    return true;
    // console.log('SENT IT', res.data);
  } catch (error: any) {
    console.log('Error sending message: ', error.response.data);
    console.log('Phone: ', phone, 'payment_id: ', payment_id, 'name: ', name);
  }

}

export async function sendSoldPaymentWAMessage(payment_id: string, phone: string){
  const soldTemplateId = '0680680f-dfce-7cae-8000-8ffa302111e0';
  try {
    await axios.post(`${HILOS_API}/api/channels/whatsapp/template/${soldTemplateId}/send`, {
      variables: [`${PAYMENT_FRONT_URL}/pago/${payment_id}`],
      phone: phone.split('+').join(''),
  }, {
    headers: {
      Authorization: `Token ${HILOS_API_KEY}`
    }
  });
  } catch (error: any) {
    console.log('Error sending message: ', error.response.data);
    console.log('Phone: ', phone, 'payment_id: ', payment_id);
  }
}


export async function sendPaymentWAMessageCronMX(name: string, payment_id: string, phone: string, amount?: number, template_id: string = change_CLABE_Template_Id){
  try {
    const isUnblock = amount && amount === 100;
    if (isUnblock) template_id = CHARGE_100_TEMPLATE_ID;
    const variables = isUnblock ? [payment_id] : [/* imgUrl, */ name, payment_id];
    await axios.post(`${HILOS_API}/api/channels/whatsapp/template/${template_id}/send`, {
      variables,
      phone: phone.split('+').join(''),
  }, {
    headers: {
      Authorization: `Token ${HILOS_API_KEY}`
    }
  });
  } catch (error: any) {
    throw error;
  }
}


export async function sendUnlockPaymentWAMessage(name: string, phone: string, weeklyPaymentId: string, unlockPaymentId: string, templateId : string){

  try {

    const variables = [name, weeklyPaymentId,unlockPaymentId];
    console.log('url request', `${HILOS_API}/api/channels/whatsapp/template/${templateId}/send`);
    /* const res =  */ await axios.post(`${HILOS_API}/api/channels/whatsapp/template/${templateId}/send`, {
      variables,
      phone: phone.split('+').join(''),
  }, {
    headers: {
      Authorization: `Token ${HILOS_API_KEY}`
    }
  });
    console.log('Message sent to: ', phone, 'with weeklyPaymentId: ', weeklyPaymentId,', unlockPaymentId: ',unlockPaymentId, 'and name: ', name);
    return true;
    // console.log('SENT IT', res.data);
  } catch (error: any) {
    console.log('Error sending message: ', error.response.data);
    console.log('Phone: ', phone,' weeklyPaymentId: ', weeklyPaymentId,', unlockPaymentId: ',unlockPaymentId, 'and name: ', name);
  }

}

export async function sendUnlockPaymentConfirmationWAMessage(name :string, phone: string){
  try {

    const template_id = REACTIVATION_PAYMENT_CONFIRMATION_TEMPLATE_ID;
    const variables = [name];

    console.log('url request', `${HILOS_API}/api/channels/whatsapp/template/${template_id}/send`);
    await axios.post(`${HILOS_API}/api/channels/whatsapp/template/${template_id}/send`, {
          variables,
          phone: phone.split('+').join(''),
      }, {
        headers: {
          Authorization: `Token ${HILOS_API_KEY}`
        }
      });
    return true;
  } catch (error: any) {
    console.log('Error sending message: ', error.response.data);
  }
}

export async function sendVehicleUnlockConfirmationWAMessage(name :string, phone: string){
  try {

    const template_id = VEHICLE_UNLOCK_CONFIRMATION_TEMPLATE_ID;
    const variables = [name];

    console.log('url request', `${HILOS_API}/api/channels/whatsapp/template/${template_id}/send`);
    await axios.post(`${HILOS_API}/api/channels/whatsapp/template/${template_id}/send`, {
          variables,
          phone: phone.split('+').join(''),
      }, {
        headers: {
          Authorization: `Token ${HILOS_API_KEY}`
        }
      });
    return true;
  } catch (error: any) {
    console.log('Error sending message: ', error.response.data);
  }
}