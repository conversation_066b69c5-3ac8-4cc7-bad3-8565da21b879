import { Injectable, Logger, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';

@Injectable()
export class RequestLoggerMiddleware implements NestMiddleware {
  private readonly logger = new Logger();

  use(req: Request, res: Response, next: NextFunction) {
    // const start = Date.now();
    const start = process.hrtime();
    res.on('finish', () => {
      // const elapsedTime = Date.now() - start;
      const elapsed = process.hrtime(start);
      const elapsedTimeInMs = elapsed[0] * 1000 + elapsed[1] / 1000000; // Convertir a milisegundos
      const elapsedTimeFormatted = elapsedTimeInMs.toFixed(3);
      const statusCode = res.statusCode;
      const correlationId = req.headers['x-correlation-id'];

      if (statusCode > 400 && statusCode < 500) {
        this.logger.warn({
          method: req.method,
          url: req.url,
          statusCode,
          elapsedTime: elapsedTimeFormatted,
          correlationId
        })

      } else if (statusCode === 400 || statusCode >= 500) {
        this.logger.error({
          method: req.method,
          url: req.url,
          statusCode,
          elapsedTime: elapsedTimeFormatted,
          correlationId
        })
      } else {
        this.logger.log({
          method: req.method,
          url: req.url,
          statusCode,
          elapsedTime: elapsedTimeFormatted,
          correlationId
        })
      }
    });

    next();
  }
}
