import { Controller, Get, Post, Body, Patch, Param, Delete, Res, Query, Logger } from '@nestjs/common';
import { SubscriptionsService } from '../services/susbscriptions.service';
import { CreateSubscriptionDto } from '../dto/create-subscription.dto';
import { UpdateSubscriptionDto, UpdateSubscriptionStatusDto } from '../dto/update-subscription.dto';
import { tryCatchResponse } from '@/lib/defaultResponse';
import { Response } from 'express';
import { ApiTags } from '@nestjs/swagger';
import { FindOneSubscriptionQueryDto } from '../dto/findOne-query.dto';
import { CreateSubscriptionPayments } from '../services/create-payments.service';
import { FindOperator, ILike, Like } from 'typeorm';
import { FindAllSubscriptionQueryDto } from '../dto/findAll.dto';
import { isUUID } from 'class-validator';
import { CreateSubscriptionPaymentDto, InitiatePayFlowDto } from '../dto/create-payment.dto';

@ApiTags('subscriptions')
@Controller('subscriptions')
export class SubscriptionsController {
  private readonly logger = new Logger(SubscriptionsController.name);
  constructor(
    private readonly suscriptionsService: SubscriptionsService,
    private readonly createSubscriptionPayments: CreateSubscriptionPayments,
  ) { }

  @Post()
  async create(@Body() body: CreateSubscriptionDto, @Res() res: Response) {
    return await tryCatchResponse(res, async () => this.suscriptionsService.create(body));
  }

  // @Post('create-payment')
  // create a controller to create a payment for each subscription

  @Post('initiate-pay-flow')
  async initiatePayFlow(@Body() createSuscriptionDto: InitiatePayFlowDto, @Res() res: Response) {
    return await tryCatchResponse(res, async () => this.createSubscriptionPayments.initiatePayFlow(createSuscriptionDto));
  }

  @Post('create-single-payment/:subscriptionId')
  async createSinglePayment(@Param('subscriptionId') subscriptionId: string, @Res() res: Response) {
    return await tryCatchResponse(res, async () => this.createSubscriptionPayments.createSinglePayment(subscriptionId));
  }

  @Get()
  async findAll(@Res() res: Response, @Query() query: FindAllSubscriptionQueryDto) {

    const clientQuery: FindAllSubscriptionQueryDto = {
      ...query,
      id: query.clientId
    }

    delete clientQuery.clientId

    const object = {
      where: []
    }

    if (query.search) {
      const orConditions: Array<{
        client: {
          email?: FindOperator<string>;
          contractNumber?: FindOperator<string>;
          id?: string;
        };
      }> = [
          { client: { email: ILike(`%${query.search}%`) } },
          { client: { contractNumber: Like(`%${query.search}%`) } },
        ]

      if (isUUID(query.search)) {
        orConditions.push({ client: { id: query.search } });
      }

      object.where = orConditions;
    } else {
      object.where = [{ client: clientQuery }];
    }

    return await tryCatchResponse(res, async () => this.suscriptionsService.findAll(object));
  }

  @Patch('status/:id')
  async updateStatus(@Param('id') id: string, @Body() { status }: UpdateSubscriptionStatusDto, @Res() res: Response) {
    return await tryCatchResponse(res, async () => this.suscriptionsService.updateStatus(id, status));
  }

  @Get(':id')
  async findOne(@Param('id') id: string, @Query() query: FindOneSubscriptionQueryDto, @Res() res: Response) {

    return await tryCatchResponse(res, async () => this.suscriptionsService.findOne(id));
  }

  @Patch(':id')
  async update(@Param('id') id: string, @Body() updateSuscriptionDto: UpdateSubscriptionDto, @Res() res: Response) {
    return await tryCatchResponse(res, async () => this.suscriptionsService.update(id, updateSuscriptionDto));
  }

  @Delete(':id')
  async remove(@Param('id') id: string, @Res() res: Response) {
    return await tryCatchResponse(res, async () => this.suscriptionsService.remove(id));
  }




}
