export interface AutoInvoiceEventData {
  id: string;
  created_at: string;
  livemode: boolean;
  organization: string;
  type: string;
  data: {
    type: string;
    object: {
      id: string;
      created_at: string;
      livemode: boolean;
      date: string;
      expires_at: string;
      status: string;
      self_invoice_url: string;
      total: number;
      invoice: string;
      key: string;
      items: Items[];
      external_id: string;
      idempotency_key: string;
      payment_form: string;
      folio_number: number;
      currency: string;
      exchange: number;
      branch: string;
    };
  };
}

interface Items {
  quantity: number;
  discount: number;
  product: any; // Tipo de datos del producto no especificado
  parts: {
    description: string;
    product_key: string;
    quantity: number;
    sku: string;
    unit_price: number;
    unit_name: string;
    customs_keys: string[];
  };
}