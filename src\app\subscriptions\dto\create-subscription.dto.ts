import { TaxFactorEnum, TaxFactorType, TaxTypesEnum, TaxTypesType } from '@/app/products/entities/product.entity';
import { RegionsEnum, RegionsType } from '@/types/entities.types';
import { Type } from 'class-transformer';
import { ArrayMinSize, ArrayNotEmpty, IsArray, IsBoolean, IsDateString, IsDefined, IsEnum, IsNumber, IsOptional, IsString, Matches, Max, Min, ValidateIf, ValidateNested } from 'class-validator';

export class ProductBody {
  @IsString()
  @Matches(/^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/, { message: 'Invalid UUID format' })
  id: string;

  @IsOptional()
  @IsNumber()
  @Min(1)
  quantity: number = 1;

  @IsNumber()
  @IsOptional()
  discount: number;

  @IsOptional()
  @IsString()
  product_key: string;

  @IsOptional()
  @IsString()
  unit_key: string;

  @IsOptional()
  @IsString()
  measurementUnit: string;

  @IsOptional()
  @IsBoolean()
  hasTaxes: boolean;

  @IsNumber({
    allowInfinity: false,
    allowNaN: false,
    maxDecimalPlaces: 2,
  })
  @Min(0.01)
  @Max(1, {
    message: 'The tax rate must be a value between 0.01 and 1, the percentage of the tax rate in decimal format. Example: 0.16 for 16% tax rate.'
  })
  @ValidateIf((object) => object.hasTaxes === true)
  taxRate?: number; // taxRate

  @IsString()
  @IsEnum(TaxFactorEnum)
  @ValidateIf((object) => object.hasTaxes === true)
  taxFactor?: TaxFactorType; // TaxFactor

  @IsString()
  @IsEnum(TaxTypesEnum)
  @ValidateIf((object) => object.hasTaxes === true)
  taxType?: TaxTypesType; // taxType

}

export class CreateSubscriptionDto {

  @IsString()
  @Matches(/^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/, { message: 'Invalid UUID format' })
  clientId: string;

  @IsArray()
  @ArrayNotEmpty({ message: 'At least one product is required' })
  @ArrayMinSize(1, { message: 'At least one product is required' })
  @IsDefined()
  @ValidateNested()
  @Type(() => ProductBody)
  public products: ProductBody[];


  @IsOptional()
  @IsEnum(RegionsEnum)
  region: RegionsType;

  @IsOptional()
  @IsDateString()
  startDate: string;

  @IsDefined()
  @IsDateString()
  endDate: string;

}
