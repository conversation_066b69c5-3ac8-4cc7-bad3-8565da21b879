import { ClientEntity } from '@/app/clients/entities/client.entity';
import { PaymentsEntity } from '@/app/payments/entities/payment.entity';
import { PaymentsService } from '@/app/payments/services/payments.service';
import { Wire4WebHookService } from '@/app/payments/services/wire4Webhook.service';
import facturapi from '@/facturapi';
import { Customer } from '@/facturapi/Customers';
import { PaymentFormType } from '@/facturapi/Invoices';
import { getFirstDayOfCurrentMonth, getLocalDate, getTimezoneDate } from '@/lib/dates';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
// import { addDays } from 'date-fns';
import { Between, IsNull, MoreThanOrEqual, Not, Raw, Repository } from 'typeorm';

@Injectable()
export class SchedulesService {

  constructor(
    @InjectRepository(PaymentsEntity) private readonly paymentRepository: Repository<PaymentsEntity>,
    private readonly wire4WebHook: Wire4WebHookService,
  ) { }

  async invoiceReceipts(body?: { isTest?: boolean }) {

    // const today = getLocalDate(undefined, {
    //   hours: 59,
    //   minutes: 59,
    // }); // today is the cron job date execution, always last day of the month at 23:59

    const today = getTimezoneDate({
      // fromIsoString: '2024-09-30', // testing
      hour: 23,
      minute: 59,
    })

    const firstDayOfMonth = getFirstDayOfCurrentMonth({
      // month: 9, // testing
    });

    console.log('today', today)
    console.log('firstDayOfMonth', firstDayOfMonth)

    const allPayments = await this.paymentRepository.find({
      where: [
        {
          isPaid: true,
          status: 'success',
          receiptId: Not(IsNull()),
          invoiceId: IsNull(),
          createdAt: Between(firstDayOfMonth, today),
          paidAt: Between(firstDayOfMonth, today),
        },
      ],
      relations: ['client', 'subscription.products'],
      select: {
        id: true,
        invoiceId: true,
        receiptId: true,
        products: true,
        total: true,
        client: {
          id: true,
          contractNumber: true,
          legal_name: true,
          rfc: true,
          zip: true,
          email: true,
          phone: true,
          region: true,
        },
        subscription: {
          id: true,
          products: true,
        },
        createdAt: true,
      },
    });

    if (body?.isTest) {
      return {
        message: 'CREATING INVOICE RECEIPTS',
        isTest: body.isTest,
        total: allPayments.length,
      }
    }

    // const fs = require('fs');
    // const path = require('path');

    // save all payments to a json file

    // const filePath = path.join(__dirname, 'allPayments.json');
    // const root = process.cwd();

    // const filePath = path.join(root, 'allPayments.json');

    // const data = [];
    // allPayments.forEach(payment => {
    //   console.log('payment', payment.id)
    //   console.log('client', payment.client.legal_name, payment.invoiceId, payment.receiptId)
    //   console.log('-----------------------------------')

    //   const howManyPaymentsForThisClient = allPayments.filter(p => p.client.id === payment.client.id).length;

    //   const obj = {
    //     id: payment.id,
    //     contractNumber: payment.client.contractNumber,
    //     clientName: payment.client.legal_name,
    //     clientId: payment.client.id,
    //     clientEmail: payment.client.email,
    //     invoiceId: payment.invoiceId,
    //     receiptId: payment.receiptId,
    //     total: payment.total,
    //   }

    //   data.push(obj);
    // });

    // fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf-8');

    // return {
    //   message: 'CREATING INVOICE RECEIPTS',
    //   total: allPayments.length,
    // }

    let count = 0;

    await Promise.allSettled(allPayments.map(async payment => {

      console.log('payment', payment.id)
      console.log('client', payment.client.legal_name, payment.client.id)
      console.log('-----------------------------------')
      const res = await this.createInvoice(payment);
      if (res) {
        // count++;
        const invoiceId = res.id;
        await this.paymentRepository.update(payment.id, { invoiceId });
      }
    }));

    return {
      message: `${count} Invoices created successfully`,
      total: count,
    }
  }

  async createInvoice(payment: PaymentsEntity) {

    const client = payment.client;

    const customer: Customer = {
      legal_name: client.legal_name,
      tax_system: '616',
      tax_id: 'XAXX010101000',
      address: {
        zip: '11320',
      },
    }

    try {

      const receipt = await facturapi.receipts.retrieve(payment.receiptId);

      console.log('receipt', receipt.status);

      if (receipt.status === 'invoiced_to_customer') {
        console.log('receipt already invoiced')
        // await facturapi.receipts.cancel(payment.receiptId);
        return {
          id: receipt.invoice,
        }
      }
    
    

      if (receipt.status === 'open') {

        const response = await facturapi.receipts.invoice(payment.receiptId, {
          customer,
          use: 'S01',
          series: client.region,
        });
        return response;
      }
    } catch (error) {
      console.log('error', error);
    }

  }


  // async createInvoice(payment: PaymentsEntity) {

  //   const client = payment.client;

  //   const paymentHasSubscription = Boolean(payment.subscription);

  //   try {
  //     const items = paymentHasSubscription ?
  //       await this.wire4WebHook.createItemsOnSubscription(payment) :
  //       await this.wire4WebHook.createItemsForNonSubscription(payment);

  //     const customer = {
  //       legal_name: client.legal_name,
  //       tax_system: '616',
  //       tax_id: 'XAXX010101000',
  //       address: {
  //         zip: '11320',
  //       },
  //     }

  //     const createInvoice = await facturapi.invoices.create({
  //       customer,
  //       items,
  //       payment_form: '03',
  //       use: 'S01',
  //       series: payment.client.region,
  //     });

  //     payment.invoiceId = createInvoice.id;

  //     await facturapi.invoices.sendInvoiceByEmail(createInvoice.id, {
  //       email: client.email,
  //     });

  //     await this.paymentRepository.update(payment.id, payment);
  //     // await this.paymentRepository.save(payment);

  //     return 1;
  //   } catch (error) {
  //     console.log('error', error);
  //   }

  // }


}