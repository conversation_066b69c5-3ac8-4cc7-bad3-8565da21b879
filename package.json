{"name": "payments", "version": "0.0.1", "description": "", "author": "", "private": true, "main": "dist/main.js", "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "node build/main.js", "dev": "ts-node-dev -r tsconfig-paths/register ./src/main.ts", "start:dev": "nest start  --watch --preserveWatchOutput", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "migration:postgres": "npx prisma migrate dev --schema ./prisma/postgres_schema.prisma", "migration:mongo": "npx prisma migrate dev --schema ./prisma/mongo_schema.prisma", "generate:postgres": "npx prisma generate --schema ./prisma/postgres_schema.prisma", "generate:mongo": "npx prisma generate --schema ./prisma/mongo_schema.prisma", "studio:postgres": "npx prisma studio --schema ./prisma/postgres_schema.prisma", "studio:mongo": "npx prisma studio --schema ./prisma/mongo_schema.prisma", "push:postgres": "npx prisma db push --schema ./prisma/postgres_schema.prisma", "push:mongo": "npx prisma db push --schema ./prisma/mongo_schema.prisma", "pull:postgres": "npx prisma db pull --schema ./prisma/postgres_schema.prisma", "pull:mongo": "npx prisma db pull --schema ./prisma/mongo_schema.prisma --force", "watch:postgres": "npx prisma generate --schema ./prisma/postgres_schema.prisma --watch", "watch:mongo": "npx prisma generate --schema ./prisma/mongo_schema.prisma --watch", "uninstallDevDependencies": "npm uninstall --save-dev @nestjs/cli @nestjs/schematics @nestjs/testing @types/express @types/jest @types/node @types/supertest @typescript-eslint/eslint-plugin @typescript-eslint/parser eslint eslint-config-prettier eslint-plugin-prettier jest prettier ts-jest ts-loader ts-node-dev tsconfig-paths"}, "dependencies": {"@aws-sdk/client-s3": "^3.782.0", "@aws-sdk/s3-request-presigner": "^3.782.0", "@fastify/cors": "^11.0.1", "@fastify/static": "^8.1.1", "@bull-board/api": "6.x", "@bull-board/fastify": "6.x", "@bull-board/nestjs": "6.x", "@nestjs/axios": "^4.0.0", "@nestjs/bullmq": "^11.0.2", "@nestjs/cache-manager": "^3.0.1", "@nestjs/common": "^11.0.14", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.14", "@nestjs/jwt": "^11.0.0", "@nestjs/mongoose": "^11.0.3", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.14", "@nestjs/platform-fastify": "^11.0.14", "@nestjs/schedule": "^5.0.1", "@nestjs/swagger": "^11.1.1", "@nestjs/terminus": "^11.0.0", "@nestjs/typeorm": "^11.0.0", "@prisma/client": "6.6.0", "@react-email/components": "^0.0.36", "@slack/web-api": "^7.9.1", "@types/nodemailer": "^6.4.17", "@types/react": "^19.1.0", "axios": "^1.8.4", "bcrypt": "^5.1.1", "bullmq": "^5.48.0", "cache-manager": "^6.4.2", "class-transformer": "0.5.1", "class-validator": "0.14.1", "date-fns": "4.1.0", "dotenv": "16.4.7", "firebase-admin": "^13.2.0", "ioredis": "^5.6.0", "json2csv": "^6.0.0-alpha.2", "mongodb": "^6.15.0", "mongoose": "^8.13.2", "nest-winston": "1.10.2", "nodemailer": "^6.10.0", "passport-jwt": "^4.0.1", "pg": "^8.14.1", "react": "^19.1.0", "reflect-metadata": "0.2.2", "rxjs": "7.8.2", "stripe": "^18.0.0", "typeorm": "0.3.22", "uuid": "11.1.0", "winston": "3.17.0", "winston-loki": "^6.1.3", "winston-slack-webhook-transport": "2.3.6"}, "devDependencies": {"@nestjs/cli": "^11.0.6", "@nestjs/schematics": "^11.0.4", "@nestjs/testing": "^11.0.14", "@types/express": "^5.0.1", "@types/ioredis": "^5.0.0", "@types/jest": "^29.5.14", "@types/json2csv": "^5.0.7", "@types/node": "^22.14.0", "@types/supertest": "^6.0.3", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.29.1", "@typescript-eslint/parser": "^8.29.1", "eslint": "^9.24.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-prettier": "^5.2.6", "jest": "^29.7.0", "prettier": "^3.5.3", "prisma": "^6.6.0", "source-map-support": "^0.5.21", "supertest": "^7.1.0", "ts-jest": "^29.3.1", "ts-loader": "^9.5.2", "ts-node-dev": "^2.0.0", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}