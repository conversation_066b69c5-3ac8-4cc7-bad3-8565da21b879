import {
  Body,
  Button,
  Container,
  Hr,
  Section,
  Text,
  Tailwind,
  render,
} from '@react-email/components';
import * as React from 'react';
import { sendEmail } from '..';
import { PAYMENT_FRONT_URL } from '@/constants';

interface KoalaWelcomeEmailProps {
  name: string;
  url: string;
  bodyText: string;
  subBodyText: string;
}

export const KoalaWelcomeEmail = ({
  name,
  url,
  bodyText,
  subBodyText,
}: KoalaWelcomeEmailProps) => (
  <Tailwind>
    {/* <Head /> */}
    <Body style={main}>
      <Container style={container}>
        <Text style={paragraph}>Hi {name}</Text>

        <>
          <Text style={paragraph}>{bodyText}</Text>
          <div className=" flex gap-2 items-center ">
            <Text style={{ ...paragraph }} className=" my-2 mx-0">
              {subBodyText}
              <span className="align-text-bottom ">🚗</span>
            </Text>
          </div>
        </>

        <Section style={btnContainer}>
          <Button style={button} href={url}>
            {"Receipt"}
          </Button>
        </Section>
        <Text style={paragraph}>
          Greetings,
          <br />
          OneCarNow! team
        </Text>
        <Hr style={hr} />
        <Text style={footer}>
          © {new Date().getFullYear()} OneCarNow! All rights
        </Text>
      </Container>
    </Body>
  </Tailwind>
);

const main = {
  backgroundColor: '#ffffff',
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif',
};

const container = {
  margin: '0 auto',
  padding: '20px 0 48px',
};

const logo = {
  margin: '0 auto',
};

const paragraph = {
  fontSize: '16px',
  lineHeight: '26px',
};

const btnContainer = {
  textAlign: 'center' as const,
};

const button = {
  backgroundColor: '#5F51E8',
  borderRadius: '3px',
  color: '#fff',
  fontSize: '16px',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'block',
  padding: '12px',
};

const hr = {
  borderColor: '#cccccc',
  margin: '20px 0',
};

const footer = {
  color: '#8898aa',
  fontSize: '12px',
};

interface IReceiptAfterPaymentLinkEmail {
  name: string;
  email: string;
  paymentId: string;
}

export default async function stripeReceiptLinkAfterPaymentEmail(
  data: IReceiptAfterPaymentLinkEmail,
) {
  const url = `${PAYMENT_FRONT_URL}/payment-receipt/${data.paymentId}`;
  const bodyText =
    'We are pleased to inform you that your recent payment has been successfully debited from your account. For further details about this transaction, please visit the following link:';
  const subBodyText =
    'Thank you for your payment! If you have any questions or need further assistance, please feel free to reach out to us. Please do not share this link with anyone, as it contains sensitive information about your transaction.';
  const subject =
    'Payment Confirmation: Your Recent Payment Has Been Successfully Debited';

  const html = await render(
    KoalaWelcomeEmail({ name: data.name, url, bodyText, subBodyText }),
  );
  await sendEmail(data.email, subject, html);
}
