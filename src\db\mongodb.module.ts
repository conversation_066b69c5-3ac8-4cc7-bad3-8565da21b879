import { Module } from '@nestjs/common';
import { config } from 'dotenv';
import { MongoClient, Db } from 'mongodb';
import { ConfigModule, ConfigService } from '@nestjs/config';

@Module({
  imports: [ConfigModule],
  providers: [
    {
      provide: 'MONGODB_CONNECTION',
      useFactory: async (configService: ConfigService): Promise<Db> => {
        try {
          const client = await MongoClient.connect(process.env.MONGODB_URI);

          return client.db('test');
        } catch (e) {
          throw e;
        }
      }
    },
  ],
  exports: ['MONGODB_CONNECTION'],
})
export class MongoDbModule {}