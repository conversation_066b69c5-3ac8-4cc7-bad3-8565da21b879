import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedC<PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  OneToOne,
  JoinColumn,
  OneToMany,
} from 'typeorm';
import { StripeSetupIntentEntity } from './stripe-setup-intent.entity';
import { ClientEntity } from '@/app/clients/entities/client.entity';

@Entity('stripe_customer_info')
export class StripeCustomerInfoEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({
    type: 'varchar',
  })
  stripeCustomerId: string;

  @Column({
    type: 'boolean',
    default: false,
  })
  isCustomerBankAccountLinked: boolean;

  @OneToOne(() => ClientEntity, (client) => client.stripeCustomer)
  @JoinColumn()
  client: ClientEntity

  @OneToMany(
    () => StripeSetupIntentEntity,
    (stripeSetupIntent) => stripeSetupIntent.stripeCustomerInfo,
    {
      nullable: true,
    }
  )
  stripeSetupIntent: StripeSetupIntentEntity[];



  
  @CreateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  createdAt: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  updatedAt: Date;

  @Column({
    type: 'timestamp',
    nullable: true,
  })
  deletedAt: Date;
}
