generator client {
  provider      = "prisma-client-js"
  output        = "../node_modules/@prisma/postgres"
  binaryTargets = [env("PRISMA_BINARY_TARGETS")]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Associates {
  id        String   @id
  model     String
  createdAt DateTime @default(now())
}

model clients {
  id             String              @id(map: "PK_f1ab7cf3a5714dbc6bb4e1c28a4") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  contractNumber String?
  name           String
  lastName       String?             @db.VarChar
  email          String?
  phone          String?
  fiscalRegime   String?
  cfdiUse        String?
  rfc            String?
  street         String?
  monexClabe     String?
  country        String              @default("MX")
  legal_name     String?
  zip            String?
  region         clients_region_enum
  gigId          String?
  isActive       Boolean             @default(false)
  createdAt      DateTime            @default(now()) @db.Timestamp(6)
  payments       payments[]
  subscriptions  subscriptions?
}

model products {
  id              String                 @id(map: "PK_0806c755e0aca124e67c0cf6d7d") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  name            String
  description     String?
  price           Float
  product_key     String                 @default("01010101")
  unit_key        String                 @default("E48")
  taxRate         Float                  @default(0.16)
  taxFactor       String?                @default("Tasa")
  taxType         products_taxtype_enum? @default(IVA)
  withHolding     String?                @default(dbgenerated("false"))
  ivaIncluded     Boolean                @default(true)
  createdAt       DateTime               @default(now()) @db.Timestamp(6)
  measurementUnit String?                @default("Unidad de servicio")
  region          products_region_enum
  subTotal        Float?
  tax             Float?                 @default(0)
}

model subscriptions {
  id                    String                    @id(map: "PK_a87248d73155605cf782be9ee5e") @db.VarChar
  paymentNumber         Int                       @default(1)
  startDate             DateTime?                 @db.Timestamp(6)
  endDate               DateTime?                 @db.Timestamp(6)
  region                subscriptions_region_enum
  createdAt             DateTime                  @default(now()) @db.Timestamp(6)
  updatedAt             DateTime                  @default(now()) @db.Timestamp(6)
  clientId              String?                   @unique(map: "UQ_8dd67476f865da4c65177b3c69a") @db.Uuid
  isActive              Boolean                   @default(true)
  total                 Float?
  subTotal              Float?
  tax                   Float?
  subscription_products subscription_products[]
  clients               clients?                  @relation(fields: [clientId], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_8dd67476f865da4c65177b3c69a")
}

model subscription_products {
  id              String                              @id(map: "PK_739a1a9b9cdb56f57460239dbe8") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  name            String
  description     String?
  price           Float
  product_key     String                              @default("01010101")
  unit_key        String                              @default("E48")
  measurementUnit String?                             @default("Unidad de servicio")
  taxRate         Float                               @default(0.16)
  taxFactor       String?                             @default("Tasa")
  taxType         subscription_products_taxtype_enum? @default(IVA)
  withHolding     String?                             @default(dbgenerated("false"))
  ivaIncluded     Boolean                             @default(true)
  region          subscription_products_region_enum
  createdAt       DateTime                            @default(now()) @db.Timestamp(6)
  quantity        Int                                 @default(1)
  discount        Float                               @default(0)
  total           Float
  subscriptionId  String?                             @db.VarChar
  subTotal        Float?
  tax             Float?                              @default(0)
  subscriptions   subscriptions?                      @relation(fields: [subscriptionId], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_f64beaebf31a2b88f2bb567a35a")
}

model payments {
  id        String               @id(map: "PK_197ab7af18c93fbb0c9b28b4a59") @db.VarChar
  isPaid    Boolean              @default(false)
  createdAt DateTime             @default(now()) @db.Timestamp(6)
  updatedAt DateTime             @default(now()) @db.Timestamp(6)
  clientId  String?              @db.Uuid
  amount    Float
  status    payments_status_enum @default(pending)
  clients   clients?             @relation(fields: [clientId], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_e7c2e95ccd4bd2068c70744dd65")
}

enum Clients_region_enum {
  CDMX
  GDL
  MTY
  TIJ
  QRO
}

enum clients_region_enum {
  CDMX
  GDL
  MTY
  TIJ
  QRO
  TEST
}

enum products_taxtype_enum {
  IVA
  IEPS
  ISR
}

enum products_region_enum {
  CDMX
  GDL
  MTY
  TIJ
  QRO
  TEST
}

enum subscriptions_region_enum {
  CDMX
  GDL
  MTY
  TIJ
  QRO
  TEST
}

enum subscription_products_region_enum {
  CDMX
  GDL
  MTY
  TIJ
  QRO
  TEST
}

enum subscription_products_taxtype_enum {
  IVA
  IEPS
  ISR
}

enum suscription_products_region_enum {
  CDMX
  GDL
  MTY
  TIJ
  QRO
  TEST
}

enum suscription_products_taxtype_enum {
  IVA
  IEPS
  ISR
}

enum payments_status_enum {
  pending
  success
  failed
}
