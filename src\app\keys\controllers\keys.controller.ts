import { JwtAuthGuard } from '@/app/jwt/guard/jwt.guard';
import { Controller, UseGuards } from "@nestjs/common";
import { KeysService } from '../services/keys.service';
import { Get, Post, Req, Res } from '@nestjs/common';
import { Request, Response } from 'express';
import { tryCatchResponse } from '@/lib/defaultResponse';
import { PublicRoute } from '../guards/public.decorator';

// @PublicRoute()
// @ApplyPublicRouteToAllMethods()
@Controller('keys')
@UseGuards(JwtAuthGuard)
export class KeysController {

  constructor(
    private readonly keysService: KeysService
  ) { }

  @PublicRoute()
  @Post()
  async createKey(@Req() req: Request, @Res() res: Response) {

    const user = req['user'];

    return await tryCatchResponse(res, () => this.keysService.createKey(user.id));
  }

  @PublicRoute()
  @Get()
  async getKeys(@Res() res: Response) {
    return await tryCatchResponse(res, () => this.keysService.getKeys());
  }

}
