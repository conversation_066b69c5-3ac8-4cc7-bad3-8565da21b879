import { BullModule } from '@nestjs/bullmq';
import { Module } from '@nestjs/common';
import { WhatsappQueueService } from './whatsapp-queue.service';
import { WhatsappProcessor } from './whatsapp-queue.processor';
import { BullMQAdapter } from '@bull-board/api/bullMQAdapter';
import { BullBoardModule } from '@bull-board/nestjs';
import { WHATSAPP_QUEUE } from '@/constants';

@Module({
  imports: [
    BullModule.registerQueue({
      name: WHATSAPP_QUEUE,
    }),
    BullBoardModule.forFeature({
      name: WHATSAPP_QUEUE, // Register the queue with Bull Board
      adapter: BullMQAdapter,
    }),
  ],
  providers: [WhatsappQueueService, WhatsappProcessor],
  exports: [WhatsappQueueService],
})
export class WhatsappQueueModule {}
