import { Controller, Get, Req, UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../guard/jwt.guard';
import { PublicRoute } from '@/app/keys/guards/public.decorator';

@Controller('auth')
export class AuthController {
  constructor() { }

  @Get()
  getHello() {
    return 'Hello World!';
  }

  @PublicRoute()
  @UseGuards(JwtAuthGuard)
  @Get('test')
  test(@Req() req: Request) {
    const user = req['user'];
    // console.log('user', user);
    return 'test';
  }

}
